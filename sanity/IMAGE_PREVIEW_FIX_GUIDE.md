# Sanity Studio 图片预览修复指南

## 问题描述
在 Sanity Studio 产品管理页面中，当点击产品标签查看产品列表时，右侧面板的产品图片无法正常显示。

## 根本原因分析

1. **复杂的图片架构**：项目使用了 `flexibleImage` 模式，支持上传和外部图片
2. **预览配置问题**：产品预览的 `media` 字段处理不够健壮
3. **错误处理不足**：缺乏针对图片加载失败的错误边界

## 修复内容

### 1. 更新产品预览逻辑 (`sanity/schemas/product.ts`)

**修复前**：
```typescript
preview: {
  select: {
    title: 'name.zh',
    subtitle: 'category.name.zh',
    media: 'gallery',  // Use first image from gallery
    // ...
  }
}
```

**修复后**：
```typescript
preview: {
  select: {
    title: 'name.zh',
    subtitle: 'category.name.zh',
    gallery: 'gallery',  // Select complete gallery array
    // ...
  },
  prepare({title, subtitle, gallery, published, stockStatus}) {
    // 智能处理不同类型的图片
    let media = undefined
    
    const mainImage = gallery?.[0]\n    if (mainImage) {
      // 处理旧版格式（直接图片）
      if (mainImage.asset) {
        media = mainImage
      }
      // 处理新的灵活图片格式
      else if (mainImage.imageType === 'upload' && mainImage.uploadedImage) {
        media = mainImage.uploadedImage
      }
      else if (mainImage.imageType === 'external' && mainImage.externalUrl) {
        media = {
          url: mainImage.externalUrl,
          alt: mainImage.alt || '外部图片'
        }
      }
    }
    // ...
  }
}
```

### 2. 改进 flexibleImage 预览 (`sanity/schemas/components/flexibleImage.tsx`)

- 添加了对外部图片的更好预览支持
- 改进了错误处理逻辑
- 加入了 URL 验证和异常捕获

### 3. 增强外部图片预览组件 (`sanity/schemas/components/ExternalImagePreview.tsx`)

**新增功能**：
- 10秒加载超时机制
- CORS 头处理 (`crossOrigin = 'anonymous'`)
- 重试加载按钮
- 更详细的错误信息

### 4. 添加错误边界组件 (`sanity/schemas/components/ImageErrorBoundary.tsx`)

新建的错误边界组件可以：
- 捕获图片组件的渲染错误
- 提供友好的错误提示
- 支持重试功能

## 验证步骤

### 1. 启动 Sanity Studio
```bash
cd sanity
npm run dev
```
访问 http://localhost:3333

### 2. 测试图片显示
1. 进入"产品管理"部分
2. 点击任一产品标签（如"📦 现货商品"）
3. 检查右侧产品列表是否显示图片
4. 尝试点击不同的过滤器标签

### 3. 检查不同图片类型
- **上传图片**：应该正常显示
- **外部图片**：应该显示或显示错误信息

### 4. 运行测试脚本
```bash
# 测试基本图片加载
node scripts/test-image-preview.js

# 测试过滤器功能
node scripts/test-filter-functionality.js
```

## 测试结果

根据测试脚本的结果：
- ✅ 找到 10 个产品，全部都有图片
- ✅ 所有产品都使用上传的图片格式
- ✅ 所有图片都有有效的 CDN URL
- ✅ 分类浏览功能正常
- ✅ 各种过滤器都能正确返回产品

## 浏览器测试清单

当您在浏览器中测试时，请检查：

1. **打开开发者工具**（F12）
2. **控制台标签页**：查看是否有JavaScript错误
3. **网络标签页**：检查图片请求是否成功
4. **图片显示**：确认产品列表中的图片正常显示

### 常见问题排查

#### 图片仍然不显示？
1. 检查浏览器控制台是否有错误
2. 在网络标签页查看是否有失败的请求
3. 确认网络连接正常
4. 清除浏览器缓存后重试

#### 外部图片不显示？
1. 外部图片可能有 CORS 限制
2. 检查图片 URL 是否有效
3. 验证图片域名是否在白名单中

#### 性能问题？
1. 图片过大可能导致加载缓慢
2. 考虑使用 Sanity 的图片变换功能
3. 检查网络连接速度

## 长期改进建议

1. **图片优化**：使用 Sanity 的图片变换 API 生成缩略图
2. **缓存策略**：实现图片预加载和缓存机制
3. **懒加载**：对于大量产品的列表实现懒加载
4. **监控**：添加图片加载失败的监控和报告

## 技术细节

### 支持的图片类型
- **upload**：上传到 Sanity CDN 的图片
- **external**：外部图片链接（主要支持 Unsplash）

### 预览机制
- 产品列表使用 `preview` 配置生成缩略图
- `media` 字段决定显示的图片
- 智能处理不同图片类型（上传、外部）

### 错误处理层级
1. **组件级别**：ErrorBoundary 捕获渲染错误
2. **网络级别**：超时和重试机制
3. **数据级别**：类型检查和验证