{"name": "ani-globe-cms", "private": true, "version": "1.0.0", "main": "package.json", "scripts": {"dev": "sanity dev", "start": "sanity start", "build": "sanity build", "deploy": "sanity deploy", "deploy-graphql": "sanity graphql deploy", "migrate:main-image": "sanity exec scripts/migrate-main-image-to-gallery.js --with-user-token", "migrate:block-content": "sanity exec scripts/migrateToBlockContent.ts --with-user-token", "check:validation": "sanity exec scripts/checkValidationErrors.ts --with-user-token", "check:about": "sanity exec scripts/checkAboutPageValidation.ts --with-user-token", "init:about": "sanity exec scripts/initializeAboutPage.ts --with-user-token", "fix:fontsize": "sanity exec scripts/fixFontSizeAnnotations.ts --with-user-token", "check:modular": "sanity exec scripts/checkModularMigrationStatus.ts --with-user-token", "migrate:to-modular": "sanity exec scripts/migrateToModular.ts --with-user-token", "migrate:to-modular:dry": "sanity exec scripts/migrateToModular.ts --with-user-token -- --dry-run", "clean:fontsize": "sanity exec scripts/removeFontSizeFromAboutPage.ts --with-user-token", "migrate:homepage-v2": "sanity exec scripts/migrateHomepageToV2.ts --with-user-token", "migrate:homepage-v2:dry": "sanity exec scripts/migrateHomepageToV2.ts --with-user-token -- --dry-run", "migrate:homepage-subpages": "sanity exec scripts/migrateHomepageToSubpages.ts --with-user-token", "migrate:homepage-subpages:dry": "sanity exec scripts/migrateHomepageToSubpages.ts --with-user-token -- --dry-run", "cleanup:homepage": "sanity exec scripts/cleanupHomepageDocuments.ts --with-user-token"}, "keywords": ["sanity"], "dependencies": {"@sanity/color-input": "^4.0.6", "@sanity/icons": "^3.7.4", "@sanity/ui": "^3.0.7", "@sanity/vision": "^3.98.1", "react": "^18.3.1", "react-dom": "^18.3.1", "sanity": "^4.4.1", "styled-components": "^6.1.15"}, "devDependencies": {"@sanity/client": "^7.6.0", "@sanity/eslint-config-studio": "^4.0.0", "dotenv": "^17.2.0", "eslint": "^8.57.1", "nanoid": "^5.1.5", "ora": "^8.2.0", "p-map": "^7.0.3", "p-retry": "^6.2.1", "prettier": "^3.0.0", "typescript": "^5.1.6"}, "prettier": {"semi": false, "printWidth": 100, "bracketSpacing": false, "singleQuote": true}}