# 首页配置Tab结构改造说明

## 改造完成内容

### 1. 新的Tab结构设计
已成功将首页配置重新组织为6个清晰的Tab页面：

- **🏠 基础设置** - 页面标题、主标题、副标题、描述、CTA按钮
- **⭐ 精选产品** - 整合了原独立的精选产品配置
- **✨ 特色功能** - 功能卡片列表和布局设置
- **📖 品牌故事** - 故事内容和相关产品展示
- **📊 数据展示** - 统计数据配置
- **🔍 SEO设置** - 搜索引擎优化配置

### 2. 主要改进
- ✅ **更清晰的组织结构**：相关配置按功能分组到对应Tab
- ✅ **更好的编辑体验**：每个Tab可以折叠/展开，减少页面杂乱
- ✅ **统一管理**：精选产品功能整合到首页配置中
- ✅ **保持兼容性**：所有字段名称保持不变，确保前端代码兼容

## 使用步骤

### 1. 访问新的首页配置
1. 打开 Sanity Studio (http://localhost:3333)
2. 在左侧菜单中点击 "🏠 首页管理"
3. 你将看到新的Tab结构界面

### 2. 数据迁移
运行以下命令将现有数据迁移到新结构：

```bash
# 进入sanity目录
cd sanity

# 先运行干跑模式查看将要迁移的内容
npm run migrate:homepage-v2:dry

# 确认无误后执行实际迁移
npm run migrate:homepage-v2
```

### 3. 前端代码更新
迁移完成后，需要更新前端查询：

```typescript
// 原查询
const homepage = await sanityClient.fetch(`*[_type == "homepage"][0]`)
const featuredProducts = await sanityClient.fetch(`*[_type == "featuredProducts"][0]`)

// 新查询 - 精选产品已整合到首页配置中
const homepage = await sanityClient.fetch(`*[_type == "homepageV2"][0]`)
// featuredProducts数据现在在 homepage.featuredProductsSection 中
```

## Tab界面使用说明

### 基础设置Tab
- 包含原heroSection的所有配置
- 标题显示模式切换（文本/图片）
- 渐变效果配置（预设或自定义）

### 精选产品Tab
- **显示控制**：开关控制是否显示该区域
- **产品选择**：可选择3-8个精选产品
- **显示设置**：价格、评分、分类、标识的显示控制
- **布局设置**：网格、轮播、列表三种展示模式

### 特色功能Tab
- 支持1-6个功能卡片
- 每个卡片可配置图标、标题、描述、链接
- 多种布局样式可选

### 品牌故事Tab
- 富文本编辑器支持
- 可选背景图片
- 可关联2-4个相关产品展示

### 数据展示Tab
- 支持1-8个统计数据项
- 每项可配置数字、标签、颜色、图标
- 三种布局方式：水平、网格、卡片

### SEO设置Tab
- 独立的SEO配置区域
- Meta标签、Open Graph等设置

## 注意事项

1. **保留旧配置**：原homepage schema暂时保留，可作为备份
2. **测试验证**：在正式切换前请充分测试新界面
3. **逐步迁移**：建议先在staging环境测试，确认无误后再更新production
4. **前端更新**：记得同步更新前端代码中的查询和类型定义

## 后续优化建议

1. 完成测试后可以删除原homepage schema和featuredProducts schema
2. 考虑添加更多交互式预览功能
3. 可以进一步细化每个Tab的权限控制

## 技术细节

- 使用Sanity的`fieldsets`功能实现Tab结构
- 每个fieldset配置了`collapsible`和`collapsed`选项
- 保持字段名称向后兼容，减少前端改动
- 整合了featuredProducts文档类型到主配置中