#!/usr/bin/env node
/**
 * check-image-migration-status.js
 *
 * Analyzes the current status of image migration across all document types
 * Shows detailed breakdown of what needs to be migrated
 * 
 * Usage:
 *   node scripts/check-image-migration-status.js
 *   node scripts/check-image-migration-status.js --verbose  # Show detailed document list
 */

require('dotenv').config()
const {createClient} = require('@sanity/client')

// Simple loading spinner replacement
const spinner = {
  start: (text) => { console.log(text) },
  succeed: (text) => { console.log('✅', text) },
  fail: (text) => { console.log('❌', text) }
}

const isVerbose = process.argv.includes('--verbose')

console.log('🔍 Image Migration Status Check')
console.log('===============================')
console.log()

/* --- Sanity Client -------------------------------------------------- */
const client = createClient({
  projectId : process.env.SANITY_STUDIO_PROJECT_ID || '4za4x22i',
  dataset   : process.env.SANITY_STUDIO_DATASET     || 'production',
  token     : process.env.SANITY_WRITE_TOKEN || process.env.SANITY_READ_TOKEN,
  apiVersion: new Date().toISOString().slice(0,10),   // YYYY-MM-DD
  useCdn    : false
})

/* --- 检查图片格式 --------------------------------------------------- */
function analyzeImage(image, fieldName = 'image') {
  if (!image) return { status: 'missing', issues: [`${fieldName} is null/undefined`] }
  
  const issues = []
  
  // Check if it's old format (direct reference or direct asset)
  if (image._ref || (image.asset && !image.imageType)) {
    return { 
      status: 'old_format', 
      issues: [`${fieldName} uses old direct reference format`] 
    }
  }
  
  // Check if it's incomplete flexibleImage
  if (image._type === 'flexibleImage') {
    if (!image.imageType) {
      issues.push(`${fieldName}.imageType is missing`)
    }
    if (typeof image.alt === 'undefined') {
      issues.push(`${fieldName}.alt is missing`)
    }
    if (typeof image.caption === 'undefined') {
      issues.push(`${fieldName}.caption is missing`)
    }
    
    if (image.imageType === 'upload' && !image.uploadedImage) {
      issues.push(`${fieldName}.uploadedImage is missing for upload type`)
    }
    if (image.imageType === 'external' && !image.externalUrl) {
      issues.push(`${fieldName}.externalUrl is missing for external type`)
    }
    
    return {
      status: issues.length > 0 ? 'incomplete' : 'migrated',
      issues
    }
  }
  
  // Check if it's a regular image object (but not already flexibleImage)
  if (image._type === 'image' || (image.asset && !image.imageType)) {
    return { 
      status: 'old_format', 
      issues: [`${fieldName} uses old image object format`] 
    }
  }
  
  return { 
    status: 'unknown', 
    issues: [`${fieldName} has unknown format`] 
  }
}

/* --- 分析单个文档 --------------------------------------------------- */
function analyzeDocument(doc) {
  const analysis = {
    _id: doc._id,
    _type: doc._type,
    status: 'migrated',
    issues: [],
    imageFields: {}
  }
  
  // Product analysis
  if (doc._type === 'product') {
    // Check gallery (mainImage is now gallery[0])
    if (doc.gallery && Array.isArray(doc.gallery)) {
      for (let i = 0; i < doc.gallery.length; i++) {
        const result = analyzeImage(doc.gallery[i], `gallery[${i}]`)
        analysis.imageFields[`gallery[${i}]`] = result
        if (result.status !== 'migrated') {
          analysis.status = result.status
          analysis.issues.push(...result.issues)
        }
      }
    }
  }
  
  // Category analysis
  if (doc._type === 'category' && doc.image) {
    const result = analyzeImage(doc.image, 'image')
    analysis.imageFields.image = result
    if (result.status !== 'migrated') {
      analysis.status = result.status
      analysis.issues.push(...result.issues)
    }
  }
  
  // IP Series analysis
  if (doc._type === 'ipSeries') {
    if (doc.logo) {
      const result = analyzeImage(doc.logo, 'logo')
      analysis.imageFields.logo = result
      if (result.status !== 'migrated') {
        analysis.status = result.status
        analysis.issues.push(...result.issues)
      }
    }
    
    if (doc.bannerImage) {
      const result = analyzeImage(doc.bannerImage, 'bannerImage')
      analysis.imageFields.bannerImage = result
      if (result.status !== 'migrated') {
        analysis.status = result.status
        analysis.issues.push(...result.issues)
      }
    }
  }
  
  // Homepage analysis
  if (doc._type === 'homepage' && doc.brandStorySection?.backgroundImage) {
    const result = analyzeImage(doc.brandStorySection.backgroundImage, 'brandStorySection.backgroundImage')
    analysis.imageFields['brandStorySection.backgroundImage'] = result
    if (result.status !== 'migrated') {
      analysis.status = result.status
      analysis.issues.push(...result.issues)
    }
  }
  
  // Site Settings analysis
  if (doc._type === 'siteSettings') {
    if (doc.logo) {
      const result = analyzeImage(doc.logo, 'logo')
      analysis.imageFields.logo = result
      if (result.status !== 'migrated') {
        analysis.status = result.status
        analysis.issues.push(...result.issues)
      }
    }
    
    if (doc.favicon) {
      const result = analyzeImage(doc.favicon, 'favicon')
      analysis.imageFields.favicon = result
      if (result.status !== 'migrated') {
        analysis.status = result.status
        analysis.issues.push(...result.issues)
      }
    }
  }
  
  // About Page analysis
  if (doc._type === 'aboutPage') {
    // Check heroSection.heroImage
    if (doc.heroSection?.heroImage) {
      const result = analyzeImage(doc.heroSection.heroImage, 'heroSection.heroImage')
      analysis.imageFields['heroSection.heroImage'] = result
      if (result.status !== 'migrated') {
        analysis.status = result.status
        analysis.issues.push(...result.issues)
      }
    }
    
    // Check sections images
    if (doc.sections && Array.isArray(doc.sections)) {
      doc.sections.forEach((section, sectionIndex) => {
        // Check imageTextSection images
        if (section._type === 'imageTextSection' && section.image) {
          const result = analyzeImage(section.image, `sections[${sectionIndex}].image`)
          analysis.imageFields[`sections[${sectionIndex}].image`] = result
          if (result.status !== 'migrated') {
            analysis.status = result.status
            analysis.issues.push(...result.issues)
          }
        }
        
        // Check teamSection member photos
        if (section._type === 'teamSection' && section.teamMembers && Array.isArray(section.teamMembers)) {
          section.teamMembers.forEach((member, memberIndex) => {
            if (member.photo) {
              const result = analyzeImage(member.photo, `sections[${sectionIndex}].teamMembers[${memberIndex}].photo`)
              analysis.imageFields[`sections[${sectionIndex}].teamMembers[${memberIndex}].photo`] = result
              if (result.status !== 'migrated') {
                analysis.status = result.status
                analysis.issues.push(...result.issues)
              }
            }
          })
        }
      })
    }
  }
  
  return analysis
}

/* --- 主流程 ---------------------------------------------------------- */
(async () => {
  try {
    console.log('📊 Dataset:', client.config().dataset)
    console.log()
    
    spinner.start('📊 Analyzing all documents...')
    
    // Query all documents with images
    const query = `{
      "products": *[_type == "product"] {
        _id, _type, gallery,
        "title": name.zh
      },
      "categories": *[_type == "category"] {
        _id, _type, image,
        "title": name.zh
      },
      "ipSeries": *[_type == "ipSeries"] {
        _id, _type, logo, bannerImage,
        "title": name.zh
      },
      "homepage": *[_type == "homepage"] {
        _id, _type, brandStorySection,
        "title": title.zh
      },
      "siteSettings": *[_type == "siteSettings"] {
        _id, _type, logo, favicon,
        "title": siteTitle.zh
      },
      "aboutPage": *[_type == "aboutPage"] {
        _id, _type, heroSection, sections,
        "title": title.zh
      }
    }`
    
    const results = await client.fetch(query)
    spinner.succeed('Analysis complete!')
    
    // Analyze all documents
    const analysis = {
      migrated: [],
      incomplete: [],
      old_format: [],
      missing: [],
      unknown: [],
      stats: {
        total: 0,
        migrated: 0,
        needsMigration: 0,
        totalImages: 0
      }
    }
    
    for (const [type, docs] of Object.entries(results)) {
      for (const doc of docs) {
        const docAnalysis = analyzeDocument(doc)
        docAnalysis.title = doc.title || 'Untitled'
        
        analysis.stats.total++
        analysis[docAnalysis.status].push(docAnalysis)
        
        if (docAnalysis.status === 'migrated') {
          analysis.stats.migrated++
        } else {
          analysis.stats.needsMigration++
        }
        
        // Count images
        analysis.stats.totalImages += Object.keys(docAnalysis.imageFields).length
      }
    }
    
    // Print summary
    console.log('\n📊 Migration Status Summary')
    console.log('============================')
    console.log(`Total documents:    ${analysis.stats.total}`)
    console.log(`Total image fields: ${analysis.stats.totalImages}`)
    console.log(`✅ Fully migrated:  ${analysis.stats.migrated}`)
    console.log(`⚠️  Need migration: ${analysis.stats.needsMigration}`)
    console.log()
    
    // Detailed breakdown
    const statusEmojis = {
      migrated: '✅',
      incomplete: '⚠️ ',
      old_format: '🔄',
      missing: '❓',
      unknown: '❌'
    }
    
    const statusLabels = {
      migrated: 'Fully Migrated',
      incomplete: 'Incomplete flexibleImage',
      old_format: 'Old Format (needs migration)',
      missing: 'Missing Images',
      unknown: 'Unknown Format'
    }
    
    for (const [status, docs] of Object.entries(analysis)) {
      if (status === 'stats') continue
      if (docs.length === 0) continue
      
      console.log(`\n${statusEmojis[status]} ${statusLabels[status]} (${docs.length})`)
      console.log('─'.repeat(50))
      
      for (const doc of docs) {
        console.log(`📄 ${doc._type}: ${doc.title} (${doc._id.slice(-6)})`)
        
        if (isVerbose && doc.issues.length > 0) {
          for (const issue of doc.issues) {
            console.log(`   • ${issue}`)
          }
        }
      }
    }
    
    // Recommendations
    if (analysis.stats.needsMigration > 0) {
      console.log('\n🛠️  Recommended Actions')
      console.log('======================')
      console.log('Run the migration script to fix these issues:')
      console.log()
      console.log('1. Dry run first (recommended):')
      console.log('   node scripts/migrate-all-images.js')
      console.log()
      console.log('2. Execute migration:')
      console.log('   node scripts/migrate-all-images.js --execute')
      console.log()
      console.log('3. Execute and publish:')
      console.log('   node scripts/migrate-all-images.js --execute --publish')
    } else {
      console.log('\n🎉 All images are properly migrated!')
      console.log('No action needed.')
    }
    
    console.log()
    
  } catch (error) {
    console.error('\n💥 Error analyzing documents:', error)
    process.exit(1)
  }
})()