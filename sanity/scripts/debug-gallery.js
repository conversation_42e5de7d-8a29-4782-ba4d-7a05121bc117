#!/usr/bin/env node
/**
 * Simple debug script for gallery investigation
 */

require('dotenv').config()
const {createClient} = require('@sanity/client')

const client = createClient({
  projectId : process.env.SANITY_STUDIO_PROJECT_ID || '4za4x22i',
  dataset   : process.env.SANITY_STUDIO_DATASET     || 'production',
  token     : process.env.SANITY_WRITE_TOKEN || process.env.SANITY_READ_TOKEN,
  apiVersion: '2024-01-01',
  useCdn    : false
})

console.log('🔍 Gallery Debug')
console.log('===============')

// Query the specific problematic product
const query = `*[_type == "product" && _id match "*db933d"]{
  _id, 
  "name": name.zh,
  // mainImage has been migrated to gallery[0]
  gallery
}[0]`

client.fetch(query)
  .then(product => {
    if (!product) {
      console.log('❌ Product not found')
      return
    }
    
    console.log(`📄 Product: ${product.name}`)
    console.log(`📄 ID: ${product._id}`)
    console.log()
    
    console.log('🖼️  Main Image:')
    console.log('Main image (gallery[0]):', JSON.stringify(product.gallery?.[0], null, 2))
    console.log()
    
    console.log('🖼️  Gallery:')
    if (product.gallery && Array.isArray(product.gallery)) {
      console.log(`Gallery has ${product.gallery.length} items`)
      
      product.gallery.forEach((item, index) => {
        console.log(`\n[${index}] -------`)
        console.log(JSON.stringify(item, null, 2))
      })
    } else {
      console.log('No gallery')
    }
  })
  .catch(error => {
    console.error('Error:', error)
  })