#!/usr/bin/env node
/**
 * repair-broken-images.js
 *
 * Repairs broken flexibleImage data that may have been created during migration
 * Specifically targets incomplete flexibleImage objects that are missing required fields
 * 
 * Usage:
 *   node scripts/repair-broken-images.js                  # Dry run by default
 *   node scripts/repair-broken-images.js --execute        # Actually perform repairs
 *   node scripts/repair-broken-images.js --execute --publish  # Repair and publish documents
 */

require('dotenv').config()
const {createClient} = require('@sanity/client')
const {customAlphabet} = require('nanoid')
const nanoid = customAlphabet('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz', 10)

const isDryRun = !process.argv.includes('--execute')
const shouldPublish = process.argv.includes('--publish')

console.log('🔧 Image Repair Tool')
console.log('===================')
console.log(`Mode: ${isDryRun ? 'DRY RUN (no changes will be made)' : 'EXECUTE'}`)
console.log(`Publish after repair: ${shouldPublish ? 'YES' : 'NO'}`)
console.log()

/* --- Sanity Client -------------------------------------------------- */
const client = createClient({
  projectId : process.env.SANITY_STUDIO_PROJECT_ID || '4za4x22i',
  dataset   : process.env.SANITY_STUDIO_DATASET     || 'production',
  token     : process.env.SANITY_WRITE_TOKEN,
  apiVersion: new Date().toISOString().slice(0,10),   // YYYY-MM-DD
  useCdn    : false
})

if (!client.config().token) {
  console.error('❌ 需要 SANITY_WRITE_TOKEN（Editor / Developer 权限）')
  console.error('   请在 .env 文件中设置 SANITY_WRITE_TOKEN')
  process.exit(1)
}

/* --- 诊断和修复单个图片 --------------------------------------------- */
function repairImage(item, context = '') {
  if (!item) return null
  
  // Skip non-flexibleImage items
  if (item._type !== 'flexibleImage') {
    return item
  }
  
  const issues = []
  const imageType = item.imageType || 'upload'
  
  // Check for missing required fields
  if (!item.imageType) issues.push('missing imageType')
  if (typeof item.alt === 'undefined') issues.push('missing alt')
  if (typeof item.caption === 'undefined') issues.push('missing caption')
  
  // Check for missing image data based on type
  if (imageType === 'upload') {
    if (!item.uploadedImage) {
      issues.push('missing uploadedImage')
      
      // Try to recover from legacy fields
      if (item.asset) {
        console.log(`🔧 ${context}: Recovering uploadedImage from legacy asset field`)
        return {
          ...item,
          imageType: 'upload',
          uploadedImage: {
            _type: 'image',
            asset: item.asset,
            crop: item.crop,
            hotspot: item.hotspot
          },
          alt: item.alt || '',
          caption: item.caption || ''
        }
      } else if (item._ref) {
        console.log(`🔧 ${context}: Recovering uploadedImage from direct _ref`)
        return {
          ...item,
          imageType: 'upload',
          uploadedImage: {
            _type: 'image',
            asset: {_type: 'reference', _ref: item._ref},
            crop: item.crop,
            hotspot: item.hotspot
          },
          alt: item.alt || '',
          caption: item.caption || ''
        }
      } else {
        console.warn(`❌ ${context}: Cannot repair - no recoverable image data`)
        return null // Mark for removal
      }
    } else {
      // Check if uploadedImage has the wrong structure (direct _ref instead of nested asset)
      if (item.uploadedImage._ref && !item.uploadedImage.asset) {
        console.log(`🔧 ${context}: Fixing uploadedImage structure (converting direct _ref to nested asset)`)
        return {
          ...item,
          imageType: 'upload',
          uploadedImage: {
            _type: 'image',
            asset: {
              _type: 'reference',
              _ref: item.uploadedImage._ref
            },
            crop: item.uploadedImage.crop || item.crop,
            hotspot: item.uploadedImage.hotspot || item.hotspot
          },
          alt: item.alt || '',
          caption: item.caption || ''
        }
      } else if (!item.uploadedImage.asset) {
        issues.push('uploadedImage missing asset')
        console.warn(`❌ ${context}: Cannot repair - uploadedImage has no asset and no _ref`)
        return null // Mark for removal
      }
    }
  } else if (imageType === 'external') {
    if (!item.externalUrl) {
      issues.push('missing externalUrl')
      console.warn(`❌ ${context}: Cannot repair external image - no URL`)
      return null // Mark for removal
    }
  }
  
  // If we have issues but can fix them
  if (issues.length > 0 && item !== null) {
    console.log(`🔧 ${context}: Fixing issues: ${issues.join(', ')}`)
    return {
      ...item,
      imageType,
      alt: item.alt || '',
      caption: item.caption || ''
    }
  }
  
  // Return unchanged if no issues
  return item
}

/* --- 修复数组中的图片 ----------------------------------------------- */
function repairImageArray(arr, context = '') {
  if (!Array.isArray(arr)) return arr
  
  const repaired = arr.map((item, index) => 
    repairImage(item, `${context}[${index}]`)
  ).filter(Boolean) // Remove null items
  
  if (repaired.length !== arr.length) {
    console.log(`🗑️  ${context}: Removed ${arr.length - repaired.length} broken image(s)`)
  }
  
  return repaired
}

/* --- 检查和修复单个文档 --------------------------------------------- */
function getDocumentRepairs(doc) {
  const repairs = {}
  let hasChanges = false
  const docName = `${doc._type}:${doc._id.slice(-6)}`
  
  // Product: gallery only (mainImage is now gallery[0])
  if (doc._type === 'product') {
    if (doc.gallery && Array.isArray(doc.gallery)) {
      const repaired = repairImageArray(doc.gallery, `${docName}.gallery`)
      if (JSON.stringify(repaired) !== JSON.stringify(doc.gallery)) {
        repairs.gallery = repaired
        hasChanges = true
      }
    }
  }
  
  // Category: image
  if (doc._type === 'category' && doc.image) {
    const repaired = repairImage(doc.image, `${docName}.image`)
    if (JSON.stringify(repaired) !== JSON.stringify(doc.image)) {
      repairs.image = repaired
      hasChanges = true
    }
  }
  
  // IP Series: logo and bannerImage
  if (doc._type === 'ipSeries') {
    if (doc.logo) {
      const repaired = repairImage(doc.logo, `${docName}.logo`)
      if (JSON.stringify(repaired) !== JSON.stringify(doc.logo)) {
        repairs.logo = repaired
        hasChanges = true
      }
    }
    
    if (doc.bannerImage) {
      const repaired = repairImage(doc.bannerImage, `${docName}.bannerImage`)
      if (JSON.stringify(repaired) !== JSON.stringify(doc.bannerImage)) {
        repairs.bannerImage = repaired
        hasChanges = true
      }
    }
  }
  
  // Homepage: brandStorySection.backgroundImage
  if (doc._type === 'homepage' && doc.brandStorySection?.backgroundImage) {
    const repaired = repairImage(doc.brandStorySection.backgroundImage, `${docName}.brandStorySection.backgroundImage`)
    if (JSON.stringify(repaired) !== JSON.stringify(doc.brandStorySection.backgroundImage)) {
      repairs['brandStorySection.backgroundImage'] = repaired
      hasChanges = true
    }
  }
  
  // Site Settings: logo and favicon
  if (doc._type === 'siteSettings') {
    if (doc.logo) {
      const repaired = repairImage(doc.logo, `${docName}.logo`)
      if (JSON.stringify(repaired) !== JSON.stringify(doc.logo)) {
        repairs.logo = repaired
        hasChanges = true
      }
    }
    
    if (doc.favicon) {
      const repaired = repairImage(doc.favicon, `${docName}.favicon`)
      if (JSON.stringify(repaired) !== JSON.stringify(doc.favicon)) {
        repairs.favicon = repaired
        hasChanges = true
      }
    }
  }
  
  return hasChanges ? repairs : null
}

/* --- 查找需要修复的文档 --------------------------------------------- */
async function findDocumentsNeedingRepair() {
  console.log('🔍 Analyzing documents for broken images...')
  
  try {
    // Query all document types that might have broken images
    const query = `{
      "products": *[_type == "product"] {
        _id, _rev, _type, gallery
      },
      "categories": *[_type == "category"] {
        _id, _rev, _type, image
      },
      "ipSeries": *[_type == "ipSeries"] {
        _id, _rev, _type, logo, bannerImage
      },
      "homepage": *[_type == "homepage"] {
        _id, _rev, _type, brandStorySection
      },
      "siteSettings": *[_type == "siteSettings"] {
        _id, _rev, _type, logo, favicon
      }
    }`
    
    const results = await client.fetch(query)
    
    const documentsToRepair = []
    const stats = {
      products: 0,
      categories: 0,
      ipSeries: 0,
      homepage: 0,
      siteSettings: 0,
      totalImages: 0
    }
    
    // Check each document type
    for (const [type, docs] of Object.entries(results)) {
      for (const doc of docs) {
        const repairs = getDocumentRepairs(doc)
        if (repairs) {
          documentsToRepair.push({ doc, repairs })
          stats[type]++
        }
      }
    }
    
    console.log('✅ Analysis complete!')
    
    return { documentsToRepair, stats }
  } catch (error) {
    console.error('❌ Failed to analyze documents:', error)
    throw error
  }
}

/* --- 执行修复 ------------------------------------------------------- */
async function performRepairs(documentsToRepair) {
  let repaired = 0
  let failed = 0
  
  for (const { doc, repairs } of documentsToRepair) {
    const displayName = `${doc._type}:${doc._id.slice(-6)}`
    
    if (isDryRun) {
      console.log(`\n📋 Would repair ${displayName}:`)
      console.log(JSON.stringify(repairs, null, 2))
      repaired++
      continue
    }
    
    try {
      // Perform the repair
      const transaction = client
        .patch(doc._id)
        .ifRevisionId(doc._rev)
        .set(repairs)
      
      // Publish if requested
      if (shouldPublish) {
        await transaction.commit({ publish: true })
        console.log(`✅ Repaired and published: ${displayName}`)
      } else {
        await transaction.commit()
        console.log(`✅ Repaired: ${displayName}`)
      }
      
      repaired++
      
      // Small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 100))
    } catch (error) {
      console.error(`❌ Failed to repair ${displayName}:`, error.message)
      failed++
    }
  }
  
  return { repaired, failed }
}

/* --- 主流程 ---------------------------------------------------------- */
(async () => {
  try {
    console.log('📊 Dataset:', client.config().dataset)
    console.log()
    
    // Find documents needing repair
    const { documentsToRepair, stats } = await findDocumentsNeedingRepair()
    
    console.log('\n📊 Repair Summary:')
    console.log('==================')
    console.log(`Products:      ${stats.products} documents`)
    console.log(`Categories:    ${stats.categories} documents`)
    console.log(`IP Series:     ${stats.ipSeries} documents`)
    console.log(`Homepage:      ${stats.homepage} documents`)
    console.log(`Site Settings: ${stats.siteSettings} documents`)
    console.log(`-----------------------`)
    console.log(`Total documents: ${documentsToRepair.length}`)
    
    if (documentsToRepair.length === 0) {
      console.log('\n✨ All images are properly structured! No repairs needed.')
      process.exit(0)
    }
    
    if (isDryRun) {
      console.log('\n🔍 Running in DRY RUN mode. Showing what would be repaired...')
    } else {
      console.log('\n⚠️  This will modify your production data!')
      console.log('Press Ctrl+C within 5 seconds to cancel...')
      await new Promise(resolve => setTimeout(resolve, 5000))
    }
    
    // Perform repairs
    const { repaired, failed } = await performRepairs(documentsToRepair)
    
    // Final summary
    console.log('\n🎉 Repair Complete!')
    console.log('===================')
    console.log(`Successfully repaired: ${repaired} documents`)
    if (failed > 0) {
      console.log(`Failed: ${failed} documents`)
    }
    
    if (isDryRun) {
      console.log('\n📌 This was a DRY RUN. To actually perform the repairs, run:')
      console.log('   node scripts/repair-broken-images.js --execute')
      if (!shouldPublish) {
        console.log('\n📌 To also publish documents after repair:')
        console.log('   node scripts/repair-broken-images.js --execute --publish')
      }
    } else if (!shouldPublish) {
      console.log('\n📌 Documents have been repaired but NOT published.')
      console.log('   They will remain as drafts until manually published.')
    }
    
    process.exit(failed > 0 ? 1 : 0)
  } catch (error) {
    console.error('\n💥 Fatal error:', error)
    process.exit(1)
  }
})()