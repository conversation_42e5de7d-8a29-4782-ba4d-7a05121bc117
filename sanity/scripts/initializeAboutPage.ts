import {createClient} from '@sanity/client'
import {v4 as uuidv4} from 'uuid'

const client = createClient({
  projectId: process.env.SANITY_STUDIO_PROJECT_ID || '4za4x22i',
  dataset: process.env.SANITY_STUDIO_DATASET || 'staging',
  apiVersion: '2024-01-01',
  token: process.env.SANITY_STUDIO_API_TOKEN,
  useCdn: false
})

// 创建默认的块内容
function createDefaultBlockContent(text: string) {
  return [{
    _type: 'block',
    _key: uuidv4(),
    style: 'normal',
    markDefs: [],
    children: [{
      _type: 'span',
      _key: uuidv4(),
      text: text,
      marks: []
    }]
  }]
}

async function initializeAboutPage() {
  console.log('🚀 初始化 About Page 文档...\n')
  
  try {
    // 检查是否已存在
    const existing = await client.fetch(`*[_type == "aboutPage"][0]`)
    if (existing) {
      console.log('✅ About Page 文档已存在，无需创建')
      return
    }
    
    // 创建默认的 aboutPage 文档
    const defaultAboutPage = {
      _type: 'aboutPage',
      _id: 'aboutPage', // 单例文档使用固定 ID
      title: {
        zh: '关于我们',
        en: 'About Us',
        ar: 'من نحن'
      },
      heroSection: {
        headline: {
          zh: createDefaultBlockContent('欢迎了解 MyNgaPop'),
          en: createDefaultBlockContent('Welcome to MyNgaPop'),
          ar: createDefaultBlockContent('مرحبا بكم في MyNgaPop')
        },
        subtitle: {
          zh: createDefaultBlockContent('我们致力于为全球粉丝带来最优质的动漫周边产品'),
          en: createDefaultBlockContent('We are dedicated to bringing the best anime merchandise to fans worldwide'),
          ar: createDefaultBlockContent('نحن ملتزمون بتقديم أفضل منتجات الأنمي للمعجبين في جميع أنحاء العالم')
        }
      },
      sections: [
        {
          _type: 'textSection',
          _key: uuidv4(),
          title: {
            zh: '我们的故事',
            en: 'Our Story',
            ar: 'قصتنا'
          },
          content: {
            zh: createDefaultBlockContent('MyNgaPop 成立于对动漫文化的热爱。我们相信，每一件周边产品都承载着粉丝们的情感和回忆。'),
            en: createDefaultBlockContent('MyNgaPop was founded on a love for anime culture. We believe that every piece of merchandise carries the emotions and memories of fans.'),
            ar: createDefaultBlockContent('تأسست MyNgaPop على حب ثقافة الأنمي. نحن نؤمن بأن كل قطعة من البضائع تحمل مشاعر وذكريات المعجبين.')
          }
        }
      ],
      team: {
        title: {
          zh: '我们的团队',
          en: 'Our Team',
          ar: 'فريقنا'
        },
        members: []
      },
      seo: {
        title: {
          zh: '关于我们 - MyNgaPop',
          en: 'About Us - MyNgaPop',
          ar: 'من نحن - MyNgaPop'
        },
        description: {
          zh: '了解 MyNgaPop - 您值得信赖的动漫周边商品平台',
          en: 'Learn about MyNgaPop - Your trusted anime merchandise platform',
          ar: 'تعرف على MyNgaPop - منصة بضائع الأنمي الموثوقة'
        }
      }
    }
    
    console.log('📝 创建 About Page 文档...')
    const result = await client.create(defaultAboutPage)
    
    console.log('✅ About Page 创建成功!')
    console.log('  - ID:', result._id)
    console.log('  - 类型:', result._type)
    console.log('\n现在您可以在 Sanity Studio 中编辑和发布这个文档了！')
    
  } catch (error) {
    console.error('❌ 创建失败:', error)
    if (error instanceof Error) {
      console.error('错误详情:', error.message)
    }
  }
}

initializeAboutPage()