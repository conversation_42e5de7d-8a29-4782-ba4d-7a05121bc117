import {createClient} from '@sanity/client'

const client = createClient({
  projectId: process.env.SANITY_STUDIO_PROJECT_ID || '4za4x22i',
  dataset: process.env.SANITY_STUDIO_DATASET || 'staging',
  apiVersion: '2024-01-01',
  token: process.env.SANITY_STUDIO_API_TOKEN,
  useCdn: false
})

async function fixFontSizeAnnotations() {
  console.log('🔧 修复字体大小注释数据...\n')
  
  try {
    // 获取 aboutPage 文档（包括草稿）
    const documents = await client.fetch(`
      *[_type == "aboutPage"] {
        _id,
        _type,
        _rev,
        heroSection,
        sections
      }
    `)
    
    console.log(`找到 ${documents.length} 个 aboutPage 文档\n`)
    
    for (const doc of documents) {
      console.log(`检查文档: ${doc._id}`)
      let hasIssues = false
      const patches: any[] = []
      
      // 修复块内容中的字体大小注释
      const fixBlockContent = (blocks: any[], path: string) => {
        if (!Array.isArray(blocks)) return blocks
        
        return blocks.map((block, blockIndex) => {
          if (block._type === 'block' && Array.isArray(block.markDefs)) {
            const fixedMarkDefs = block.markDefs.map((markDef: any, markDefIndex: number) => {
              if (markDef._type === 'fontSize') {
                // 检查 size 字段是否是对象
                if (typeof markDef.size === 'object' && markDef.size !== null) {
                  console.log(`  ❌ 发现错误的字体大小数据在 ${path}[${blockIndex}].markDefs[${markDefIndex}]`)
                  console.log(`     当前值:`, JSON.stringify(markDef.size))
                  hasIssues = true
                  
                  // 尝试提取正确的值
                  let correctSize = 'default'
                  if (markDef.size.value) {
                    correctSize = markDef.size.value
                  } else if (markDef.size.size) {
                    correctSize = markDef.size.size
                  }
                  
                  console.log(`     修复为: "${correctSize}"`)
                  
                  return {
                    ...markDef,
                    size: correctSize
                  }
                }
              }
              return markDef
            })
            
            return {
              ...block,
              markDefs: fixedMarkDefs
            }
          }
          return block
        })
      }
      
      // 创建修复后的文档
      const fixedDoc = {...doc}
      
      // 修复 heroSection
      if (doc.heroSection) {
        if (doc.heroSection.headline) {
          const locales = ['zh', 'en', 'ar']
          locales.forEach(locale => {
            if (doc.heroSection.headline[locale]) {
              fixedDoc.heroSection.headline[locale] = fixBlockContent(
                doc.heroSection.headline[locale],
                `heroSection.headline.${locale}`
              )
            }
          })
        }
        
        if (doc.heroSection.subtitle) {
          const locales = ['zh', 'en', 'ar']
          locales.forEach(locale => {
            if (doc.heroSection.subtitle[locale]) {
              fixedDoc.heroSection.subtitle[locale] = fixBlockContent(
                doc.heroSection.subtitle[locale],
                `heroSection.subtitle.${locale}`
              )
            }
          })
        }
      }
      
      // 修复 sections
      if (Array.isArray(doc.sections)) {
        fixedDoc.sections = doc.sections.map((section: any, sectionIndex: number) => {
          if (section._type === 'textSection' && section.content) {
            const fixedSection = {...section}
            const locales = ['zh', 'en', 'ar']
            
            locales.forEach(locale => {
              if (section.content[locale]) {
                fixedSection.content[locale] = fixBlockContent(
                  section.content[locale],
                  `sections[${sectionIndex}].content.${locale}`
                )
              }
            })
            
            return fixedSection
          }
          return section
        })
      }
      
      // 如果发现问题，更新文档
      if (hasIssues) {
        console.log(`\n📝 更新文档 ${doc._id}...`)
        
        try {
          // 使用 set 操作更新整个文档
          await client
            .patch(doc._id)
            .set({
              heroSection: fixedDoc.heroSection,
              sections: fixedDoc.sections
            })
            .commit()
          
          console.log(`✅ 文档 ${doc._id} 修复成功！`)
        } catch (error) {
          console.error(`❌ 更新文档 ${doc._id} 失败:`, error)
        }
      } else {
        console.log(`✅ 文档 ${doc._id} 没有发现问题`)
      }
      
      console.log('')
    }
    
    console.log('🎉 修复完成！')
    console.log('请刷新 Sanity Studio 并再次尝试发布。')
    
  } catch (error) {
    console.error('❌ 修复过程出错:', error)
  }
}

// 运行修复
fixFontSizeAnnotations()