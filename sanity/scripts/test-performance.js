/**
 * Performance test for image loading and studio responsiveness
 */
import {createClient} from '@sanity/client'
import dotenv from 'dotenv'

dotenv.config()

const client = createClient({
  projectId: process.env.SANITY_STUDIO_PROJECT_ID || '4za4x22i',
  dataset: process.env.SANITY_STUDIO_DATASET || 'production',
  apiVersion: '2024-01-01',
  useCdn: false
})

async function testPerformance() {
  console.log('⚡ Testing Sanity Studio performance...\n')

  try {
    // Test query performance
    console.log('📊 Testing query performance...')
    
    const start = Date.now()
    const products = await client.fetch(`
      *[_type == "product"][0...50] {
        _id,
        name,
        gallery[] {
          imageType,
          uploadedImage {
            asset-> {
              _id,
              url,
              metadata {
                dimensions
              }
            }
          },
          externalUrl,
          asset-> {
            _id,
            url,
            metadata {
              dimensions
            }
          }
        },
        category-> {
          name
        }
      }
    `)
    const queryTime = Date.now() - start

    console.log(`   ✅ Query completed in ${queryTime}ms`)
    console.log(`   📦 Retrieved ${products.length} products`)

    // Analyze image sizes and types
    let uploadedImages = 0
    let externalImages = 0
    let noImages = 0
    let totalImageSize = 0
    let imageDimensions = []

    products.forEach(product => {
      if (product.gallery && product.gallery[0]) {
        const mainImage = product.gallery[0]
        if (mainImage.imageType === 'upload' && mainImage.uploadedImage) {
          uploadedImages++
          if (mainImage.uploadedImage.asset?.metadata?.dimensions) {
            const {width, height} = mainImage.uploadedImage.asset.metadata.dimensions
            imageDimensions.push({width, height})
          }
        } else if (mainImage.imageType === 'external') {
          externalImages++
        }
      } else {
        noImages++
      }
    })

    console.log('\n📸 Image statistics:')
    console.log(`   Uploaded images: ${uploadedImages}`)
    console.log(`   External images: ${externalImages}`)
    console.log(`   No images: ${noImages}`)

    if (imageDimensions.length > 0) {
      const avgWidth = imageDimensions.reduce((sum, dim) => sum + dim.width, 0) / imageDimensions.length
      const avgHeight = imageDimensions.reduce((sum, dim) => sum + dim.height, 0) / imageDimensions.length
      console.log(`   Average dimensions: ${Math.round(avgWidth)}x${Math.round(avgHeight)}px`)
    }

    // Test filter query performance
    console.log('\n🔍 Testing filter query performance...')
    
    const filterStart = Date.now()
    const publishedProducts = await client.fetch(`
      *[_type == "product" && isPublished == true][0...20] {
        _id,
        name,
        gallery
      }
    `)
    const filterTime = Date.now() - filterStart

    console.log(`   ✅ Filter query completed in ${filterTime}ms`)
    console.log(`   📦 Found ${publishedProducts.length} published products`)

    // Performance recommendations
    console.log('\n💡 Performance analysis:')
    
    if (queryTime > 1000) {
      console.log('   ⚠️  Query time >1s - consider optimizing query or using CDN')
    } else if (queryTime > 500) {
      console.log('   ⚠️  Query time >500ms - monitor performance')
    } else {
      console.log('   ✅ Query performance is good')
    }

    if (imageDimensions.length > 0) {
      const largeImages = imageDimensions.filter(dim => dim.width > 1000 || dim.height > 1000).length
      if (largeImages > imageDimensions.length * 0.5) {
        console.log('   ⚠️  Many images >1000px - consider using Sanity image transforms for thumbnails')
      }
    }

    console.log('\n🎯 Recommendations for studio optimization:')
    console.log('   • Use Sanity image transforms for thumbnail generation')
    console.log('   • Consider lazy loading for long product lists')
    console.log('   • Enable CDN for production queries')
    console.log('   • Monitor query performance in production')

  } catch (error) {
    console.error('❌ Performance test failed:', error)
  }
}

testPerformance()