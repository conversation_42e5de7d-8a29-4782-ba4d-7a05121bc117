#!/usr/bin/env node
/**
 * Debug script to show exact gallery structure
 */

require('dotenv').config()
const {createClient} = require('@sanity/client')

const client = createClient({
  projectId : process.env.SANITY_STUDIO_PROJECT_ID || '4za4x22i',
  dataset   : process.env.SANITY_STUDIO_DATASET     || 'production',
  token     : process.env.SANITY_WRITE_TOKEN || process.env.SANITY_READ_TOKEN,
  apiVersion: '2024-01-01',
  useCdn    : false
})

// Query one product with gallery issues
const query = `*[_type == "product" && defined(gallery) && count(gallery) > 0][0]{
  _id, 
  "name": name.zh,
  gallery
}`

client.fetch(query)
  .then(product => {
    if (!product) {
      console.log('❌ No products with gallery found')
      return
    }
    
    console.log(`📄 Product: ${product.name}`)
    console.log(`📄 ID: ${product._id}`)
    console.log()
    
    console.log('Raw Gallery Data:')
    console.log('================')
    console.log(JSON.stringify(product.gallery, null, 2))
    
    console.log('\nGallery Analysis:')
    console.log('================')
    
    product.gallery.forEach((item, index) => {
      console.log(`\n[${index}] Type: ${item._type || 'undefined'}`)
      console.log(`    Key: ${item._key || 'undefined'}`)
      console.log(`    ImageType: ${item.imageType || 'undefined'}`)
      
      // FlexibleImage format analysis
      if (item.imageType === 'upload') {
        console.log(`    Has uploadedImage: ${!!item.uploadedImage}`)
        if (item.uploadedImage) {
          console.log(`    uploadedImage._type: ${item.uploadedImage._type || 'undefined'}`)
          console.log(`    uploadedImage.asset: ${!!item.uploadedImage.asset}`)
          if (item.uploadedImage.asset) {
            console.log(`    asset._ref: ${item.uploadedImage.asset._ref || 'undefined'}`)
            console.log(`    asset._type: ${item.uploadedImage.asset._type || 'undefined'}`)
          }
        }
      } else if (item.imageType === 'external') {
        console.log(`    ExternalUrl: ${item.externalUrl || 'undefined'}`)
        console.log(`    Has fallbackImage: ${!!item.fallbackImage}`)
        if (item.fallbackImage) {
          console.log(`    fallbackImage.asset: ${!!item.fallbackImage.asset}`)
        }
      }
      
      // Legacy pattern detection (should not exist after migration)
      if (item.asset || item._ref) {
        console.log(`    ⚠️  LEGACY: Has direct asset: ${!!item.asset}`)
        console.log(`    ⚠️  LEGACY: Has direct _ref: ${!!item._ref}`)
      }
      
      console.log(`    Alt: "${item.alt || ''}"`)
      console.log(`    Caption: "${item.caption || ''}"`)
    })
  })
  .catch(error => {
    console.error('Error:', error)
  })