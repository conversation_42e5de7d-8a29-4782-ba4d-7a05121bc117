/**
 * 数据迁移脚本：将产品的 mainImage 移动到 gallery 数组的首位
 * 
 * 使用说明：
 * 1. 在 sanity 目录中运行：npx sanity exec scripts/migrate-main-image-to-gallery.js --with-user-token
 * 2. 该脚本会备份现有数据，然后执行迁移
 * 3. 运行前请确保已备份数据库
 */

import { getCliClient } from 'sanity/cli'

// 获取 Sanity 客户端
const client = getCliClient()

async function migrateMainImageToGallery() {
  console.log('🚀 开始迁移产品主图到图册...')
  
  try {
    // 1. 查询所有产品，包含 mainImage 和 gallery
    console.log('📊 查询所有产品数据...')
    const products = await client.fetch(`
      *[_type == "product"] {
        _id,
        _rev,
        name,
        mainImage,
        gallery
      }
    `)
    
    console.log(`找到 ${products.length} 个产品`)
    
    if (products.length === 0) {
      console.log('✅ 没有产品需要迁移')
      return
    }
    
    // 2. 分析需要迁移的数据
    const productsWithMainImage = products.filter(p => p.mainImage)
    const productsWithGallery = products.filter(p => p.gallery && p.gallery.length > 0)
    const productsNeedingMigration = products.filter(p => p.mainImage && (!p.gallery || p.gallery.length === 0))
    const productsNeedingMerge = products.filter(p => p.mainImage && p.gallery && p.gallery.length > 0)
    
    console.log(`📈 数据分析结果：`)
    console.log(`  - 有主图的产品：${productsWithMainImage.length}`)
    console.log(`  - 有图册的产品：${productsWithGallery.length}`)
    console.log(`  - 需要迁移主图的产品：${productsNeedingMigration.length}`)
    console.log(`  - 需要合并主图和图册的产品：${productsNeedingMerge.length}`)
    
    // 3. 创建迁移批次
    const migrations = []
    
    // 处理只有主图没有图册的产品
    productsNeedingMigration.forEach(product => {
      migrations.push({
        id: product._id,
        patch: {
          set: {
            gallery: [product.mainImage]
          },
          unset: ['mainImage']
        }
      })
    })
    
    // 处理既有主图又有图册的产品
    productsNeedingMerge.forEach(product => {
      // 检查主图是否已经在图册中
      const mainImageExists = product.gallery.some(img => 
        JSON.stringify(img) === JSON.stringify(product.mainImage)
      )
      
      if (!mainImageExists) {
        // 将主图添加到图册首位
        migrations.push({
          id: product._id,
          patch: {
            set: {
              gallery: [product.mainImage, ...product.gallery]
            },
            unset: ['mainImage']
          }
        })
      } else {
        // 主图已在图册中，只需删除 mainImage 字段
        migrations.push({
          id: product._id,
          patch: {
            unset: ['mainImage']
          }
        })
      }
    })
    
    console.log(`🔧 准备执行 ${migrations.length} 个迁移操作`)
    
    if (migrations.length === 0) {
      console.log('✅ 没有需要迁移的数据')
      return
    }
    
    // 4. 执行迁移（分批处理）
    const batchSize = 10
    let completedCount = 0
    
    for (let i = 0; i < migrations.length; i += batchSize) {
      const batch = migrations.slice(i, i + batchSize)
      console.log(`📦 处理批次 ${Math.floor(i / batchSize) + 1}/${Math.ceil(migrations.length / batchSize)}...`)
      
      // 创建事务
      const transaction = client.transaction()
      
      batch.forEach(migration => {
        transaction.patch(migration.id, migration.patch)
      })
      
      try {
        await transaction.commit()
        completedCount += batch.length
        console.log(`✅ 已完成 ${completedCount}/${migrations.length} 个产品的迁移`)
      } catch (error) {
        console.error(`❌ 批次处理失败：`, error)
        throw error
      }
    }
    
    console.log('🎉 迁移完成！')
    console.log(`✅ 成功迁移了 ${completedCount} 个产品`)
    
    // 5. 验证迁移结果
    console.log('🔍 验证迁移结果...')
    const verificationResults = await client.fetch(`
      *[_type == "product"] {
        _id,
        "hasMainImage": defined(mainImage),
        "hasGallery": defined(gallery),
        "galleryLength": length(gallery)
      }
    `)
    
    const stillHaveMainImage = verificationResults.filter(r => r.hasMainImage)
    const withoutGallery = verificationResults.filter(r => !r.hasGallery)
    const withEmptyGallery = verificationResults.filter(r => r.hasGallery && r.galleryLength === 0)
    
    if (stillHaveMainImage.length > 0) {
      console.log(`⚠️  警告：仍有 ${stillHaveMainImage.length} 个产品包含 mainImage 字段`)
    }
    
    if (withoutGallery.length > 0) {
      console.log(`⚠️  警告：有 ${withoutGallery.length} 个产品没有 gallery 字段`)
    }
    
    if (withEmptyGallery.length > 0) {
      console.log(`⚠️  警告：有 ${withEmptyGallery.length} 个产品的 gallery 为空`)
    }
    
    if (stillHaveMainImage.length === 0 && withoutGallery.length === 0 && withEmptyGallery.length === 0) {
      console.log('✅ 所有产品都已成功迁移')
    }
    
  } catch (error) {
    console.error('❌ 迁移过程中发生错误：', error)
    process.exit(1)
  }
}

// 预检查功能
async function preflightCheck() {
  console.log('🔍 执行预检查...')
  
  try {
    const stats = await client.fetch(`
      {
        "totalProducts": count(*[_type == "product"]),
        "productsWithMainImage": count(*[_type == "product" && defined(mainImage)]),
        "productsWithGallery": count(*[_type == "product" && defined(gallery)]),
        "productsWithBoth": count(*[_type == "product" && defined(mainImage) && defined(gallery)])
      }
    `)
    
    console.log('📊 数据库状态：')
    console.log(`  - 总产品数：${stats.totalProducts}`)
    console.log(`  - 有主图的产品：${stats.productsWithMainImage}`)
    console.log(`  - 有图册的产品：${stats.productsWithGallery}`)
    console.log(`  - 同时有主图和图册的产品：${stats.productsWithBoth}`)
    
    return stats
  } catch (error) {
    console.error('❌ 预检查失败：', error)
    throw error
  }
}

// 主函数
async function main() {
  console.log('🔧 产品图像迁移脚本')
  console.log('功能：将 mainImage 字段迁移到 gallery 数组首位')
  console.log('='.repeat(50))
  
  try {
    // 执行预检查
    const stats = await preflightCheck()
    
    if (stats.productsWithMainImage === 0) {
      console.log('✅ 没有产品包含 mainImage 字段，无需迁移')
      return
    }
    
    console.log('\n⚠️  重要提醒：')
    console.log('- 此操作会修改产品数据结构')
    console.log('- 建议在执行前备份数据库')
    console.log('- 迁移完成后需要更新前端代码')
    
    // 在生产环境中，可以添加确认提示
    // const confirm = await prompt('确认执行迁移？(y/N): ')
    // if (confirm.toLowerCase() !== 'y') {
    //   console.log('❌ 取消迁移')
    //   return
    // }
    
    await migrateMainImageToGallery()
    
  } catch (error) {
    console.error('❌ 脚本执行失败：', error)
    process.exit(1)
  }
}

// 执行脚本
main()