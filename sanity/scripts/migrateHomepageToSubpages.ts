#!/usr/bin/env node
import {createClient} from '@sanity/client'
import dotenv from 'dotenv'
import ora from 'ora'

// Load environment variables
dotenv.config({path: '.env.local'})
dotenv.config()

const projectId = process.env.SANITY_STUDIO_PROJECT_ID || '4za4x22i'
const dataset = process.env.SANITY_STUDIO_DATASET || 'production'
const apiVersion = '2024-01-01'
const token = process.env.SANITY_WRITE_TOKEN || process.env.SANITY_API_TOKEN

if (!token) {
  console.error('❌ SANITY_WRITE_TOKEN or SANITY_API_TOKEN is required in .env file')
  process.exit(1)
}

const client = createClient({
  projectId,
  dataset,
  apiVersion,
  token,
  useCdn: false,
})

async function migrateHomepageToSubpages() {
  const spinner = ora('Starting homepage migration to sub-pages...').start()

  try {
    // Step 1: Fetch existing homepage data
    spinner.text = 'Fetching existing homepage data...'
    const homepage = await client.fetch(`*[_type == "homepage"][0]`)
    
    if (!homepage) {
      spinner.warn('No existing homepage data found')
      return
    }

    // Step 2: Fetch featured products data
    spinner.text = 'Fetching featured products data...'
    const featuredProductsData = await client.fetch(`*[_type == "featuredProducts"][0]`)

    // Step 3: Create/Update Homepage Basic
    spinner.text = 'Creating Homepage Basic...'
    const homepageBasicData = {
      _id: 'homepageBasic',
      _type: 'homepageBasic',
      title: homepage.title,
      heroSection: homepage.heroSection || {}
    }
    
    const existingBasic = await client.fetch(`*[_id == "homepageBasic"][0]`)
    if (existingBasic) {
      await client.patch('homepageBasic').set(homepageBasicData).commit()
    } else {
      await client.create(homepageBasicData)
    }
    spinner.succeed('✅ Homepage Basic created')

    // Step 4: Create/Update Homepage Featured Products
    spinner.text = 'Creating Homepage Featured Products...'
    const homepageFeaturedData = {
      _id: 'homepageFeaturedProducts',
      _type: 'homepageFeaturedProducts',
      showSection: featuredProductsData?.isActive !== false,
      sectionTitle: featuredProductsData?.title || {
        zh: '精选产品',
        en: 'Featured Products',
        ar: 'المنتجات المميزة'
      },
      sectionSubtitle: featuredProductsData?.subtitle,
      products: featuredProductsData?.products || [],
      displaySettings: {
        showPrices: featuredProductsData?.displaySettings?.showPrices !== false,
        showRatings: featuredProductsData?.displaySettings?.showRatings !== false,
        showCategories: featuredProductsData?.displaySettings?.showCategories !== false,
        showBadges: featuredProductsData?.displaySettings?.showBadges !== false,
        showDescription: false
      },
      layoutSettings: {
        displayMode: 'grid',
        columnsDesktop: featuredProductsData?.layout?.columnsDesktop || 4,
        columnsTablet: featuredProductsData?.layout?.columnsTablet || 2,
        columnsMobile: featuredProductsData?.layout?.columnsMobile || 1,
      },
      ctaButton: {
        show: true,
        text: {
          zh: '查看所有产品',
          en: 'View All Products',
          ar: 'عرض جميع المنتجات'
        },
        url: '/products'
      }
    }
    
    const existingFeatured = await client.fetch(`*[_id == "homepageFeaturedProducts"][0]`)
    if (existingFeatured) {
      await client.patch('homepageFeaturedProducts').set(homepageFeaturedData).commit()
    } else {
      await client.create(homepageFeaturedData)
    }
    spinner.succeed('✅ Homepage Featured Products created')

    // Step 5: Create/Update Homepage Features
    spinner.text = 'Creating Homepage Features...'
    const homepageFeaturesData = {
      _id: 'homepageFeatures',
      _type: 'homepageFeatures',
      showSection: homepage.featuresSection?.showSection !== false,
      sectionTitle: homepage.featuresSection?.sectionTitle,
      sectionDescription: homepage.featuresSection?.sectionDescription,
      layout: homepage.featuresSection?.layout || 'grid-3',
      features: homepage.featuresSection?.features || [],
      style: {
        cardStyle: 'default',
        iconStyle: 'default',
        animation: true
      }
    }
    
    const existingFeatures = await client.fetch(`*[_id == "homepageFeatures"][0]`)
    if (existingFeatures) {
      await client.patch('homepageFeatures').set(homepageFeaturesData).commit()
    } else {
      await client.create(homepageFeaturesData)
    }
    spinner.succeed('✅ Homepage Features created')

    // Step 6: Create/Update Homepage Brand Story
    spinner.text = 'Creating Homepage Brand Story...'
    const homepageBrandStoryData = {
      _id: 'homepageBrandStory',
      _type: 'homepageBrandStory',
      showSection: homepage.brandStorySection ? true : false,
      title: homepage.brandStorySection?.title || {
        zh: '品牌故事',
        en: 'Brand Story',
        ar: 'قصة العلامة التجارية'
      },
      description: homepage.brandStorySection?.description,
      backgroundImage: homepage.brandStorySection?.backgroundImage,
      showProducts: homepage.brandStorySection?.showProducts || false,
      products: homepage.brandStorySection?.products || [],
      productSettings: homepage.brandStorySection?.productSettings || {
        showPrices: true,
        showCategories: true,
        showBadges: true,
        layout: 'grid'
      }
    }
    
    const existingBrandStory = await client.fetch(`*[_id == "homepageBrandStory"][0]`)
    if (existingBrandStory) {
      await client.patch('homepageBrandStory').set(homepageBrandStoryData).commit()
    } else {
      await client.create(homepageBrandStoryData)
    }
    spinner.succeed('✅ Homepage Brand Story created')

    // Step 7: Create/Update Homepage Stats
    spinner.text = 'Creating Homepage Stats...'
    const homepageStatsData = {
      _id: 'homepageStats',
      _type: 'homepageStats',
      showStats: homepage.statsSection?.showStats !== false,
      sectionTitle: {
        zh: '我们的成就',
        en: 'Our Achievements',
        ar: 'إنجازاتنا'
      },
      stats: homepage.statsSection?.stats || [],
      layout: 'horizontal',
      style: {
        background: 'transparent',
        showDividers: false,
        alignment: 'center',
        numberSize: 'large'
      }
    }
    
    const existingStats = await client.fetch(`*[_id == "homepageStats"][0]`)
    if (existingStats) {
      await client.patch('homepageStats').set(homepageStatsData).commit()
    } else {
      await client.create(homepageStatsData)
    }
    spinner.succeed('✅ Homepage Stats created')

    // Step 8: Create/Update Homepage SEO
    spinner.text = 'Creating Homepage SEO...'
    const homepageSeoData = {
      _id: 'homepageSeo',
      _type: 'homepageSeo',
      metaTitle: homepage.seo?.metaTitle || homepage.title || {
        zh: '动漫周边品牌商城',
        en: 'Anime Merchandise Brand Store',
        ar: 'متجر العلامة التجارية لمنتجات الأنمي'
      },
      metaDescription: homepage.seo?.metaDescription || {
        zh: '精选高品质动漫周边产品，正版授权，全球发货',
        en: 'Premium anime merchandise, officially licensed, worldwide shipping',
        ar: 'منتجات أنمي عالية الجودة، مرخصة رسمياً، شحن عالمي'
      },
      keywords: homepage.seo?.keywords,
      ogTitle: homepage.seo?.ogTitle,
      ogDescription: homepage.seo?.ogDescription,
      ogImage: homepage.seo?.ogImage,
      canonicalUrl: homepage.seo?.canonicalUrl,
      robots: {
        index: true,
        follow: true,
        archive: true,
        snippet: true,
        imageindex: true
      }
    }
    
    const existingSeo = await client.fetch(`*[_id == "homepageSeo"][0]`)
    if (existingSeo) {
      await client.patch('homepageSeo').set(homepageSeoData).commit()
    } else {
      await client.create(homepageSeoData)
    }
    spinner.succeed('✅ Homepage SEO created')

    // Step 9: Display migration summary
    console.log('\n📊 Migration Summary:')
    console.log('  ✅ Homepage Basic - Migrated')
    console.log('  ✅ Homepage Featured Products - Migrated')
    console.log('  ✅ Homepage Features - Migrated')
    console.log('  ✅ Homepage Brand Story - Migrated')
    console.log('  ✅ Homepage Stats - Migrated')
    console.log('  ✅ Homepage SEO - Migrated')
    
    console.log('\n✨ Migration completed successfully!')
    console.log('\n⚠️  Important Notes:')
    console.log('  1. The old homepage document is still intact')
    console.log('  2. All homepage sections are now separate documents')
    console.log('  3. Test the new structure in Sanity Studio')
    console.log('  4. Update frontend queries to fetch from multiple documents:')
    console.log('\n  Example frontend query:')
    console.log(`
    const [basic, featured, features, brandStory, stats, seo] = await Promise.all([
      client.fetch('*[_type == "homepageBasic"][0]'),
      client.fetch('*[_type == "homepageFeaturedProducts"][0]'),
      client.fetch('*[_type == "homepageFeatures"][0]'),
      client.fetch('*[_type == "homepageBrandStory"][0]'),
      client.fetch('*[_type == "homepageStats"][0]'),
      client.fetch('*[_type == "homepageSeo"][0]')
    ])
    `)
    
  } catch (error) {
    spinner.fail('❌ Migration failed')
    console.error('Error details:', error)
    process.exit(1)
  }
}

// Add dry-run option
const isDryRun = process.argv.includes('--dry-run')

if (isDryRun) {
  console.log('🔍 Running in DRY RUN mode - no changes will be made')
}

console.log('🚀 Homepage to Sub-pages Migration Script')
console.log('================================')
console.log(`Project ID: ${projectId}`)
console.log(`Dataset: ${dataset}`)
console.log(`Mode: ${isDryRun ? 'DRY RUN' : 'LIVE'}`)
console.log('================================\n')

// Run migration
migrateHomepageToSubpages()