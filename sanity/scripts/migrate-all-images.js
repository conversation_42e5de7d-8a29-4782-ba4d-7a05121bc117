#!/usr/bin/env node
/**
 * migrate-all-images.js
 *
 * Comprehensive image migration script that converts ALL old image formats to flexibleImage
 * across ALL document types (products, categories, ipSeries, homepage, siteSettings)
 * 
 * Usage:
 *   node scripts/migrate-all-images.js                  # Dry run by default
 *   node scripts/migrate-all-images.js --execute        # Actually perform migration
 *   node scripts/migrate-all-images.js --execute --publish  # Migrate and publish documents
 */

require('dotenv').config()
const {createClient} = require('@sanity/client')
const {customAlphabet} = require('nanoid')
const nanoid = customAlphabet('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz', 10)

// Simple loading spinner replacement
const ora = (text) => {
  console.log(text)
  return {
    start: () => ({ 
      succeed: (text) => { console.log('✅', text) },
      fail: (text) => { console.log('❌', text) }
    }),
    succeed: (text) => { console.log('✅', text) },
    fail: (text) => { console.log('❌', text) }
  }
}

const isDryRun = !process.argv.includes('--execute')
const shouldPublish = process.argv.includes('--publish')

console.log('🚀 Comprehensive Image Migration Tool')
console.log('=====================================')
console.log(`Mode: ${isDryRun ? 'DRY RUN (no changes will be made)' : 'EXECUTE'}`)
console.log(`Publish after migration: ${shouldPublish ? 'YES' : 'NO'}`)
console.log()

/* --- Sanity Client -------------------------------------------------- */
const client = createClient({
  projectId : process.env.SANITY_STUDIO_PROJECT_ID || '4za4x22i',
  dataset   : process.env.SANITY_STUDIO_DATASET     || 'production',
  token     : process.env.SANITY_WRITE_TOKEN,
  apiVersion: new Date().toISOString().slice(0,10),   // YYYY-MM-DD
  useCdn    : false
})

if (!client.config().token) {
  console.error('❌ 需要 SANITY_WRITE_TOKEN（Editor / Developer 权限）')
  console.error('   请在 .env 文件中设置 SANITY_WRITE_TOKEN')
  process.exit(1)
}

/* --- 把一张旧图改成 flexibleImage ----------------------------------- */
function migrateImage(item) {
  // Handle null/undefined
  if (!item) return null
  
  // If it's already a proper flexibleImage with all fields, return as-is
  if (item._type === 'flexibleImage' && item.imageType && 
      typeof item.alt !== 'undefined' && typeof item.caption !== 'undefined') {
    return item
  }
  
  // Handle old format: direct reference or object with asset
  if (item._ref || item.asset || (item._type === 'image')) {
    const imageAsset = item._ref ? {_type: 'reference', _ref: item._ref} : 
                      item.asset ? item.asset : 
                      item
    
    return {
      _key: item._key || nanoid(),
      _type: 'flexibleImage',
      imageType: 'upload',
      uploadedImage: {
        _type: 'image',
        asset: imageAsset,
        crop: item.crop,
        hotspot: item.hotspot
      },
      alt: item.alt || '',
      caption: item.caption || ''
    }
  }
  
  // Handle incomplete flexibleImage
  if (item._type === 'flexibleImage') {
    const imageType = item.imageType || 'upload'
    
    // For upload type, ensure uploadedImage exists
    if (imageType === 'upload') {
      let uploadedImage = item.uploadedImage
      
      // If uploadedImage is missing, try to recover from legacy asset field
      if (!uploadedImage && item.asset) {
        uploadedImage = {
          _type: 'image',
          asset: item.asset,
          crop: item.crop,
          hotspot: item.hotspot
        }
      }
      
      // If still no uploadedImage and we have a direct _ref, try that
      if (!uploadedImage && item._ref) {
        uploadedImage = {
          _type: 'image',
          asset: {_type: 'reference', _ref: item._ref},
          crop: item.crop,
          hotspot: item.hotspot
        }
      }
      
      // If we still can't find valid image data, this item is broken
      if (!uploadedImage || !uploadedImage.asset) {
        console.warn(`⚠️  Removing broken flexibleImage with key: ${item._key || 'no-key'}`)
        return null // This will be filtered out by migrateImageArray
      }
      
      return {
        ...item,
        imageType,
        uploadedImage,
        alt: item.alt || '',
        caption: item.caption || ''
      }
    }
    
    // For external type, ensure externalUrl exists
    if (imageType === 'external') {
      if (!item.externalUrl) {
        console.warn(`⚠️  Removing broken external flexibleImage with key: ${item._key || 'no-key'}`)
        return null
      }
      
      return {
        ...item,
        imageType,
        alt: item.alt || '',
        caption: item.caption || ''
      }
    }
    
    // Default fallback
    return {
      ...item,
      imageType,
      alt: item.alt || '',
      caption: item.caption || ''
    }
  }
  
  return item
}

/* --- 迁移数组中的图片 ----------------------------------------------- */
function migrateImageArray(arr) {
  if (!Array.isArray(arr)) return arr
  return arr.map(migrateImage).filter(Boolean)
}

/* --- 检查单个文档的图片字段 ----------------------------------------- */
function getDocumentImageUpdates(doc) {
  const updates = {}
  let hasChanges = false
  
  // Product: gallery only (mainImage is now gallery[0])
  if (doc._type === 'product') {
    if (doc.gallery && Array.isArray(doc.gallery)) {
      const migrated = migrateImageArray(doc.gallery)
      if (JSON.stringify(migrated) !== JSON.stringify(doc.gallery)) {
        updates.gallery = migrated
        hasChanges = true
      }
    }
  }
  
  // Category: image
  if (doc._type === 'category' && doc.image) {
    const migrated = migrateImage(doc.image)
    if (JSON.stringify(migrated) !== JSON.stringify(doc.image)) {
      updates.image = migrated
      hasChanges = true
    }
  }
  
  // IP Series: logo and bannerImage
  if (doc._type === 'ipSeries') {
    if (doc.logo) {
      const migrated = migrateImage(doc.logo)
      if (JSON.stringify(migrated) !== JSON.stringify(doc.logo)) {
        updates.logo = migrated
        hasChanges = true
      }
    }
    
    if (doc.bannerImage) {
      const migrated = migrateImage(doc.bannerImage)
      if (JSON.stringify(migrated) !== JSON.stringify(doc.bannerImage)) {
        updates.bannerImage = migrated
        hasChanges = true
      }
    }
  }
  
  // Homepage: brandStorySection.backgroundImage
  if (doc._type === 'homepage' && doc.brandStorySection?.backgroundImage) {
    const migrated = migrateImage(doc.brandStorySection.backgroundImage)
    if (JSON.stringify(migrated) !== JSON.stringify(doc.brandStorySection.backgroundImage)) {
      updates['brandStorySection.backgroundImage'] = migrated
      hasChanges = true
    }
  }
  
  // Site Settings: logo and favicon
  if (doc._type === 'siteSettings') {
    if (doc.logo) {
      const migrated = migrateImage(doc.logo)
      if (JSON.stringify(migrated) !== JSON.stringify(doc.logo)) {
        updates.logo = migrated
        hasChanges = true
      }
    }
    
    if (doc.favicon) {
      const migrated = migrateImage(doc.favicon)
      if (JSON.stringify(migrated) !== JSON.stringify(doc.favicon)) {
        updates.favicon = migrated
        hasChanges = true
      }
    }
  }
  
  // About Page: heroSection.heroImage and sections images
  if (doc._type === 'aboutPage') {
    if (doc.heroSection?.heroImage) {
      const migrated = migrateImage(doc.heroSection.heroImage)
      if (JSON.stringify(migrated) !== JSON.stringify(doc.heroSection.heroImage)) {
        updates['heroSection.heroImage'] = migrated
        hasChanges = true
      }
    }
    
    if (doc.sections && Array.isArray(doc.sections)) {
      const migratedSections = doc.sections.map(section => {
        const sectionCopy = {...section}
        
        // Handle imageTextSection images
        if (section._type === 'imageTextSection' && section.image) {
          const migrated = migrateImage(section.image)
          if (JSON.stringify(migrated) !== JSON.stringify(section.image)) {
            sectionCopy.image = migrated
          }
        }
        
        // Handle teamSection member photos
        if (section._type === 'teamSection' && section.teamMembers && Array.isArray(section.teamMembers)) {
          sectionCopy.teamMembers = section.teamMembers.map(member => {
            if (member.photo) {
              const migrated = migrateImage(member.photo)
              if (JSON.stringify(migrated) !== JSON.stringify(member.photo)) {
                return {...member, photo: migrated}
              }
            }
            return member
          })
        }
        
        return sectionCopy
      })
      
      if (JSON.stringify(migratedSections) !== JSON.stringify(doc.sections)) {
        updates.sections = migratedSections
        hasChanges = true
      }
    }
  }
  
  return hasChanges ? updates : null
}

/* --- 查询需要迁移的文档 --------------------------------------------- */
async function findDocumentsNeedingMigration() {
  const spinner = ora('🔍 Analyzing documents...').start()
  
  try {
    // Query all document types that might have images
    const query = `{
      "products": *[_type == "product"] {
        _id, _rev, _type, gallery
      },
      "categories": *[_type == "category"] {
        _id, _rev, _type, image
      },
      "ipSeries": *[_type == "ipSeries"] {
        _id, _rev, _type, logo, bannerImage
      },
      "homepage": *[_type == "homepage"] {
        _id, _rev, _type, brandStorySection
      },
      "siteSettings": *[_type == "siteSettings"] {
        _id, _rev, _type, logo, favicon
      },
      "aboutPage": *[_type == "aboutPage"] {
        _id, _rev, _type, heroSection, sections
      }
    }`
    
    const results = await client.fetch(query)
    
    const documentsToMigrate = []
    const stats = {
      products: 0,
      categories: 0,
      ipSeries: 0,
      homepage: 0,
      siteSettings: 0,
      aboutPage: 0,
      totalImages: 0
    }
    
    // Check each document type
    for (const [type, docs] of Object.entries(results)) {
      for (const doc of docs) {
        const updates = getDocumentImageUpdates(doc)
        if (updates) {
          documentsToMigrate.push({ doc, updates })
          stats[type]++
          
          // Count images
          if (type === 'products') {
            if (updates.gallery) stats.totalImages += updates.gallery.length
          } else if (type === 'ipSeries') {
            if (updates.logo) stats.totalImages++
            if (updates.bannerImage) stats.totalImages++
          } else if (type === 'siteSettings') {
            if (updates.logo) stats.totalImages++
            if (updates.favicon) stats.totalImages++
          } else if (type === 'aboutPage') {
            if (updates['heroSection.heroImage']) stats.totalImages++
            if (updates.sections) {
              // Count images in sections
              updates.sections.forEach(section => {
                if (section.image) stats.totalImages++
                if (section.teamMembers) {
                  section.teamMembers.forEach(member => {
                    if (member.photo) stats.totalImages++
                  })
                }
              })
            }
          } else {
            stats.totalImages++
          }
        }
      }
    }
    
    spinner.succeed('Analysis complete!')
    
    return { documentsToMigrate, stats }
  } catch (error) {
    spinner.fail('Failed to analyze documents')
    throw error
  }
}

/* --- 执行迁移 ------------------------------------------------------- */
async function performMigration(documentsToMigrate) {
  let migrated = 0
  let failed = 0
  
  for (const { doc, updates } of documentsToMigrate) {
    const displayName = `${doc._type}:${doc._id.slice(-6)}`
    
    if (isDryRun) {
      console.log(`\n📋 Would update ${displayName}:`)
      console.log(JSON.stringify(updates, null, 2))
      migrated++
      continue
    }
    
    try {
      // Perform the update
      const transaction = client
        .patch(doc._id)
        .ifRevisionId(doc._rev)
        .set(updates)
      
      // Publish if requested
      if (shouldPublish) {
        await transaction.commit({ publish: true })
        console.log(`✅ Migrated and published: ${displayName}`)
      } else {
        await transaction.commit()
        console.log(`✅ Migrated: ${displayName}`)
      }
      
      migrated++
      
      // Small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 100))
    } catch (error) {
      console.error(`❌ Failed to migrate ${displayName}:`, error.message)
      failed++
    }
  }
  
  return { migrated, failed }
}

/* --- 主流程 ---------------------------------------------------------- */
(async () => {
  try {
    console.log('📊 Dataset:', client.config().dataset)
    console.log()
    
    // Find documents needing migration
    const { documentsToMigrate, stats } = await findDocumentsNeedingMigration()
    
    console.log('\n📊 Migration Summary:')
    console.log('====================')
    console.log(`Products:      ${stats.products} documents`)
    console.log(`Categories:    ${stats.categories} documents`)
    console.log(`IP Series:     ${stats.ipSeries} documents`)
    console.log(`Homepage:      ${stats.homepage} documents`)
    console.log(`Site Settings: ${stats.siteSettings} documents`)
    console.log(`About Page:    ${stats.aboutPage} documents`)
    console.log(`-----------------------`)
    console.log(`Total documents: ${documentsToMigrate.length}`)
    console.log(`Total images:    ${stats.totalImages}`)
    
    if (documentsToMigrate.length === 0) {
      console.log('\n✨ All images are already migrated! No action needed.')
      process.exit(0)
    }
    
    if (isDryRun) {
      console.log('\n🔍 Running in DRY RUN mode. Showing what would be changed...')
    } else {
      console.log('\n⚠️  This will modify your production data!')
      console.log('Press Ctrl+C within 5 seconds to cancel...')
      await new Promise(resolve => setTimeout(resolve, 5000))
    }
    
    // Perform migration
    const { migrated, failed } = await performMigration(documentsToMigrate)
    
    // Final summary
    console.log('\n🎉 Migration Complete!')
    console.log('=====================')
    console.log(`Successfully migrated: ${migrated} documents`)
    if (failed > 0) {
      console.log(`Failed: ${failed} documents`)
    }
    
    if (isDryRun) {
      console.log('\n📌 This was a DRY RUN. To actually perform the migration, run:')
      console.log('   node scripts/migrate-all-images.js --execute')
      if (!shouldPublish) {
        console.log('\n📌 To also publish documents after migration:')
        console.log('   node scripts/migrate-all-images.js --execute --publish')
      }
    } else if (!shouldPublish) {
      console.log('\n📌 Documents have been migrated but NOT published.')
      console.log('   They will remain as drafts until manually published.')
    }
    
    process.exit(failed > 0 ? 1 : 0)
  } catch (error) {
    console.error('\n💥 Fatal error:', error)
    process.exit(1)
  }
})()