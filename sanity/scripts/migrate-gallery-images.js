#!/usr/bin/env node
/**
 * migrate-gallery-images.one-by-one.js
 *
 * 将旧 image → flexibleImage，逐条 patch & commit。
 * 用法：
 *   node scripts/migrate-gallery-images.one-by-one.js
 *   # dry-run 只看改动
 *   node scripts/migrate-gallery-images.one-by-one.js --dry
 */

require('dotenv').config()
const {createClient} = require('@sanity/client')
const {customAlphabet} = require('nanoid')
const nanoid = customAlphabet('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz', 10)

const argv = process.argv.includes('--dry')

/* --- Sanity Client -------------------------------------------------- */
const client = createClient({
  projectId : process.env.SANITY_STUDIO_PROJECT_ID || '4za4x22i',
  dataset   : process.env.SANITY_STUDIO_DATASET     || 'production',
  token     : process.env.SANITY_WRITE_TOKEN,
  apiVersion: new Date().toISOString().slice(0,10),   // YYYY-MM-DD
  useCdn    : false
})

if (!client.config().token) {
  console.error('❌ 需要 SANITY_WRITE_TOKEN（Editor / Developer 权限）')
  process.exit(1)
}

/* --- 把一张旧图改成 flexibleImage ----------------------------------- */
function migrateItem(item) {
  // ① 旧裸 image → flexibleImage
  if ((item._ref || item.asset) && !item.imageType) {
    return {
      _key : item._key || nanoid(),
      _type: 'flexibleImage',
      imageType: 'upload',
      uploadedImage: {
        _type: 'image',
        asset: item.asset || item,       // 兼容两种旧写法
        crop:  item.crop,
        hotspot: item.hotspot
      },
      alt    : item.alt    || '',
      caption: item.caption|| ''
    }
  }
  // ② 已是 flexibleImage，但字段可能缺失
  if (item._type === 'flexibleImage') {
    return {
      ...item,
      imageType: item.imageType || 'upload',
      alt     : item.alt     || '',
      caption : item.caption || ''
    }
  }
  return item
}

/* --- 主流程 ---------------------------------------------------------- */
(async () => {
  console.log('🔍 查找需要迁移的产品…')
  const products = await client.fetch(`
    *[
      _type == "product" &&
      defined(gallery[0]) &&
      (
        gallery[0]._type != "flexibleImage" ||
        !defined(gallery[0].imageType) ||
        !defined(gallery[0].alt) ||
        !defined(gallery[0].caption)
      )
    ]{
      _id, _rev, gallery
    }`)

  console.log(`📦 共 ${products.length} 个产品待检查\n`)

  let migrated = 0, skipped = 0

  for (const prod of products) {
    const newGallery = prod.gallery.map(migrateItem)
    const changed = JSON.stringify(newGallery) !== JSON.stringify(prod.gallery)

    if (!changed) {
      console.log(`⏭️  ${prod._id} 已符合规范`)
      skipped++
      continue
    }

    if (argv) {               // --dry 模式
      console.log('--- PATCH', prod._id, '---')
      console.dir({gallery: newGallery}, {depth: 5})
      migrated++
      continue
    }

    await client
        .patch(prod._id)
        .ifRevisionId(prod._rev)   // 乐观锁，避免并发冲突
        .set({gallery: newGallery})
        .commit()

    console.log(`✅ 已迁移 ${prod._id}`)
    migrated++
  }

  console.log('\n🎉 迁移完毕')
  console.log(`   成功迁移: ${migrated}`)
  console.log(`   已是最新: ${skipped}`)
  if (argv) console.log('[Dry-Run] 未写入任何数据')
  process.exit(0)
})().catch(err => {
  console.error('💥 Fatal:', err)
  process.exit(1)
})
