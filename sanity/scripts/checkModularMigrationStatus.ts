#!/usr/bin/env node
import {createClient} from '@sanity/client'
import dotenv from 'dotenv'
import ora from 'ora'

dotenv.config({path: '.env.local'})
dotenv.config()

const client = createClient({
  projectId: process.env.SANITY_STUDIO_PROJECT_ID || '4za4x22i',
  dataset: process.env.SANITY_STUDIO_DATASET || 'production',
  apiVersion: '2024-01-01',
  token: process.env.SANITY_WRITE_TOKEN || process.env.SANITY_API_TOKEN,
  useCdn: false,
})

async function checkMigrationStatus() {
  const spinner = ora('检查模块化迁移状态...').start()

  try {
    const data = await client.fetch(`{
      "homepageBasic": *[_type == "homepageBasic"][0] {
        _id,
        featuresSection,
        brandStorySection,
        statsSection
      },
      "homepage": *[_type == "homepage"][0] {
        _id,
        brandStorySection,
        featuresSection,
        statsSection
      },
      "modularDocs": {
        "brandStory": *[_type == "homepageBrandStory"][0] { _id },
        "features": *[_type == "homepageFeatures"][0] { _id },
        "stats": *[_type == "homepageStats"][0] { _id },
        "seo": *[_type == "homepageSeo"][0] { _id },
        "featuredProducts": *[_type == "homepageFeaturedProducts"][0] { _id }
      }
    }`)

    spinner.succeed('状态检查完成')

    console.log('\n📊 当前状态:')
    console.log('================================')
    
    // 检查内嵌配置
    const sourceDoc = data.homepageBasic || data.homepage
    console.log('\n🏠 Homepage 内嵌配置:')
    console.log(`  - 数据源: ${data.homepageBasic ? 'homepageBasic' : data.homepage ? 'homepage' : '无'}`)
    console.log(`  - brandStorySection: ${sourceDoc?.brandStorySection ? '✓ 存在' : '✗ 不存在'}`)
    console.log(`  - featuresSection: ${sourceDoc?.featuresSection ? '✓ 存在' : '✗ 不存在'}`)
    console.log(`  - statsSection: ${sourceDoc?.statsSection ? '✓ 存在' : '✗ 不存在'}`)

    // 检查独立文档
    console.log('\n📄 独立模块文档:')
    console.log(`  - homepageBrandStory: ${data.modularDocs.brandStory ? '✓ 存在' : '✗ 不存在'}`)
    console.log(`  - homepageFeatures: ${data.modularDocs.features ? '✓ 存在' : '✗ 不存在'}`)
    console.log(`  - homepageStats: ${data.modularDocs.stats ? '✓ 存在' : '✗ 不存在'}`)
    console.log(`  - homepageSeo: ${data.modularDocs.seo ? '✓ 存在' : '✗ 不存在'}`)
    console.log(`  - homepageFeaturedProducts: ${data.modularDocs.featuredProducts ? '✓ 存在' : '✗ 不存在'}`)

    // 迁移建议
    console.log('\n💡 迁移建议:')
    const needsMigration = sourceDoc?.brandStorySection || sourceDoc?.featuresSection || sourceDoc?.statsSection
    if (needsMigration) {
      console.log('  ⚠️  发现内嵌配置，建议执行迁移')
      console.log('  📝 运行: npm run migrate:to-modular')
    } else {
      console.log('  ✅ 所有配置已模块化，可以清理 schema')
    }

    return data
  } catch (error) {
    spinner.fail('检查失败')
    console.error('Error:', error)
    throw error
  }
}

if (require.main === module) {
  checkMigrationStatus()
}

export { checkMigrationStatus }
