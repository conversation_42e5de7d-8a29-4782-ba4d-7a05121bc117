#!/usr/bin/env node
import {createClient} from '@sanity/client'
import dotenv from 'dotenv'
import ora from 'ora'

dotenv.config({path: '.env.local'})
dotenv.config()

const client = createClient({
  projectId: process.env.SANITY_STUDIO_PROJECT_ID || '4za4x22i',
  dataset: process.env.SANITY_STUDIO_DATASET || 'production',
  apiVersion: '2024-01-01',
  token: process.env.SANITY_WRITE_TOKEN || process.env.SANITY_API_TOKEN,
  useCdn: false,
})

const isDryRun = process.argv.includes('--dry-run')

async function migrateToModular() {
  const spinner = ora('开始模块化迁移...').start()

  try {
    // 1. 获取当前数据 - 优先从 homepageBasic 获取，fallback 到 homepage
    spinner.text = '获取当前数据...'
    const data = await client.fetch(`{
      "homepageBasic": *[_type == "homepageBasic"][0],
      "homepage": *[_type == "homepage"][0],
      "featuredProducts": *[_type == "featuredProducts"][0]
    }`)
    
    const sourceDoc = data.homepageBasic || data.homepage
    if (!sourceDoc) {
      spinner.warn('未找到 homepage 或 homepageBasic 文档')
      return
    }

    console.log(`\n📄 数据源: ${data.homepageBasic ? 'homepageBasic' : 'homepage'}`)

    // 2. 创建备份
    if (!isDryRun) {
      spinner.text = '创建备份...'
      await client.create({
        _type: 'backup',
        _id: `modular-migration-backup-${Date.now()}`,
        title: '模块化迁移备份',
        data: sourceDoc,
        reason: '迁移到模块化架构前的备份',
        timestamp: new Date().toISOString()
      })
    }

    const migrations = []

    // 3. 迁移品牌故事
    if (sourceDoc.brandStorySection) {
      spinner.text = '迁移品牌故事...'
      const brandStoryDoc = {
        _type: 'homepageBrandStory',
        _id: 'homepage-brand-story',
        showSection: sourceDoc.brandStorySection.showSection !== false,
        ...sourceDoc.brandStorySection
      }
      
      if (!isDryRun) {
        await client.createOrReplace(brandStoryDoc)
      }
      migrations.push('✓ 品牌故事已迁移')
    }

    // 4. 迁移特色功能
    if (sourceDoc.featuresSection) {
      spinner.text = '迁移特色功能...'
      const featuresDoc = {
        _type: 'homepageFeatures',
        _id: 'homepage-features',
        showSection: sourceDoc.featuresSection.showSection !== false,
        ...sourceDoc.featuresSection
      }
      
      if (!isDryRun) {
        await client.createOrReplace(featuresDoc)
      }
      migrations.push('✓ 特色功能已迁移')
    }

    // 5. 迁移统计数据
    if (sourceDoc.statsSection) {
      spinner.text = '迁移统计数据...'
      const statsDoc = {
        _type: 'homepageStats',
        _id: 'homepage-stats',
        showStats: sourceDoc.statsSection.showStats !== false,
        ...sourceDoc.statsSection
      }
      
      if (!isDryRun) {
        await client.createOrReplace(statsDoc)
      }
      migrations.push('✓ 统计数据已迁移')
    }

    // 6. 迁移精选产品（如果存在独立的 featuredProducts 文档）
    if (data.featuredProducts) {
      spinner.text = '迁移精选产品...'
      const featuredProductsDoc = {
        _type: 'homepageFeaturedProducts',
        _id: 'homepage-featured-products',
        showSection: data.featuredProducts.isActive !== false,
        sectionTitle: data.featuredProducts.title,
        sectionSubtitle: data.featuredProducts.subtitle,
        products: data.featuredProducts.products,
        displaySettings: data.featuredProducts.displaySettings,
        layoutSettings: data.featuredProducts.layout
      }
      
      if (!isDryRun) {
        await client.createOrReplace(featuredProductsDoc)
      }
      migrations.push('✓ 精选产品已迁移')
    }

    // 7. 创建默认 SEO 文档（如果不存在）
    spinner.text = '检查 SEO 配置...'
    const existingSeo = await client.fetch(`*[_type == "homepageSeo"][0]`)
    if (!existingSeo) {
      const seoDoc = {
        _type: 'homepageSeo',
        _id: 'homepage-seo',
        title: sourceDoc.seo?.title || sourceDoc.title,
        description: sourceDoc.seo?.description,
        keywords: sourceDoc.seo?.keywords,
        ogImage: sourceDoc.seo?.ogImage
      }
      
      if (!isDryRun) {
        await client.createOrReplace(seoDoc)
      }
      migrations.push('✓ SEO 配置已创建')
    }

    // 8. 清理内嵌字段
    if (!isDryRun && migrations.length > 0) {
      spinner.text = '清理内嵌字段...'
      await client.patch(sourceDoc._id)
        .unset(['brandStorySection', 'featuresSection', 'statsSection'])
        .commit()
    }

    spinner.succeed(`${isDryRun ? '预览' : '迁移'}完成！`)

    console.log('\n📊 迁移结果:')
    console.log('================================')
    migrations.forEach(msg => console.log(`  ${msg}`))
    
    if (isDryRun) {
      console.log('\n⚠️  这是预览模式，没有实际修改数据')
      console.log('执行实际迁移: npm run migrate:to-modular')
    } else {
      console.log('\n✅ 迁移完成！')
      console.log('\n📝 下一步:')
      console.log('  1. 更新前端查询逻辑')
      console.log('  2. 清理 schema 定义')
      console.log('  3. 测试前端显示')
    }

  } catch (error) {
    spinner.fail('迁移失败')
    console.error('Error:', error)
    process.exit(1)
  }
}

console.log(`🚀 模块化迁移脚本 ${isDryRun ? '(预览模式)' : ''}`)
console.log('================================\n')

migrateToModular()
