import { getCliClient } from 'sanity/cli'

const client = getCliClient()

async function debugImageData() {
  console.log('🔍 调试图片数据结构...')
  
  try {
    const products = await client.fetch(`
      *[_type == "product" && isPublished == true][0...5] {
        _id,
        name,
        "firstGalleryImage": gallery[0],
        "galleryLength": length(gallery),
        gallery[0...2] {
          _key,
          imageType,
          imageType == "upload" => {
            "hasUploadedImage": defined(uploadedImage),
            "hasAssetRef": defined(uploadedImage.asset._ref),
            "assetRef": uploadedImage.asset._ref
          },
          imageType == "external" => {
            "hasExternalUrl": defined(externalUrl),
            "externalUrl": externalUrl
          }
        }
      }
    `)
    
    console.log(`\n找到 ${products.length} 个产品进行调试:`)
    
    products.forEach((product, index) => {
      console.log(`\n=== 产品 ${index + 1} ===`)
      console.log(`ID: ${product._id}`)
      console.log(`名称: ${product.name?.zh || '未命名'}`)
      console.log(`Gallery 长度: ${product.galleryLength}`)
      
      if (product.firstGalleryImage) {
        console.log(`第一张图片:`)
        console.log(`  - 类型: ${product.firstGalleryImage.imageType}`)
        console.log(`  - _key: ${product.firstGalleryImage._key || 'NO KEY'}`)
        
        if (product.firstGalleryImage.imageType === 'upload') {
          console.log(`  - 有 uploadedImage: ${product.firstGalleryImage.hasUploadedImage}`)
          console.log(`  - 有 asset._ref: ${product.firstGalleryImage.hasAssetRef}`)
          console.log(`  - asset._ref: ${product.firstGalleryImage.assetRef || 'NO REF'}`)
        } else if (product.firstGalleryImage.imageType === 'external') {
          console.log(`  - 有 externalUrl: ${product.firstGalleryImage.hasExternalUrl}`)
          console.log(`  - externalUrl: ${product.firstGalleryImage.externalUrl || 'NO URL'}`)
        }
      } else {
        console.log(`❌ 第一张图片为空!`)
      }
      
      console.log(`Gallery 数组前2项:`)
      product.gallery?.forEach((img, idx) => {
        console.log(`  [${idx}] 类型: ${img.imageType}, key: ${img._key || 'NO KEY'}`)
      })
    })
    
  } catch (error) {
    console.error('❌ 调试失败:', error)
  }
}

debugImageData()