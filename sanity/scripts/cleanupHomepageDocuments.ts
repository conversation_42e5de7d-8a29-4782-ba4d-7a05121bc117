import { client } from '@sanity/client'
import { getCliClient } from 'sanity/cli'

// 连接到Sanity客户端
const sanityClient = getCliClient()

// 清理旧的首页文档配置
async function cleanupOldHomepageDocuments() {
  console.log('🧹 开始清理旧的首页文档...')
  
  try {
    // 1. 查询所有需要清理的文档
    const documentsToCleanup = await sanityClient.fetch(`
      {
        "homepage": *[_type == "homepage"] { _id, _type, _updatedAt },
        "homepageV2": *[_type == "homepageV2"] { _id, _type, _updatedAt }
      }
    `)
    
    console.log('📋 发现的文档:')
    console.log('- homepage 文档:', documentsToCleanup.homepage?.length || 0)
    console.log('- homepageV2 文档:', documentsToCleanup.homepageV2?.length || 0)
    
    // 2. 备份重要数据（以防需要恢复）
    if (documentsToCleanup.homepage?.length > 0 || documentsToCleanup.homepageV2?.length > 0) {
      const backupData = {
        timestamp: new Date().toISOString(),
        homepage: documentsToCleanup.homepage,
        homepageV2: documentsToCleanup.homepageV2
      }
      
      // 将备份数据保存到备份文档
      await sanityClient.create({
        _type: 'backup',
        _id: `homepage-cleanup-backup-${Date.now()}`,
        title: '首页文档清理备份',
        data: backupData,
        reason: '清理重复的首页配置前的备份',
      })
      
      console.log('💾 已创建备份文档')
    }
    
    // 3. 删除旧的 homepage 文档
    if (documentsToCleanup.homepage?.length > 0) {
      for (const doc of documentsToCleanup.homepage) {
        await sanityClient.delete(doc._id)
        console.log(`🗑️  已删除 homepage 文档: ${doc._id}`)
      }
    }
    
    // 4. 删除 homepageV2 文档
    if (documentsToCleanup.homepageV2?.length > 0) {
      for (const doc of documentsToCleanup.homepageV2) {
        await sanityClient.delete(doc._id)
        console.log(`🗑️  已删除 homepageV2 文档: ${doc._id}`)
      }
    }
    
    // 5. 验证清理结果
    const remainingDocs = await sanityClient.fetch(`
      *[_type match "homepage*" && !(_type match "*Basic" || _type match "*Features" || _type match "*Stats" || _type match "*Seo" || _type match "*BrandStory" || _type match "*FeaturedProducts")] {
        _id, _type
      }
    `)
    
    if (remainingDocs.length === 0) {
      console.log('✅ 清理完成！所有旧的首页文档已删除')
      console.log('📋 当前使用模块化结构的首页配置:')
      console.log('   - homepageBasic (基础设置)')
      console.log('   - homepageFeatures (特色功能)')
      console.log('   - homepageBrandStory (品牌故事)')
      console.log('   - homepageStats (数据展示)')
      console.log('   - homepageSeo (SEO设置)')
      console.log('   - homepageFeaturedProducts (精选产品)')
    } else {
      console.warn('⚠️  仍有未清理的文档:', remainingDocs)
    }
    
  } catch (error) {
    console.error('❌ 清理过程中发生错误:', error)
    throw error
  }
}

// 如果直接运行此脚本则执行清理
if (require.main === module) {
  cleanupOldHomepageDocuments()
    .then(() => {
      console.log('🎉 首页文档清理完成！')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 清理失败:', error)
      process.exit(1)
    })
}

export default cleanupOldHomepageDocuments
