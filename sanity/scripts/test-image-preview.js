/**
 * Test script to verify image preview functionality
 * This script checks if products with different image types can be properly loaded
 */
import {createClient} from '@sanity/client'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config()

const client = createClient({
  projectId: process.env.SANITY_STUDIO_PROJECT_ID || '4za4x22i',
  dataset: process.env.SANITY_STUDIO_DATASET || 'production',
  apiVersion: '2024-01-01',
  useCdn: false
})

async function testImagePreview() {
  console.log('🔍 Testing product image preview functionality...\n')

  try {
    // Query products with their image data
    const products = await client.fetch(`
      *[_type == "product"][0...10] {
        _id,
        name,
        gallery[] {
          imageType,
          uploadedImage {
            asset-> {
              _id,
              url
            },
            alt
          },
          externalUrl,
          alt,
          asset-> {
            _id,
            url
          }
        }
      }
    `)

    console.log(`📦 Found ${products.length} products\n`)

    products.forEach((product, index) => {
      console.log(`${index + 1}. ${product.name?.zh || product.name || 'Unnamed Product'}`)
      console.log(`   ID: ${product._id}`)
      
      if (product.gallery && product.gallery[0]) {
        const mainImage = product.gallery[0]
        
        if (mainImage.imageType === 'upload' && mainImage.uploadedImage) {
          console.log(`   📸 Uploaded Image: ${mainImage.uploadedImage.asset?.url || 'No asset URL'}`)
        } else if (mainImage.imageType === 'external' && mainImage.externalUrl) {
          console.log(`   🔗 External Image: ${mainImage.externalUrl}`)
        } else {
          console.log(`   ❌ Invalid image configuration`)
          console.log(`      Type: ${mainImage.imageType}`)
          console.log(`      Has uploadedImage: ${!!mainImage.uploadedImage}`)
          console.log(`      Has externalUrl: ${!!mainImage.externalUrl}`)
        }
      } else {
        console.log(`   ❌ No gallery images`)
      }
      console.log()
    })

    // Test categories for navigation
    console.log('📂 Testing category navigation...')
    const categories = await client.fetch(`
      *[_type == "category"][0...5] {
        _id,
        name,
        "productCount": count(*[_type == "product" && category._ref == ^._id])
      }
    `)

    categories.forEach(category => {
      console.log(`   ${category.name?.zh || category.name || 'Unnamed Category'}: ${category.productCount} products`)
    })

    console.log('\n✅ Image preview test completed successfully!')

  } catch (error) {
    console.error('❌ Error testing image preview:', error)
    process.exit(1)
  }
}

testImagePreview()