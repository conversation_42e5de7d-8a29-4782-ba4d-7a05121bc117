import { getCliClient } from 'sanity/cli'

const client = getCliClient()

async function testFixedQueries() {
  console.log('🔍 测试修复后的查询...')
  
  try {
    // 测试产品查询
    const products = await client.fetch(`
      *[_type == "product" && isPublished == true][0...2] {
        _id,
        name,
        "mainImage": gallery[0] {
          imageType,
          imageType == "upload" => {
            "uploadedImage": uploadedImage {
              asset,
              alt,
              caption
            }
          },
          imageType == "external" => {
            externalUrl,
            alt,
            caption,
            fallbackImage
          }
        }
      }
    `)
    
    console.log(`\n找到 ${products.length} 个产品:`)
    
    products.forEach((product, index) => {
      console.log(`\n=== 产品 ${index + 1} ===`)
      console.log(`名称: ${product.name?.zh || '未命名'}`)
      
      if (product.mainImage) {
        console.log(`✅ 主图数据结构:`)
        console.log(`  - 类型: ${product.mainImage.imageType}`)
        
        if (product.mainImage.imageType === 'upload' && product.mainImage.uploadedImage) {
          console.log(`  - ✅ 有 uploadedImage`)
          console.log(`  - ✅ 有 asset: ${!!product.mainImage.uploadedImage.asset}`)
          console.log(`  - ✅ 有 asset._ref: ${!!product.mainImage.uploadedImage.asset?._ref}`)
          console.log(`  - Asset ref: ${product.mainImage.uploadedImage.asset?._ref?.substring(0, 20)}...`)
        } else if (product.mainImage.imageType === 'external') {
          console.log(`  - ✅ 外部URL: ${product.mainImage.externalUrl}`)
        } else {
          console.log(`  - ❌ 缺少图片数据`)
        }
      } else {
        console.log(`❌ 没有主图数据`)
      }
    })
    
    console.log('\n✅ 查询测试完成')
    
  } catch (error) {
    console.error('❌ 测试失败:', error)
  }
}

testFixedQueries()