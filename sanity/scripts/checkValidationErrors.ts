import {createClient} from '@sanity/client'

const client = createClient({
  projectId: process.env.SANITY_STUDIO_PROJECT_ID || '4za4x22i',
  dataset: process.env.SANITY_STUDIO_DATASET || 'staging',
  apiVersion: '2024-01-01',
  token: process.env.SANITY_STUDIO_API_TOKEN,
  useCdn: false
})

async function checkValidationErrors() {
  console.log('🔍 检查可能的验证错误...\n')
  
  try {
    // 1. 检查有问题的产品
    console.log('1️⃣ 检查产品数据完整性...')
    const allProducts = await client.fetch(`
      *[_type == "product"] {
        _id,
        name,
        "hasChineseName": defined(name.zh),
        "hasSlug": defined(slug.current),
        "hasCategory": defined(category._ref),
        "galleryCount": count(gallery),
        "hasPrice": defined(price) && price > 0,
        price
      }
    `)
    
    const problemProducts = allProducts.filter((product: any) => {
      const issues = []
      if (!product.hasChineseName) issues.push('缺少中文名称')
      if (!product.hasSlug) issues.push('缺少URL标识')
      if (!product.hasCategory) issues.push('缺少分类')
      if (product.galleryCount === 0) issues.push('没有图片')
      if (!product.hasPrice) issues.push('价格无效')
      
      if (issues.length > 0) {
        product.issues = issues
        return true
      }
      return false
    })
    
    if (problemProducts.length > 0) {
      console.log(`❌ 发现 ${problemProducts.length} 个有问题的产品:`)
      problemProducts.forEach((product: any) => {
        console.log(`  - ${product.name?.zh || product._id}: ${product.issues.join(', ')}`)
      })
    } else {
      console.log('✅ 所有产品数据完整')
    }
    
    // 2. 检查描述字段
    console.log('\n2️⃣ 检查富文本描述字段...')
    const descriptionIssues = await client.fetch(`
      *[_type in ["product", "category", "ipSeries", "homepage"]] {
        _id,
        _type,
        name,
        title,
        "hasDescription": defined(description),
        "descriptionType": description._type,
        "zhBlocks": count(description.zh),
        "enBlocks": count(description.en),
        "arBlocks": count(description.ar)
      }
    `)
    
    const invalidDescriptions = descriptionIssues.filter((doc: any) => {
      if (!doc.hasDescription) return false
      // 检查是否所有语言都是空数组
      return doc.zhBlocks === 0 && doc.enBlocks === 0 && doc.arBlocks === 0
    })
    
    if (invalidDescriptions.length > 0) {
      console.log(`❌ 发现 ${invalidDescriptions.length} 个空描述:`)
      invalidDescriptions.forEach((doc: any) => {
        const displayName = doc.name?.zh || doc.title?.zh || doc._id
        console.log(`  - ${doc._type}: ${displayName}`)
      })
    } else {
      console.log('✅ 所有描述字段正常')
    }
    
    // 3. 检查首页特定字段
    console.log('\n3️⃣ 检查首页数据...')
    const homepage = await client.fetch(`
      *[_type == "homepage"][0] {
        _id,
        "heroDescBlocks": count(heroSection.description.zh),
        "featureCount": count(featuresSection.features),
        "features": featuresSection.features[] {
          title,
          "descBlocks": count(description.zh)
        }
      }
    `)
    
    if (homepage) {
      console.log(`  - Hero描述: ${homepage.heroDescBlocks || 0} 个块`)
      console.log(`  - 功能数量: ${homepage.featureCount || 0}`)
      if (homepage.features) {
        homepage.features.forEach((feature: any, index: number) => {
          if (feature.descBlocks === 0) {
            console.log(`    ❌ 功能 ${index + 1} 描述为空`)
          }
        })
      }
    }
    
    // 4. 检查联系页面
    console.log('\n4️⃣ 检查联系页面数据...')
    const contactPage = await client.fetch(`
      *[_type == "contactPage"][0] {
        _id,
        "formDescBlocks": count(contactForm.formDescription.zh)
      }
    `)
    
    if (contactPage && contactPage.formDescBlocks === 0) {
      console.log('  ❌ 联系表单描述为空')
    } else {
      console.log('  ✅ 联系页面数据正常')
    }
    
    console.log('\n📝 检查完成！')
    console.log('如果上述检查没有发现问题，请在 Sanity Studio 中查看具体的验证错误信息。')
    
  } catch (error) {
    console.error('❌ 检查过程中出错:', error)
  }
}

// 运行检查
checkValidationErrors()