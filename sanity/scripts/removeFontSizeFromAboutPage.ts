import {createClient} from '@sanity/client'

const client = createClient({
  projectId: process.env.SANITY_STUDIO_PROJECT_ID || '4za4x22i',
  dataset: process.env.SANITY_STUDIO_DATASET || 'staging',
  apiVersion: '2024-01-01',
  token: process.env.SANITY_STUDIO_API_TOKEN,
  useCdn: false
})

async function removeFontSizeAnnotations() {
  console.log('🧹 移除 aboutPage 中的字体大小注释...\n')
  
  try {
    // 查找所有包含草稿的 aboutPage 文档
    const query = `*[_type == "aboutPage" || _id == "drafts.aboutPage"] {
      _id,
      _type,
      _rev,
      "isDraft": _id in path("drafts.**")
    }`
    
    const documents = await client.fetch(query)
    console.log(`找到 ${documents.length} 个 aboutPage 文档:`)
    documents.forEach((doc: any) => {
      console.log(`  - ${doc._id} (${doc.isDraft ? '草稿' : '已发布'})`)
    })
    
    if (documents.length === 0) {
      console.log('\n💡 提示：在 Sanity Studio 中，清理步骤：')
      console.log('1. 进入 About Page 编辑界面')
      console.log('2. 找到有字体大小标记的文本（蓝色文字）')
      console.log('3. 选中这些文本')
      console.log('4. 点击字体大小按钮，然后点击垃圾桶图标重置')
      console.log('5. 对所有有问题的文本重复此操作')
      console.log('6. 保存并发布')
      return
    }
    
    // 对每个文档进行处理
    for (const doc of documents) {
      console.log(`\n处理文档: ${doc._id}`)
      
      // 获取完整文档
      const fullDoc = await client.getDocument(doc._id)
      if (!fullDoc) {
        console.log('  ❌ 无法获取文档内容')
        continue
      }
      
      // 递归处理块内容，移除字体大小标记
      const cleanBlockContent = (blocks: any[]): any[] => {
        if (!Array.isArray(blocks)) return blocks
        
        return blocks.map(block => {
          if (block._type === 'block') {
            // 移除 markDefs 中的 fontSize 类型
            const cleanedMarkDefs = (block.markDefs || []).filter((markDef: any) => {
              if (markDef._type === 'fontSize') {
                console.log(`  🗑️ 移除字体大小标记`)
                return false
              }
              return true
            })
            
            // 移除 children 中对 fontSize 标记的引用
            const cleanedChildren = (block.children || []).map((child: any) => {
              if (child.marks && Array.isArray(child.marks)) {
                // 过滤掉指向已删除 fontSize markDef 的标记
                const cleanedMarks = child.marks.filter((mark: string) => {
                  // 检查这个标记是否指向一个 fontSize markDef
                  const markDef = (block.markDefs || []).find((md: any) => md._key === mark)
                  return !markDef || markDef._type !== 'fontSize'
                })
                
                return {
                  ...child,
                  marks: cleanedMarks
                }
              }
              return child
            })
            
            return {
              ...block,
              markDefs: cleanedMarkDefs,
              children: cleanedChildren
            }
          }
          return block
        })
      }
      
      // 清理文档中的所有富文本字段
      const cleanDocument = (obj: any): any => {
        if (Array.isArray(obj)) {
          return obj.map(cleanDocument)
        }
        
        if (obj && typeof obj === 'object') {
          const cleaned: any = {}
          
          for (const key in obj) {
            const value = obj[key]
            
            // 检查是否是多语言富文本字段
            if (value && typeof value === 'object' && 
                (Array.isArray(value.zh) || Array.isArray(value.en) || Array.isArray(value.ar))) {
              cleaned[key] = {
                zh: value.zh ? cleanBlockContent(value.zh) : value.zh,
                en: value.en ? cleanBlockContent(value.en) : value.en,
                ar: value.ar ? cleanBlockContent(value.ar) : value.ar
              }
            } else {
              cleaned[key] = cleanDocument(value)
            }
          }
          
          return cleaned
        }
        
        return obj
      }
      
      // 清理整个文档
      const cleanedDoc = cleanDocument(fullDoc)
      
      // 提取需要更新的字段
      const updates: any = {}
      if (cleanedDoc.heroSection) updates.heroSection = cleanedDoc.heroSection
      if (cleanedDoc.sections) updates.sections = cleanedDoc.sections
      if (cleanedDoc.team) updates.team = cleanedDoc.team
      
      console.log('  📝 更新文档...')
      
      try {
        await client
          .patch(doc._id)
          .set(updates)
          .commit()
        
        console.log('  ✅ 更新成功！')
      } catch (error: any) {
        console.error('  ❌ 更新失败:', error.message)
      }
    }
    
    console.log('\n✨ 处理完成！')
    console.log('请刷新 Sanity Studio 并尝试发布。')
    
  } catch (error) {
    console.error('❌ 处理过程出错:', error)
  }
}

// 运行清理
removeFontSizeAnnotations()