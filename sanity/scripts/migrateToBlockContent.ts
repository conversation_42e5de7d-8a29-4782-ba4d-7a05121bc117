import {createClient} from '@sanity/client'
import {v4 as uuidv4} from 'uuid'

// 配置 Sanity 客户端
const client = createClient({
  projectId: process.env.SANITY_STUDIO_PROJECT_ID || '4za4x22i',
  dataset: process.env.SANITY_STUDIO_DATASET || 'staging',
  apiVersion: '2024-01-01',
  token: process.env.SANITY_STUDIO_API_TOKEN,
  useCdn: false
})

// 将纯文本转换为块内容格式
function textToBlockContent(text: string | undefined, locale: string): any[] {
  if (!text) return []
  
  // 按换行符分割文本，创建多个段落
  const paragraphs = text.split('\n').filter(p => p.trim())
  
  return paragraphs.map(paragraph => ({
    _type: 'block',
    _key: uuidv4(),
    style: 'normal',
    markDefs: [],
    children: [
      {
        _type: 'span',
        _key: uuidv4(),
        text: paragraph.trim(),
        marks: []
      }
    ]
  }))
}

// 转换多语言文本字段
function convertLocaleText(localeText: any): any {
  if (!localeText) return null
  
  const result: any = {}
  const locales = ['zh', 'en', 'ar']
  
  locales.forEach(locale => {
    if (localeText[locale]) {
      result[locale] = textToBlockContent(localeText[locale], locale)
    }
  })
  
  return result
}

// 迁移函数
async function migrate() {
  console.log('🚀 开始迁移数据到富文本格式...')
  
  try {
    // 1. 迁移 homepage
    console.log('\n📄 迁移首页数据...')
    const homepages = await client.fetch(`*[_type == "homepage"]`)
    
    for (const homepage of homepages) {
      const updates: any = {}
      
      // 转换 heroSection.description
      if (homepage.heroSection?.description) {
        updates['heroSection.description'] = convertLocaleText(homepage.heroSection.description)
      }
      
      // 转换 features.items[].description
      if (homepage.featuresSection?.features) {
        updates['featuresSection.features'] = homepage.featuresSection.features.map((feature: any, index: number) => ({
          ...feature,
          description: convertLocaleText(feature.description)
        }))
      }
      
      if (Object.keys(updates).length > 0) {
        await client.patch(homepage._id).set(updates).commit()
        console.log(`✅ 更新首页: ${homepage._id}`)
      }
    }
    
    // 2. 迁移 categories
    console.log('\n📁 迁移分类数据...')
    const categories = await client.fetch(`*[_type == "category"]`)
    
    for (const category of categories) {
      if (category.description) {
        const updates = {
          description: convertLocaleText(category.description)
        }
        await client.patch(category._id).set(updates).commit()
        console.log(`✅ 更新分类: ${category.name?.zh || category._id}`)
      }
    }
    
    // 3. 迁移 ipSeries
    console.log('\n🎮 迁移IP系列数据...')
    const ipSeries = await client.fetch(`*[_type == "ipSeries"]`)
    
    for (const series of ipSeries) {
      if (series.description) {
        const updates = {
          description: convertLocaleText(series.description)
        }
        await client.patch(series._id).set(updates).commit()
        console.log(`✅ 更新IP系列: ${series.name?.zh || series._id}`)
      }
    }
    
    // 4. 迁移 siteSettings
    console.log('\n⚙️ 迁移站点设置数据...')
    const siteSettings = await client.fetch(`*[_type == "siteSettings"]`)
    
    for (const settings of siteSettings) {
      if (settings.siteDescription) {
        const updates = {
          siteDescription: convertLocaleText(settings.siteDescription)
        }
        await client.patch(settings._id).set(updates).commit()
        console.log(`✅ 更新站点设置: ${settings._id}`)
      }
    }
    
    // 5. 迁移 contactPage
    console.log('\n📞 迁移联系页面数据...')
    const contactPages = await client.fetch(`*[_type == "contactPage"]`)
    
    for (const contactPage of contactPages) {
      if (contactPage.contactForm?.formDescription) {
        const updates = {
          'contactForm.formDescription': convertLocaleText(contactPage.contactForm.formDescription)
        }
        await client.patch(contactPage._id).set(updates).commit()
        console.log(`✅ 更新联系页面: ${contactPage._id}`)
      }
    }
    
    console.log('\n✨ 迁移完成！')
    console.log('请在 Sanity Studio 中检查数据是否正确转换。')
    
  } catch (error) {
    console.error('❌ 迁移失败:', error)
    process.exit(1)
  }
}

// 运行迁移
migrate()