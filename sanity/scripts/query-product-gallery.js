#!/usr/bin/env node
/**
 * query-product-gallery.js
 *
 * Queries specific product gallery data to investigate migration issues
 * 
 * Usage:
 *   node scripts/query-product-gallery.js
 *   node scripts/query-product-gallery.js --id db933d  # Check specific product
 */

require('dotenv').config()
const {createClient} = require('@sanity/client')

const specificId = process.argv.find(arg => arg.startsWith('--id'))?.split('=')[1] || 
                   process.argv[process.argv.indexOf('--id') + 1]

console.log('🔍 Product Gallery Investigation')
console.log('================================')
console.log()

/* --- Sanity Client -------------------------------------------------- */
const client = createClient({
  projectId : process.env.SANITY_STUDIO_PROJECT_ID || '4za4x22i',
  dataset   : process.env.SANITY_STUDIO_DATASET     || 'production',
  token     : process.env.SANITY_WRITE_TOKEN || process.env.SANITY_READ_TOKEN,
  apiVersion: new Date().toISOString().slice(0,10),   // YYYY-MM-DD
  useCdn    : false
})

/* --- 主流程 ---------------------------------------------------------- */
(async () => {
  try {
    console.log('📊 Dataset:', client.config().dataset)
    console.log()
    
    if (specificId) {
      // Query specific product
      console.log(`🔍 Investigating product ending with: ${specificId}`)
      const query = `*[_type == "product" && _id match "*${specificId}"]{
        _id, _rev, name, gallery
      }`
      
      const products = await client.fetch(query)
      
      if (products.length === 0) {
        console.log('❌ No products found with that ID')
        return
      }
      
      for (const product of products) {
        console.log(`\n📄 Product: ${product.name?.zh || 'Unnamed'} (${product._id})`)
        console.log('─'.repeat(60))
        
        // Check gallery (mainImage is now gallery[0])
        console.log('\n🖼️  Gallery:')
        if (product.gallery && Array.isArray(product.gallery)) {
          console.log(`   Gallery has ${product.gallery.length} items`)
          
          product.gallery.forEach((item, index) => {
            console.log(`\n   [${index}] Gallery Item:`)
            console.log(JSON.stringify(item, null, 4))
            
            // Analyze each item
            if (!item) {
              console.log(`   ❌ Item ${index}: null/undefined`)
            } else if (item._type === 'flexibleImage') {
              const issues = []
              if (!item.imageType) issues.push('missing imageType')
              if (typeof item.alt === 'undefined') issues.push('missing alt')
              if (typeof item.caption === 'undefined') issues.push('missing caption')
              if (item.imageType === 'upload' && !item.uploadedImage) issues.push('missing uploadedImage')
              if (item.imageType === 'external' && !item.externalUrl) issues.push('missing externalUrl')
              
              if (issues.length > 0) {
                console.log(`   ⚠️  Item ${index}: ${issues.join(', ')}`)
              } else {
                console.log(`   ✅ Item ${index}: properly migrated`)
              }
            } else if (item._ref || item.asset) {
              console.log(`   🔄 Item ${index}: old format (needs migration)`)
            } else {
              console.log(`   ❓ Item ${index}: unknown format`)
            }
          })
        } else {
          console.log('   No gallery or not an array')
        }
      }
    } else {
      // Query multiple products for comparison
      console.log('🔍 Comparing gallery structures across products')
      
      const query = `*[_type == "product" && defined(gallery)][0..5]{
        _id, 
        "name": name.zh,
        "galleryCount": count(gallery),
        gallery
      }`
      
      const products = await client.fetch(query)
      
      console.log(`\nFound ${products.length} products with galleries`)
      
      for (const product of products) {
        console.log(`\n📄 ${product.name} (${product._id.slice(-6)})`)
        console.log(`   Gallery items: ${product.galleryCount}`)
        
        if (product.gallery && Array.isArray(product.gallery)) {
          const analysis = {
            migrated: 0,
            oldFormat: 0,
            incomplete: 0,
            invalid: 0
          }
          
          product.gallery.forEach((item, index) => {
            if (!item) {
              analysis.invalid++
            } else if (item._type === 'flexibleImage') {
              const hasIssues = !item.imageType || 
                               typeof item.alt === 'undefined' || 
                               typeof item.caption === 'undefined' ||
                               (item.imageType === 'upload' && !item.uploadedImage) ||
                               (item.imageType === 'external' && !item.externalUrl)
              
              if (hasIssues) {
                analysis.incomplete++
              } else {
                analysis.migrated++
              }
            } else if (item._ref || item.asset) {
              analysis.oldFormat++
            } else {
              analysis.invalid++
            }
          })
          
          console.log(`   ✅ Migrated: ${analysis.migrated}`)
          console.log(`   🔄 Old format: ${analysis.oldFormat}`)
          console.log(`   ⚠️  Incomplete: ${analysis.incomplete}`)
          console.log(`   ❌ Invalid: ${analysis.invalid}`)
        }
      }
    }
    
    // Additional query: Check for mixed gallery states
    console.log('\n\n🔍 Checking for mixed gallery states...')
    const mixedQuery = `*[_type == "product" && defined(gallery)] {
      _id,
      "name": name.zh,
      "hasMixedGallery": count(gallery[_type == "flexibleImage"]) > 0 && count(gallery[_type != "flexibleImage"]) > 0
    }[hasMixedGallery == true]`
    
    const mixedProducts = await client.fetch(mixedQuery)
    
    if (mixedProducts.length > 0) {
      console.log(`\n⚠️  Found ${mixedProducts.length} products with mixed gallery formats:`)
      mixedProducts.forEach(product => {
        console.log(`   📄 ${product.name} (${product._id.slice(-6)})`)
      })
    } else {
      console.log('\n✅ No products have mixed gallery formats')
    }
    
  } catch (error) {
    console.error('\n💥 Error querying data:', error)
    process.exit(1)
  }
})()