import { getCliClient } from 'sanity/cli'

const client = getCliClient()

async function verifyMigration() {
  console.log('🔍 验证图片迁移结果...')
  
  try {
    // 查询产品数据
    const products = await client.fetch(`
      *[_type == "product" && isPublished == true][0...3] {
        _id,
        name,
        slug,
        "hasMainImage": false, // mainImage已被迁移到gallery[0]
        "hasGallery": defined(gallery),
        "galleryLength": length(gallery),
        "firstGalleryImage": gallery[0] {
          imageType,
          imageType == "upload" => {
            "url": uploadedImage.asset->url,
            "alt": uploadedImage.alt
          },
          imageType == "external" => {
            "url": externalUrl,
            "alt": alt
          }
        }
      }
    `)
    
    console.log(`\n找到 ${products.length} 个已发布产品:`)
    
    products.forEach((product, index) => {
      console.log(`\n产品 ${index + 1}:`)
      console.log(`  - ID: ${product._id}`)
      console.log(`  - 名称: ${product.name?.zh || '未命名'}`)
      console.log(`  - Slug: ${product.slug?.current}`)
      // mainImage 已迁移到 gallery[0]
      console.log(`  - 有 gallery: ${product.hasGallery}`)
      console.log(`  - Gallery 长度: ${product.galleryLength || 0}`)
      
      if (product.firstGalleryImage) {
        console.log(`  - 第一张图片类型: ${product.firstGalleryImage.imageType}`)
        console.log(`  - 第一张图片 URL: ${product.firstGalleryImage.url || '无URL'}`)
      }
    })
    
    // 统计数据
    const stats = await client.fetch(`
      {
        "total": count(*[_type == "product"]),
        "published": count(*[_type == "product" && isPublished == true]),
        "withMainImage": 0, // mainImage已被迁移到gallery[0]
        "withGallery": count(*[_type == "product" && defined(gallery)]),
        "withEmptyGallery": count(*[_type == "product" && defined(gallery) && length(gallery) == 0])
      }
    `)
    
    console.log(`\n📊 数据库统计:`)
    console.log(`  - 总产品数: ${stats.total}`)
    console.log(`  - 已发布产品: ${stats.published}`)
    // mainImage 已迁移到 gallery[0]
    console.log(`  - 有 gallery 的产品: ${stats.withGallery}`)
    console.log(`  - gallery 为空的产品: ${stats.withEmptyGallery}`)
    
    if (stats.withMainImage === 0 && stats.withEmptyGallery === 0) {
      console.log('\n✅ 迁移验证成功！所有产品都已正确迁移')
    } else {
      console.log('\n⚠️  发现问题，请检查数据')
    }
    
  } catch (error) {
    console.error('❌ 验证失败:', error)
  }
}

verifyMigration()