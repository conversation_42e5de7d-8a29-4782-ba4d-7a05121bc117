/**
 * Test script to verify filter functionality and image loading in studio views
 */
import {createClient} from '@sanity/client'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config()

const client = createClient({
  projectId: process.env.SANITY_STUDIO_PROJECT_ID || '4za4x22i',
  dataset: process.env.SANITY_STUDIO_DATASET || 'production',
  apiVersion: '2024-01-01',
  useCdn: false
})

async function testFilterFunctionality() {
  console.log('🔍 Testing filter functionality and image preview...\n')

  try {
    // Test each predefined filter to see what products they return
    const filters = {
      'published': '_type == "product" && isPublished == true',
      'draft': '_type == "product" && isPublished != true',
      'inStock': '_type == "product" && stockStatus == "in-stock"',
      'preOrder': '_type == "product" && stockStatus == "pre-order"',
      'soldOut': '_type == "product" && stockStatus == "sold-out"',
      'withImages': '_type == "product" && defined(gallery) && count(gallery) > 0',
      'noImages': '_type == "product" && (!defined(gallery) || count(gallery) == 0)',
      'recent': '_type == "product" && _createdAt > dateTime(now()) - 86400*7'
    }

    for (const [filterName, filterQuery] of Object.entries(filters)) {
      console.log(`📊 Testing filter: ${filterName}`)
      
      const products = await client.fetch(`
        *[${filterQuery}][0...5] {
          _id,
          name,
          isPublished,
          stockStatus,
          _createdAt,
          gallery[] {
            imageType,
            uploadedImage {
              asset-> {
                _id,
                url
              }
            },
            externalUrl,
            asset-> {
              _id,
              url
            }
          }
        }
      `)

      console.log(`   Found ${products.length} products`)
      
      if (products.length > 0) {
        products.forEach((product, i) => {
          const hasImage = product.gallery && product.gallery.length > 0 && (
            (product.gallery[0].imageType === 'upload' && product.gallery[0].uploadedImage) ||
            (product.gallery[0].imageType === 'external' && product.gallery[0].externalUrl)
          )
          
          console.log(`   ${i + 1}. ${product.name?.zh || 'Unnamed'} - ${hasImage ? '📸' : '❌'} ${product.isPublished ? '✅' : '📝'} ${product.stockStatus}`)
        })
      }
      console.log()
    }

    // Test category browsing
    console.log('📂 Testing category browsing...')
    const categories = await client.fetch(`
      *[_type == "category"] {
        _id,
        name,
        "products": *[_type == "product" && category._ref == ^._id][0...3] {
          _id,
          name,
          gallery[] {
            imageType,
            uploadedImage { asset-> { url } },
            externalUrl,
            asset-> { url }
          }
        }
      }
    `)

    categories.forEach(category => {
      console.log(`   📁 ${category.name?.zh || 'Unnamed Category'}`)
      category.products.forEach((product, i) => {
        const hasImage = product.mainImage && (
          (product.mainImage.imageType === 'upload' && product.mainImage.uploadedImage) ||
          (product.mainImage.imageType === 'external' && product.mainImage.externalUrl)
        )
        console.log(`      ${i + 1}. ${product.name?.zh || 'Unnamed'} - ${hasImage ? '📸' : '❌'}`)
      })
    })

    console.log('\n✅ Filter functionality test completed!')

  } catch (error) {
    console.error('❌ Error testing filter functionality:', error)
    process.exit(1)
  }
}

testFilterFunctionality()