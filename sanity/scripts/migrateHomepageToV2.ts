#!/usr/bin/env node
import {createClient} from '@sanity/client'
import dotenv from 'dotenv'
import ora from 'ora'

// Load environment variables
dotenv.config({path: '.env.local'})
dotenv.config()

const projectId = process.env.SANITY_STUDIO_PROJECT_ID || '4za4x22i'
const dataset = process.env.SANITY_STUDIO_DATASET || 'production'
const apiVersion = '2024-01-01'
const token = process.env.SANITY_WRITE_TOKEN || process.env.SANITY_API_TOKEN

if (!token) {
  console.error('❌ SANITY_WRITE_TOKEN or SANITY_API_TOKEN is required in .env file')
  process.exit(1)
}

const client = createClient({
  projectId,
  dataset,
  apiVersion,
  token,
  useCdn: false,
})

async function migrateHomepageData() {
  const spinner = ora('Starting homepage migration...').start()

  try {
    // Step 1: Fetch existing homepage data
    spinner.text = 'Fetching existing homepage data...'
    const homepage = await client.fetch(`*[_type == "homepage"][0]`)
    
    if (!homepage) {
      spinner.warn('No existing homepage data found')
      return
    }

    // Step 2: Fetch featured products data
    spinner.text = 'Fetching featured products data...'
    const featuredProductsData = await client.fetch(`*[_type == "featuredProducts"][0]`)

    // Step 3: Prepare new homepage V2 data
    spinner.text = 'Preparing new homepage structure...'
    const homepageV2Data = {
      _id: 'homepageV2',
      _type: 'homepageV2',
      
      // Basic settings (Tab 1)
      title: homepage.title,
      heroSection: {
        ...homepage.heroSection,
      },
      
      // Featured Products (Tab 2) - Merge from featuredProducts document
      featuredProductsSection: featuredProductsData ? {
        showSection: featuredProductsData.isActive !== false,
        sectionTitle: featuredProductsData.title || {
          zh: '精选产品',
          en: 'Featured Products',
          ar: 'المنتجات المميزة'
        },
        sectionSubtitle: featuredProductsData.subtitle,
        products: featuredProductsData.products || [],
        displaySettings: {
          showPrices: featuredProductsData.displaySettings?.showPrices !== false,
          showRatings: featuredProductsData.displaySettings?.showRatings !== false,
          showCategories: featuredProductsData.displaySettings?.showCategories !== false,
          showBadges: featuredProductsData.displaySettings?.showBadges !== false,
        },
        layoutSettings: {
          displayMode: 'grid',
          columnsDesktop: featuredProductsData.layout?.columnsDesktop || 4,
          columnsTablet: featuredProductsData.layout?.columnsTablet || 2,
          columnsMobile: featuredProductsData.layout?.columnsMobile || 1,
        }
      } : {
        showSection: false,
        sectionTitle: {
          zh: '精选产品',
          en: 'Featured Products',
          ar: 'المنتجات المميزة'
        }
      },
      
      // Features Section (Tab 3)
      featuresSection: homepage.featuresSection ? {
        ...homepage.featuresSection,
      } : {
        showSection: false
      },
      
      // Brand Story (Tab 4)
      brandStorySection: homepage.brandStorySection ? {
        showSection: true,
        ...homepage.brandStorySection,
      } : {
        showSection: false
      },
      
      // Statistics (Tab 5)
      statsSection: homepage.statsSection ? {
        ...homepage.statsSection,
        sectionTitle: {
          zh: '我们的成就',
          en: 'Our Achievements',
          ar: 'إنجازاتنا'
        }
      } : {
        showStats: false
      },
      
      // SEO (Tab 6)
      seo: homepage.seo || {}
    }

    // Step 4: Create or update homepageV2 document
    spinner.text = 'Creating new homepage V2 document...'
    
    // Check if homepageV2 already exists
    const existingV2 = await client.fetch(`*[_id == "homepageV2"][0]`)
    
    if (existingV2) {
      // Update existing document
      await client.patch('homepageV2').set(homepageV2Data).commit()
      spinner.succeed('✅ Updated existing homepage V2 document')
    } else {
      // Create new document
      await client.create(homepageV2Data)
      spinner.succeed('✅ Created new homepage V2 document')
    }

    // Step 5: Display migration summary
    console.log('\n📊 Migration Summary:')
    console.log('  - Basic settings: ✓ Migrated')
    console.log(`  - Featured products: ${featuredProductsData ? '✓ Migrated' : '⚠ No data found'}`)
    console.log(`  - Features section: ${homepage.featuresSection ? '✓ Migrated' : '⚠ No data found'}`)
    console.log(`  - Brand story: ${homepage.brandStorySection ? '✓ Migrated' : '⚠ No data found'}`)
    console.log(`  - Statistics: ${homepage.statsSection ? '✓ Migrated' : '⚠ No data found'}`)
    console.log(`  - SEO settings: ${homepage.seo ? '✓ Migrated' : '⚠ No data found'}`)
    
    console.log('\n✨ Migration completed successfully!')
    console.log('\n⚠️  Important Notes:')
    console.log('  1. The old homepage document is still intact')
    console.log('  2. Featured products have been integrated into the new homepage')
    console.log('  3. Test the new homepage in Sanity Studio before updating the frontend')
    console.log('  4. Update frontend queries to use "homepageV2" instead of "homepage"')
    
  } catch (error) {
    spinner.fail('❌ Migration failed')
    console.error('Error details:', error)
    process.exit(1)
  }
}

// Add dry-run option
const isDryRun = process.argv.includes('--dry-run')

if (isDryRun) {
  console.log('🔍 Running in DRY RUN mode - no changes will be made')
}

console.log('🚀 Homepage Migration Script')
console.log('================================')
console.log(`Project ID: ${projectId}`)
console.log(`Dataset: ${dataset}`)
console.log(`Mode: ${isDryRun ? 'DRY RUN' : 'LIVE'}`)
console.log('================================\n')

// Run migration
migrateHomepageData()