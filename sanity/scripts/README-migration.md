# 图片迁移脚本使用指南

本目录包含用于将旧格式图片迁移到新的 flexibleImage 格式的脚本。

## 背景

项目中使用了新的 `flexibleImage` schema 来支持更灵活的图片管理，包括：
- 上传到 Sanity 的图片
- 外部图片链接（如 Unsplash）
- 统一的 alt 文本和说明管理

旧的图片格式需要迁移到新格式以确保一致性和完整的功能支持。

## 脚本说明

### 1. `check-image-migration-status.js` - 状态检查脚本

检查当前数据库中图片迁移的状态。

```bash
# 检查迁移状态
node scripts/check-image-migration-status.js

# 显示详细信息
node scripts/check-image-migration-status.js --verbose
```

**功能：**
- 分析所有文档类型的图片字段
- 显示迁移状态统计
- 识别需要迁移的文档
- 提供详细的问题报告

### 2. `migrate-all-images.js` - 全面迁移脚本

执行图片格式的全面迁移。

```bash
# 1. 首先进行干运行（推荐）
node scripts/migrate-all-images.js

# 2. 执行实际迁移
node scripts/migrate-all-images.js --execute

# 3. 执行迁移并自动发布
node scripts/migrate-all-images.js --execute --publish
```

**功能：**
- 支持所有文档类型的图片字段迁移
- 默认为干运行模式（安全）
- 一个一个处理文档（非事务）
- 可选择自动发布
- 详细的进度报告

## 支持的文档类型和字段

| 文档类型 | 图片字段 |
|---------|---------|
| `product` | `gallery[]` (mainImage migrated to gallery[0]) |
| `category` | `image` |
| `ipSeries` | `logo`, `bannerImage` |
| `homepage` | `brandStorySection.backgroundImage` |
| `siteSettings` | `logo`, `favicon` |

## 迁移过程

### 1. 迁移前检查

```bash
cd sanity
node scripts/check-image-migration-status.js
```

这会显示：
- 需要迁移的文档数量
- 按状态分类的详细列表
- 迁移建议

### 2. 执行迁移

#### 步骤 1：干运行测试
```bash
node scripts/migrate-all-images.js
```

这会显示将要进行的更改，但不会实际修改数据。

#### 步骤 2：执行迁移
```bash
node scripts/migrate-all-images.js --execute
```

这会执行实际的迁移，但文档将保持为草稿状态。

#### 步骤 3：执行迁移并发布
```bash
node scripts/migrate-all-images.js --execute --publish
```

这会执行迁移并自动发布所有修改的文档。

### 3. 验证迁移结果

```bash
node scripts/check-image-migration-status.js
```

确认所有文档都已成功迁移。

## 迁移详情

### 旧格式转换

**旧格式 1：直接引用**
```json
{
  "_ref": "image-abc123...",
  "alt": "描述文本"
}
```

**旧格式 2：图片对象**
```json
{
  "_type": "image",
  "asset": {
    "_ref": "image-abc123...",
    "_type": "reference"
  },
  "crop": {...},
  "hotspot": {...}
}
```

**新格式：flexibleImage**
```json
{
  "_type": "flexibleImage",
  "_key": "uniqueKey123",
  "imageType": "upload",
  "uploadedImage": {
    "_type": "image",
    "asset": {
      "_ref": "image-abc123...",
      "_type": "reference"
    },
    "crop": {...},
    "hotspot": {...}
  },
  "alt": "描述文本",
  "caption": "图片说明"
}
```

### 保留的数据

迁移过程会保留所有现有数据：
- 图片资源引用
- 裁剪信息（crop）
- 热点信息（hotspot）
- 替代文本（alt）
- 图片说明（caption）

### 新增的字段

迁移会为所有图片添加：
- `imageType: "upload"` - 标识为上传类型
- `alt: ""` - 如果之前没有 alt 文本，设为空字符串
- `caption: ""` - 如果之前没有说明，设为空字符串
- `_key` - 唯一标识符（如果没有的话）

## 安全特性

### 1. 乐观锁
使用文档的 `_rev` 字段进行乐观锁定，防止并发修改冲突。

### 2. 错误处理
- 每个文档单独处理，一个失败不影响其他
- 详细的错误日志
- 重试机制

### 3. 验证
- 迁移前验证所有必需权限
- 检查 Sanity 连接
- 验证环境变量

## 环境变量

确保在 `.env` 文件中设置：

```bash
SANITY_STUDIO_PROJECT_ID=4za4x22i
SANITY_STUDIO_DATASET=production  # 或 staging
SANITY_WRITE_TOKEN=your_write_token_here
```

**注意：** 需要具有 Editor 或 Developer 权限的 token。

## 故障排除

### 1. 权限错误
```
❌ 需要 SANITY_WRITE_TOKEN（Editor / Developer 权限）
```

**解决方案：** 在 Sanity 管理面板创建具有写入权限的 API token。

### 2. 版本冲突
```
❌ Failed to migrate: Revision ID mismatch
```

**解决方案：** 文档在迁移过程中被其他用户修改。重新运行迁移脚本。

### 3. 网络超时
**解决方案：** 脚本包含重试机制和速率限制。如果仍然失败，检查网络连接。

## 最佳实践

### 1. 迁移顺序
1. 先在 staging 环境测试
2. 在生产环境进行干运行
3. 在低峰时间执行迁移

### 2. 备份
虽然脚本设计得很安全，但建议：
- 在迁移前创建数据集备份
- 记录迁移日志

### 3. 验证
- 迁移后检查前端显示
- 验证图片链接是否正常
- 确认所有图片都正确显示

## 支持

如有问题：
1. 检查错误日志中的具体错误信息
2. 验证环境变量和权限设置
3. 尝试单独运行状态检查脚本

---

*最后更新：2025-01-13*