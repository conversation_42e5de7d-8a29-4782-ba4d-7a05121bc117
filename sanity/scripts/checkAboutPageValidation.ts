import {createClient} from '@sanity/client'
import {validateDocument} from '@sanity/validation'
import aboutPage from '../schemas/aboutPage'

const client = createClient({
  projectId: process.env.SANITY_STUDIO_PROJECT_ID || '4za4x22i',
  dataset: process.env.SANITY_STUDIO_DATASET || 'staging',
  apiVersion: '2024-01-01',
  token: process.env.SANITY_STUDIO_API_TOKEN,
  useCdn: false
})

async function checkAboutPageValidation() {
  console.log('🔍 检查 About Page 验证状态...\n')
  
  try {
    // 获取 aboutPage 文档（包括草稿）
    const published = await client.fetch(`*[_type == "aboutPage" && !(_id in path("drafts.**"))][0]`)
    const draft = await client.fetch(`*[_type == "aboutPage" && _id in path("drafts.**")][0]`)
    
    console.log('📄 文档状态:')
    console.log('  - 已发布版本:', published ? '✅ 存在' : '❌ 不存在')
    console.log('  - 草稿版本:', draft ? '✅ 存在' : '❌ 不存在')
    
    const doc = draft || published
    
    if (!doc) {
      console.log('\n❌ 找不到 aboutPage 文档（既无发布版本也无草稿）')
      
      // 尝试创建一个新的 aboutPage
      console.log('\n💡 这可能是问题所在 - aboutPage 是单例文档，如果不存在会导致无法发布')
      console.log('建议在 Sanity Studio 中手动创建 aboutPage 文档')
      return
    }
    
    console.log('📄 文档信息:')
    console.log('  - ID:', doc._id)
    console.log('  - 类型:', doc._type)
    console.log('  - 修订版本:', doc._rev)
    console.log('')
    
    // 检查各个字段
    console.log('🔍 检查字段完整性:')
    
    // 检查 title
    console.log('\n1. 标题 (title):')
    console.log('  - 中文:', doc.title?.zh ? '✅' : '❌ 缺失')
    console.log('  - 英文:', doc.title?.en ? '✅' : '❌ 缺失')
    console.log('  - 阿拉伯文:', doc.title?.ar ? '✅' : '❌ 缺失')
    
    // 检查 heroSection
    console.log('\n2. Hero 区域:')
    if (doc.heroSection) {
      console.log('  - headline:')
      console.log('    - 中文块数:', Array.isArray(doc.heroSection.headline?.zh) ? doc.heroSection.headline.zh.length : 0)
      console.log('    - 英文块数:', Array.isArray(doc.heroSection.headline?.en) ? doc.heroSection.headline.en.length : 0)
      console.log('    - 阿拉伯文块数:', Array.isArray(doc.heroSection.headline?.ar) ? doc.heroSection.headline.ar.length : 0)
      
      console.log('  - subtitle:')
      console.log('    - 中文块数:', Array.isArray(doc.heroSection.subtitle?.zh) ? doc.heroSection.subtitle.zh.length : 0)
      console.log('    - 英文块数:', Array.isArray(doc.heroSection.subtitle?.en) ? doc.heroSection.subtitle.en.length : 0)
      console.log('    - 阿拉伯文块数:', Array.isArray(doc.heroSection.subtitle?.ar) ? doc.heroSection.subtitle.ar.length : 0)
      
      console.log('  - backgroundImage:', doc.heroSection.backgroundImage ? '✅' : '❌ 缺失')
    } else {
      console.log('  ❌ heroSection 缺失')
    }
    
    // 检查 sections
    console.log('\n3. 内容区块 (sections):')
    if (Array.isArray(doc.sections)) {
      console.log('  - 区块数量:', doc.sections.length)
      doc.sections.forEach((section: any, index: number) => {
        console.log(`  - 区块 ${index + 1}:`)
        console.log('    - 类型:', section._type)
        console.log('    - 标题:', section.title?.zh || '无标题')
        
        if (section._type === 'textSection' && section.content) {
          console.log('    - 内容:')
          console.log('      - 中文块数:', Array.isArray(section.content.zh) ? section.content.zh.length : 0)
          console.log('      - 英文块数:', Array.isArray(section.content.en) ? section.content.en.length : 0)
          console.log('      - 阿拉伯文块数:', Array.isArray(section.content.ar) ? section.content.ar.length : 0)
        }
      })
    } else {
      console.log('  - 无区块')
    }
    
    // 检查 team
    console.log('\n4. 团队成员 (team):')
    if (doc.team) {
      console.log('  - 标题:', doc.team.title?.zh || '无')
      console.log('  - 成员数:', Array.isArray(doc.team.members) ? doc.team.members.length : 0)
      
      if (Array.isArray(doc.team.members)) {
        doc.team.members.forEach((member: any, index: number) => {
          console.log(`  - 成员 ${index + 1}:`)
          console.log('    - 姓名:', member.name?.zh || '无')
          console.log('    - 职位:', member.position?.zh || '无')
          console.log('    - 照片:', member.photo ? '✅' : '❌')
          
          if (member.bio) {
            console.log('    - 简介:')
            console.log('      - 中文块数:', Array.isArray(member.bio.zh) ? member.bio.zh.length : 0)
            console.log('      - 英文块数:', Array.isArray(member.bio.en) ? member.bio.en.length : 0)
            console.log('      - 阿拉伯文块数:', Array.isArray(member.bio.ar) ? member.bio.ar.length : 0)
          }
        })
      }
    }
    
    // 检查 SEO
    console.log('\n5. SEO 设置:')
    console.log('  - 存在:', doc.seo ? '✅' : '❌')
    
    // 检查可能的问题
    console.log('\n🔍 潜在问题检查:')
    
    // 检查空的富文本字段
    const checkEmptyBlockContent = (field: any, fieldName: string) => {
      if (field && (Array.isArray(field.zh) || Array.isArray(field.en) || Array.isArray(field.ar))) {
        const isEmpty = 
          (!field.zh || field.zh.length === 0) &&
          (!field.en || field.en.length === 0) &&
          (!field.ar || field.ar.length === 0)
        
        if (isEmpty) {
          console.log(`  ⚠️ ${fieldName} 是空的富文本字段`)
          return true
        }
      }
      return false
    }
    
    // 检查各个富文本字段
    if (doc.heroSection) {
      checkEmptyBlockContent(doc.heroSection.headline, 'heroSection.headline')
      checkEmptyBlockContent(doc.heroSection.subtitle, 'heroSection.subtitle')
    }
    
    if (Array.isArray(doc.sections)) {
      doc.sections.forEach((section: any, index: number) => {
        if (section._type === 'textSection') {
          checkEmptyBlockContent(section.content, `sections[${index}].content`)
        }
      })
    }
    
    if (doc.team && Array.isArray(doc.team.members)) {
      doc.team.members.forEach((member: any, index: number) => {
        checkEmptyBlockContent(member.bio, `team.members[${index}].bio`)
      })
    }
    
    console.log('\n✅ 检查完成！')
    
  } catch (error) {
    console.error('❌ 检查失败:', error)
  }
}

checkAboutPageValidation()