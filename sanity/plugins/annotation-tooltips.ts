import {definePlugin} from 'sanity'

// Plugin to add tooltips to annotation toolbar buttons
export const annotationTooltipsPlugin = definePlugin({
  name: 'annotation-tooltips',
  studio: {
    components: {
      layout: (props) => {
        // Add global styles for annotation toolbar tooltips
        if (typeof window !== 'undefined' && !window.__annotationTooltipsInjected) {
          window.__annotationTooltipsInjected = true
          
          const style = document.createElement('style')
          style.textContent = `
            /* Add tooltip to annotation clear button */
            [data-ui="MenuButton"][aria-label*="Remove annotation"]:hover::after,
            [data-ui="Button"][aria-label*="Remove annotation"]:hover::after,
            [data-ui="Button"][aria-label*="Clear annotation"]:hover::after,
            button[aria-label*="Remove annotation"]:hover::after {
              content: "清除格式";
              position: absolute;
              bottom: -30px;
              left: 50%;
              transform: translateX(-50%);
              background: #2a2a2a;
              color: white;
              padding: 4px 8px;
              border-radius: 4px;
              font-size: 12px;
              white-space: nowrap;
              pointer-events: none;
              z-index: 10000;
            }
            
            /* For English locale */
            html[lang="en"] [data-ui="MenuButton"][aria-label*="Remove annotation"]:hover::after,
            html[lang="en"] [data-ui="Button"][aria-label*="Remove annotation"]:hover::after,
            html[lang="en"] button[aria-label*="Remove annotation"]:hover::after {
              content: "Clear Formatting";
            }
            
            /* For Arabic locale */
            html[lang="ar"] [data-ui="MenuButton"][aria-label*="Remove annotation"]:hover::after,
            html[lang="ar"] [data-ui="Button"][aria-label*="Remove annotation"]:hover::after,
            html[lang="ar"] button[aria-label*="Remove annotation"]:hover::after {
              content: "مسح التنسيق";
            }
            
            /* Ensure parent has relative positioning */
            [data-ui="MenuButton"][aria-label*="Remove annotation"],
            [data-ui="Button"][aria-label*="Remove annotation"],
            button[aria-label*="Remove annotation"] {
              position: relative !important;
            }
          `
          document.head.appendChild(style)
        }
        
        return props.renderDefault(props)
      }
    }
  }
})