import {definePlugin} from 'sanity'

export const validationDebugPlugin = definePlugin({
  name: 'validation-debug',
  document: {
    // 在文档发布前进行验证
    actions: (prev, context) => {
      return prev.map(action => {
        if (action.action === 'sanity/publish') {
          const originalOnHandle = action.onHandle
          
          action.onHandle = async () => {
            console.log('🔍 开始发布验证...')
            console.log('文档类型:', context.schemaType.name)
            console.log('文档ID:', context.documentId)
            
            try {
              // 获取文档的当前状态
              const document = context.document
              console.log('文档内容:', JSON.stringify(document, null, 2))
              
              // 执行原始的发布动作
              if (originalOnHandle) {
                await originalOnHandle()
              }
            } catch (error) {
              console.error('❌ 发布错误:', error)
              throw error
            }
          }
        }
        return action
      })
    },
    
    // 监听验证错误
    unstable_observeDocumentPairAvailability: (prev, context) => {
      const originalObserver = prev(context)
      
      return {
        ...originalObserver,
        subscribe: (observer: any) => {
          const wrappedObserver = {
            ...observer,
            next: (value: any) => {
              if (value.validation && value.validation.length > 0) {
                console.error('🚨 验证错误:', value.validation)
                value.validation.forEach((error: any) => {
                  console.error('  - 路径:', error.path?.join('.'))
                  console.error('  - 消息:', error.message)
                  console.error('  - 级别:', error.level)
                })
              }
              observer.next(value)
            }
          }
          return originalObserver.subscribe(wrappedObserver)
        }
      }
    }
  }
})

// 添加全局验证监听器
export const validationListener = definePlugin({
  name: 'validation-listener',
  form: {
    renderInput: (props, next) => {
      // 监听表单验证
      if (props.validation && props.validation.length > 0) {
        console.warn('🔸 字段验证错误:', {
          path: props.path,
          validation: props.validation,
          value: props.value,
          schemaType: props.schemaType.name
        })
      }
      return next(props)
    }
  }
})