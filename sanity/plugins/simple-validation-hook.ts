import {definePlugin} from 'sanity'

// 简单的验证钩子插件
export const simpleValidationHook = definePlugin({
  name: 'simple-validation-hook',
  
  document: {
    // 修改文档的新动作
    newDocumentOptions: (prev, {creationContext}) => {
      console.log('📄 创建新文档:', creationContext)
      return prev
    },
    
    // 修改文档动作
    actions: (prev, context) => {
      return prev.map(action => {
        // 包装发布动作
        if (action.action === 'sanity/publish') {
          const originalOnHandle = action.onHandle
          
          action.onHandle = async () => {
            console.log('🚀 尝试发布文档...')
            console.log('  - 文档类型:', context.schemaType.name)
            console.log('  - 文档ID:', context.documentId)
            console.log('  - 当前用户:', context.currentUser?.name)
            
            // 检查文档状态
            const {published, draft} = context.document || {}
            console.log('  - 已发布版本存在:', !!published)
            console.log('  - 草稿版本存在:', !!draft)
            
            if (draft) {
              console.log('📋 草稿内容摘要:')
              console.log('  - _id:', draft._id)
              console.log('  - _type:', draft._type)
              console.log('  - _rev:', draft._rev)
              
              // 检查 localeBlockContent 字段
              const checkBlockContent = (obj: any, path = '') => {
                for (const key in obj) {
                  if (obj[key] && typeof obj[key] === 'object') {
                    const currentPath = path ? `${path}.${key}` : key
                    
                    // 检查是否是多语言块内容
                    if (obj[key].zh !== undefined || obj[key].en !== undefined || obj[key].ar !== undefined) {
                      console.log(`  - 发现多语言字段 ${currentPath}:`)
                      if (Array.isArray(obj[key].zh)) {
                        console.log(`    - zh: ${obj[key].zh.length} 个块`)
                      }
                      if (Array.isArray(obj[key].en)) {
                        console.log(`    - en: ${obj[key].en.length} 个块`)
                      }
                      if (Array.isArray(obj[key].ar)) {
                        console.log(`    - ar: ${obj[key].ar.length} 个块`)
                      }
                    }
                    
                    // 递归检查
                    checkBlockContent(obj[key], currentPath)
                  }
                }
              }
              
              checkBlockContent(draft)
            }
            
            try {
              if (originalOnHandle) {
                await originalOnHandle()
                console.log('✅ 发布成功!')
              }
            } catch (error) {
              console.error('❌ 发布失败:', error)
              if (error instanceof Error) {
                console.error('  - 错误消息:', error.message)
                console.error('  - 错误堆栈:', error.stack)
              }
              throw error
            }
          }
        }
        
        return action
      })
    }
  },
  
  // 表单级别的验证钩子
  form: {
    renderInput: (props, next) => {
      // 检测验证错误
      if (props.validation && props.validation.length > 0) {
        const errors = props.validation.filter(v => v.level === 'error')
        const warnings = props.validation.filter(v => v.level === 'warning')
        
        if (errors.length > 0) {
          console.error('🚨 字段验证错误:')
          console.error('  - 字段路径:', props.path.join('.'))
          console.error('  - 字段类型:', props.schemaType.name)
          console.error('  - 当前值:', props.value)
          errors.forEach(error => {
            console.error('  - 错误:', error.message)
          })
        }
        
        if (warnings.length > 0) {
          console.warn('⚠️ 字段验证警告:')
          console.warn('  - 字段路径:', props.path.join('.'))
          warnings.forEach(warning => {
            console.warn('  - 警告:', warning.message)
          })
        }
      }
      
      return next(props)
    }
  }
})