import {defineConfig} from 'sanity'
import {deskTool} from 'sanity/desk'
import {visionTool} from '@sanity/vision'
import {colorInput} from '@sanity/color-input'
import {schemaTypes} from './schemas'
import {PREDEFINED_FILTERS} from './lib/productFilters'
import {ProductAdvancedSearch} from './schemas/components/ProductAdvancedSearch'
import {
  createFilterListWithCounts,
  createCategoryBrowserWithCounts,
  createIpSeriesBrowserWithCounts
} from './schemas/components/FilterListWithCounts'
import {validationDebugPlugin, validationListener} from './plugins/validation-debug'
import {simpleValidationHook} from './plugins/simple-validation-hook'
import {annotationTooltipsPlugin} from './plugins/annotation-tooltips'

// Define singleton document types that should only have one instance
const singletonTypes = [
  'homepage', 
  'homepageV2',
  'homepageBasic',
  'homepageFeaturedProducts',
  'homepageFeatures',
  'homepageBrandStory',
  'homepageStats',
  'homepageSeo',
  'aboutPage', 
  'contactPage', 
  'siteSettings'
]

// Determine the dataset based on environment or URL parameter
const getDataset = () => {
  // Check URL parameter first (for deployed studio)
  if (typeof window !== 'undefined') {
    const urlParams = new URLSearchParams(window.location.search)
    const urlDataset = urlParams.get('dataset')
    if (urlDataset && ['production', 'staging'].includes(urlDataset)) {
      return urlDataset
    }
  }
  
  // Fallback to environment variables
  if (process.env.SANITY_STUDIO_USE_STAGING === 'true') {
    return process.env.SANITY_STUDIO_DATASET_STAGING || 'staging'
  }
  return process.env.SANITY_STUDIO_DATASET || 'production'
}

const dataset = getDataset()
const isStaging = dataset !== 'production'

export default defineConfig({
  name: 'default',
  title: `动漫周边品牌 CMS${isStaging ? ' (Staging)' : ''}`,
  
  projectId: '4za4x22i',
  dataset,
  apiVersion: '2024-01-01',

  plugins: [
    annotationTooltipsPlugin(),
    colorInput(),
    deskTool({
      structure: (S) =>
        S.list()
          .id('root')
          .title('内容管理')
          .items([
            // Dataset indicator
            ...(isStaging ? [
              S.listItem()
                .id('dataset-indicator')
                .title(`📍 当前数据集: ${dataset}`)
                .child(
                  S.component()
                    .id('dataset-info')
                    .component(() => {
                      const styles = {
                        padding: '20px',
                        backgroundColor: '#fff3cd',
                        border: '1px solid #ffeaa7',
                        borderRadius: '8px',
                        margin: '20px'
                      }
                      return (
                        <div style={styles}>
                          <h2>当前使用预发布数据集</h2>
                          <p>您正在编辑 <strong>{dataset}</strong> 数据集。</p>
                          <p>这是一个预发布环境，所做的更改不会直接影响生产环境。</p>
                          <hr style={{margin: '20px 0'}} />
                          <h3>发布流程：</h3>
                          <ol>
                            <li>在此预发布环境中进行内容编辑</li>
                            <li>预览并确认更改</li>
                            <li>使用内容推送工具将更改同步到生产环境</li>
                          </ol>
                        </div>
                      )
                    })
                ),
              S.divider(),
            ] : []),
            // Homepage section with sub-pages structure
            S.listItem()
              .id('homepage-management')
              .title('🏠 首页管理')
              .child(
                S.list()
                  .id('homepage-sections')
                  .title('首页配置')
                  .items([
                    S.listItem()
                      .id('homepage-basic')
                      .title('📝 基础设置')
                      .child(
                        S.document()
                          .id('homepageBasic')
                          .schemaType('homepageBasic')
                          .documentId('homepageBasic')
                          .title('基础设置')
                      ),
                    S.listItem()
                      .id('homepage-featured')
                      .title('⭐ 精选产品')
                      .child(
                        S.document()
                          .id('homepageFeaturedProducts')
                          .schemaType('homepageFeaturedProducts')
                          .documentId('homepageFeaturedProducts')
                          .title('精选产品配置')
                      ),
                    S.listItem()
                      .id('homepage-features')
                      .title('✨ 特色功能')
                      .child(
                        S.document()
                          .id('homepageFeatures')
                          .schemaType('homepageFeatures')
                          .documentId('homepageFeatures')
                          .title('特色功能配置')
                      ),
                    S.listItem()
                      .id('homepage-brand-story')
                      .title('📖 品牌故事')
                      .child(
                        S.document()
                          .id('homepageBrandStory')
                          .schemaType('homepageBrandStory')
                          .documentId('homepageBrandStory')
                          .title('品牌故事配置')
                      ),
                    S.listItem()
                      .id('homepage-stats')
                      .title('📊 数据展示')
                      .child(
                        S.document()
                          .id('homepageStats')
                          .schemaType('homepageStats')
                          .documentId('homepageStats')
                          .title('数据展示配置')
                      ),
                    S.listItem()
                      .id('homepage-seo')
                      .title('🔍 SEO设置')
                      .child(
                        S.document()
                          .id('homepageSeo')
                          .schemaType('homepageSeo')
                          .documentId('homepageSeo')
                          .title('SEO设置')
                      ),
                  ])
              ),
            S.divider(),
            // Pages and Site Configuration section
            S.listItem()
              .id('pages')
              .title('📄 页面与配置')
              .child(
                S.list()
                  .id('pages-list')
                  .title('页面与配置管理')
                  .items([
                    S.listItem()
                      .id('site-settings-group')
                      .title('🔧 全站配置')
                      .child(
                        S.document()
                          .id('siteSettings')
                          .schemaType('siteSettings')
                          .documentId('siteSettings')
                      ),
                    S.divider(),
                    S.listItem()
                      .id('aboutPage')
                      .title('📖 关于我们')
                      .child(
                        S.document()
                          .id('aboutPage')
                          .schemaType('aboutPage')
                          .documentId('aboutPage')
                      ),
                    S.listItem()
                      .id('contactPage')
                      .title('📞 联系我们')
                      .child(
                        S.document()
                          .id('contactPage')
                          .schemaType('contactPage')
                          .documentId('contactPage')
                      ),
                  ])
              ),
            S.divider(),
            // Navigation section - Hidden for now as it's not being used
            // S.listItem()
            //   .id('navigation')
            //   .title('🧭 导航管理')
            //   .child(
            //     S.documentList()
            //       .id('navigation-list')
            //       .title('导航配置')
            //       .filter('_type == "navigation"')
            //       .apiVersion('2024-01-01')
            //   ),
            // S.divider(),
            // Product management section
            S.listItem()
              .id('products')
              .title('📦 产品管理')
              .child(
                S.list()
                  .id('products-section')
                  .title('产品管理')
                  .items([
                    // Advanced search component
                    S.listItem()
                      .id('product-advanced-search')
                      .title('🔍 高级搜索')
                      .child(
                        S.component()
                          .id('product-search')
                          .component(ProductAdvancedSearch)
                          .title('产品高级搜索')
                      ),
                    S.divider(),
                    
                    // Predefined filter views with counts
                    ...createFilterListWithCounts(S),
                    S.divider(),
                    
                    // Dynamic category filters with counts
                    createCategoryBrowserWithCounts(S),
                    
                    // Dynamic IP series filters with counts
                    createIpSeriesBrowserWithCounts(S),
                    S.divider(),
                    
                    // Management sections
                    S.listItem()
                      .id('categories')
                      .title('📁 分类管理')
                      .child(
                        S.documentList()
                          .id('categories-list')
                          .title('产品分类')
                          .filter('_type == "category"')
                          .apiVersion('2024-01-01')
                          .defaultOrdering([{field: 'name.zh', direction: 'asc'}])
                      ),
                    S.listItem()
                      .id('ip-series')
                      .title('🎭 IP系列管理')
                      .child(
                        S.documentList()
                          .id('ip-series-list')
                          .title('IP系列管理')
                          .filter('_type == "ipSeries"')
                          .apiVersion('2024-01-01')
                          .defaultOrdering([{field: 'name.zh', direction: 'asc'}])
                      ),
                  ])
              ),
          ])
    }),
    visionTool(),
    simpleValidationHook()
  ],

  schema: {
    types: schemaTypes,
  },

  // Document actions customization - remove unwanted actions for singletons
  document: {
    actions: (prev, context) => {
      // For singleton documents, remove duplicate and delete actions
      if (singletonTypes.includes(context.schemaType)) {
        return prev.filter(({ action }) => !['duplicate', 'delete'].includes(action))
      }
      return prev
    },
  },
})
