/**
 * 产品过滤器工具函数
 * 用于构建Sanity Studio产品列表的过滤查询和管理过滤状态
 */

export interface ProductFilter {
  name?: string
  category?: string
  ipSeries?: string
  tags?: string[]
  minPrice?: number
  maxPrice?: number
  currency?: string
  stockStatus?: string[]
  isPublished?: boolean | null
  publishedAfter?: string
  publishedBefore?: string
  searchAll?: string
}

/**
 * 库存状态选项
 */
export const STOCK_STATUS_OPTIONS = [
  { title: '现货', value: 'in-stock', icon: '📦' },
  { title: '预售', value: 'pre-order', icon: '⏰' },
  { title: '售罄', value: 'sold-out', icon: '❌' }
] as const

/**
 * 货币选项
 */
export const CURRENCY_OPTIONS = [
  { title: '人民币', value: 'CNY', symbol: '¥' },
  { title: '美元', value: 'USD', symbol: '$' },
  { title: '阿联酋迪拉姆', value: 'AED', symbol: 'د.إ' }
] as const

/**
 * 排序选项
 */
export const SORT_OPTIONS = [
  { title: '按名称升序', value: 'name asc', field: 'name.zh', direction: 'asc' },
  { title: '按名称降序', value: 'name desc', field: 'name.zh', direction: 'desc' },
  { title: '按价格升序', value: 'price asc', field: 'price', direction: 'asc' },
  { title: '按价格降序', value: 'price desc', field: 'price', direction: 'desc' },
  { title: '按发布时间最新', value: 'publishedAt desc', field: 'publishedAt', direction: 'desc' },
  { title: '按发布时间最早', value: 'publishedAt asc', field: 'publishedAt', direction: 'asc' },
  { title: '按更新时间', value: '_updatedAt desc', field: '_updatedAt', direction: 'desc' }
] as const

/**
 * 预定义过滤器
 */
export const PREDEFINED_FILTERS = {
  all: {
    title: '全部产品',
    filter: '_type == "product"',
    icon: '📋'
  },
  published: {
    title: '已上架产品',
    filter: '_type == "product" && isPublished == true',
    icon: '✅'
  },
  draft: {
    title: '草稿产品',
    filter: '_type == "product" && isPublished == false',
    icon: '📝'
  },
  inStock: {
    title: '现货产品',
    filter: '_type == "product" && stockStatus == "in-stock"',
    icon: '📦'
  },
  preOrder: {
    title: '预售产品',
    filter: '_type == "product" && stockStatus == "pre-order"',
    icon: '⏰'
  },
  soldOut: {
    title: '售罄产品',
    filter: '_type == "product" && stockStatus == "sold-out"',
    icon: '❌'
  }
} as const

/**
 * 构建基于过滤器的GROQ查询，包含图片字段
 */
export function buildProductQuery(filters: ProductFilter, sortBy = 'name asc'): string {
  const conditions: string[] = ['_type == "product"']
  
  // 全文搜索优先
  if (filters.searchAll) {
    conditions.push(`(
      sku match "*${escapeGroqString(filters.searchAll)}*" ||
      name.zh match "*${escapeGroqString(filters.searchAll)}*" ||
      name.en match "*${escapeGroqString(filters.searchAll)}*" ||
      name.ar match "*${escapeGroqString(filters.searchAll)}*" ||
      shortDescription.zh match "*${escapeGroqString(filters.searchAll)}*" ||
      shortDescription.en match "*${escapeGroqString(filters.searchAll)}*" ||
      shortDescription.ar match "*${escapeGroqString(filters.searchAll)}*" ||
      "${escapeGroqString(filters.searchAll)}" in tags[]
    )`)
  } else {
    // 具体字段过滤
    if (filters.name) {
      conditions.push(`(
        name.zh match "*${escapeGroqString(filters.name)}*" ||
        name.en match "*${escapeGroqString(filters.name)}*" ||
        name.ar match "*${escapeGroqString(filters.name)}*"
      )`)
    }

    if (filters.category) {
      conditions.push(`category._ref == "${filters.category}"`)
    }

    if (filters.ipSeries) {
      conditions.push(`ipSeries._ref == "${filters.ipSeries}"`)
    }

    if (filters.tags && filters.tags.length > 0) {
      const tagConditions = filters.tags.map(tag => `"${escapeGroqString(tag)}" in tags[]`)
      conditions.push(`(${tagConditions.join(' || ')})`)
    }

    if (filters.minPrice !== undefined) {
      conditions.push(`price >= ${filters.minPrice}`)
    }

    if (filters.maxPrice !== undefined) {
      conditions.push(`price <= ${filters.maxPrice}`)
    }

    if (filters.currency) {
      conditions.push(`currency == "${filters.currency}"`)
    }

    if (filters.stockStatus && filters.stockStatus.length > 0) {
      const statusConditions = filters.stockStatus.map(status => `stockStatus == "${status}"`)
      conditions.push(`(${statusConditions.join(' || ')})`)
    }

    if (filters.isPublished !== null && filters.isPublished !== undefined) {
      conditions.push(`isPublished == ${filters.isPublished}`)
    }

    if (filters.publishedAfter) {
      conditions.push(`publishedAt >= "${filters.publishedAfter}"`)
    }

    if (filters.publishedBefore) {
      conditions.push(`publishedAt <= "${filters.publishedBefore}"`)
    }
  }

  const filterString = conditions.join(' && ')
  const sortString = getSortString(sortBy)
  
  return `*[${filterString}] {
    _id,
    _type,
    sku,
    name,
    category->{
      _id,
      name
    },
    ipSeries->{
      _id,
      name
    },
    price,
    currency,
    stockStatus,
    isPublished,
    publishedAt,
    tags,
    gallery[]{
      imageType,
      uploadedImage{
        asset->{
          _id,
          url,
          metadata {
            dimensions
          }
        },
        alt,
        hotspot,
        crop
      },
      externalUrl,
      alt,
      fallbackImage{
        asset->{
          _id,
          url,
          metadata {
            dimensions
          }
        }
      }
    },
    shortDescription
  } | order(${sortString})`
}

/**
 * 获取分类选项的GROQ查询
 */
export function getCategoriesQuery(): string {
  return `*[_type == "category"] | order(name.zh asc) {
    _id,
    _type,
    "title": name.zh,
    "value": _id,
    name
  }`
}

/**
 * 获取IP系列选项的GROQ查询
 */
export function getIpSeriesQuery(): string {
  return `*[_type == "ipSeries"] | order(name.zh asc) {
    _id,
    _type,
    "title": name.zh,
    "value": _id,
    name
  }`
}

/**
 * 获取所有标签的GROQ查询
 */
export function getTagsQuery(): string {
  return `array::unique(*[_type == "product" && defined(tags)].tags[])`
}

/**
 * 转换排序字符串为GROQ排序语法
 */
function getSortString(sortBy: string): string {
  const sortOption = SORT_OPTIONS.find(option => option.value === sortBy)
  if (!sortOption) {
    return 'name.zh asc'
  }
  return `${sortOption.field} ${sortOption.direction}`
}

/**
 * 转义GROQ查询字符串中的特殊字符
 */
function escapeGroqString(str: string): string {
  return str.replace(/['"\\]/g, '\\$&')
}

/**
 * 获取股票状态的显示信息
 */
export function getStockStatusInfo(status: string) {
  return STOCK_STATUS_OPTIONS.find(option => option.value === status) || {
    title: status,
    value: status,
    icon: '❓'
  }
}

/**
 * 获取货币的显示信息
 */
export function getCurrencyInfo(currency: string) {
  return CURRENCY_OPTIONS.find(option => option.value === currency) || {
    title: currency,
    value: currency,
    symbol: ''
  }
}

/**
 * 格式化价格显示
 */
export function formatPrice(price: number, currency: string): string {
  const currencyInfo = getCurrencyInfo(currency)
  return `${currencyInfo.symbol}${price.toLocaleString()}`
}

/**
 * 检查过滤器是否为空
 */
export function isFilterEmpty(filter: ProductFilter): boolean {
  return Object.values(filter).every(value => 
    value === undefined || 
    value === null || 
    value === '' || 
    (Array.isArray(value) && value.length === 0)
  )
}

/**
 * 清空过滤器
 */
export function clearFilter(): ProductFilter {
  return {
    name: '',
    category: '',
    ipSeries: '',
    tags: [],
    minPrice: undefined,
    maxPrice: undefined,
    currency: '',
    stockStatus: [],
    isPublished: null,
    publishedAfter: '',
    publishedBefore: '',
    searchAll: ''
  }
}

/**
 * 保存的过滤器预设
 */
export interface SavedFilter {
  id: string
  name: string
  filter: ProductFilter
  createdAt: string
}

/**
 * 从localStorage加载保存的过滤器
 */
export function loadSavedFilters(): SavedFilter[] {
  if (typeof window === 'undefined') return []
  
  try {
    const saved = localStorage.getItem('sanity-product-filters')
    return saved ? JSON.parse(saved) : []
  } catch {
    return []
  }
}

/**
 * 保存过滤器到localStorage
 */
export function saveFilter(name: string, filter: ProductFilter): void {
  if (typeof window === 'undefined') return
  
  const savedFilters = loadSavedFilters()
  const newFilter: SavedFilter = {
    id: Date.now().toString(),
    name,
    filter,
    createdAt: new Date().toISOString()
  }
  
  savedFilters.push(newFilter)
  localStorage.setItem('sanity-product-filters', JSON.stringify(savedFilters))
}

/**
 * 删除保存的过滤器
 */
export function deleteSavedFilter(id: string): void {
  if (typeof window === 'undefined') return
  
  const savedFilters = loadSavedFilters().filter(f => f.id !== id)
  localStorage.setItem('sanity-product-filters', JSON.stringify(savedFilters))
}

/**
 * 生成图片URL，支持flexibleImage格式
 */
export function generateImageUrl(imageAsset: any, width = 80, height = 80): string {
  if (!imageAsset) return ''
  
  // Handle flexibleImage format
  if (imageAsset.imageType) {
    if (imageAsset.imageType === 'upload' && imageAsset.uploadedImage?.asset?.url) {
      const baseUrl = imageAsset.uploadedImage.asset.url
      const params = new URLSearchParams({
        w: width.toString(),
        h: height.toString(),
        fit: 'crop',
        crop: 'center',
        auto: 'format'
      })
      return `${baseUrl}?${params.toString()}`
    }
    
    if (imageAsset.imageType === 'external' && imageAsset.externalUrl) {
      // For external images, return the URL as-is
      // Note: External images can't be resized through Sanity's API
      return imageAsset.externalUrl
    }
    
    // Fallback to fallbackImage if external image fails
    if (imageAsset.fallbackImage?.asset?.url) {
      const baseUrl = imageAsset.fallbackImage.asset.url
      const params = new URLSearchParams({
        w: width.toString(),
        h: height.toString(),
        fit: 'crop',
        crop: 'center',
        auto: 'format'
      })
      return `${baseUrl}?${params.toString()}`
    }
  }
  
  return '' // No valid image found
}

/**
 * 构建数量统计查询
 */
export function buildCountQuery(filter: string): string {
  return `count(*[${filter}])`
}

/**
 * 获取预定义过滤器的数量统计查询
 */
export function getPredefinedFilterCountQueries(): Record<string, string> {
  const queries: Record<string, string> = {}
  
  Object.entries(PREDEFINED_FILTERS).forEach(([key, config]) => {
    queries[key] = buildCountQuery(config.filter)
  })
  
  return queries
}

/**
 * 获取分类产品数量查询
 */
export function getCategoryCountQuery(): string {
  return `*[_type == "category"] {
    _id,
    "title": name.zh,
    "productCount": count(*[_type == "product" && category._ref == ^._id])
  } | order(title asc)`
}

/**
 * 获取IP系列产品数量查询
 */
export function getIpSeriesCountQuery(): string {
  return `*[_type == "ipSeries"] {
    _id,
    "title": name.zh,
    "productCount": count(*[_type == "product" && ipSeries._ref == ^._id])
  } | order(title asc)`
}