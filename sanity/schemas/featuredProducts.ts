import {defineType} from 'sanity'

export default defineType({
  name: 'featuredProducts',
  title: '首页特色产品',
  type: 'document',
  fields: [
    {
      name: 'title',
      title: '模块标题',
      type: 'localeString',
      validation: Rule => Rule.required(),
      description: '特色产品区域的标题'
    },
    {
      name: 'subtitle',
      title: '模块副标题',
      type: 'localeText',
      description: '特色产品区域的描述文字'
    },
    {
      name: 'products',
      title: '特色产品列表',
      type: 'array',
      of: [
        {
          type: 'reference',
          to: [{type: 'product'}],
          options: {
            filter: 'isPublished == true'
          }
        }
      ],
      validation: Rule => Rule.min(3).max(6),
      description: '选择3-6个特色产品进行展示'
    },
    {
      name: 'displaySettings',
      title: '显示设置',
      type: 'object',
      fields: [
        {
          name: 'showPrices',
          title: '显示价格',
          type: 'boolean',
          initialValue: true
        },
        {
          name: 'showRatings',
          title: '显示评分',
          type: 'boolean',
          initialValue: true
        },
        {
          name: 'showCategories',
          title: '显示分类标签',
          type: 'boolean',
          initialValue: true
        },
        {
          name: 'showBadges',
          title: '显示商品标识',
          type: 'boolean',
          initialValue: true,
          description: '显示"新品"、"热门"等标识'
        }
      ]
    },
    {
      name: 'layout',
      title: '布局设置',
      type: 'object',
      fields: [
        {
          name: 'columnsDesktop',
          title: '桌面端列数',
          type: 'number',
          options: {
            list: [
              {title: '2列', value: 2},
              {title: '3列', value: 3},
              {title: '4列', value: 4}
            ]
          },
          initialValue: 3
        },
        {
          name: 'columnsTablet',
          title: '平板端列数',
          type: 'number',
          options: {
            list: [
              {title: '1列', value: 1},
              {title: '2列', value: 2},
              {title: '3列', value: 3}
            ]
          },
          initialValue: 2
        },
        {
          name: 'columnsMobile',
          title: '手机端列数',
          type: 'number',
          options: {
            list: [
              {title: '1列', value: 1},
              {title: '2列', value: 2}
            ]
          },
          initialValue: 1
        }
      ]
    },
    {
      name: 'isActive',
      title: '启用特色产品模块',
      type: 'boolean',
      initialValue: true,
      description: '是否在首页显示特色产品模块'
    }
  ],
  preview: {
    select: {
      title: 'title.zh',
      productCount: 'products'
    },
    prepare({title, productCount}) {
      const count = productCount ? productCount.length : 0
      return {
        title: title || '特色产品',
        subtitle: `包含 ${count} 个产品`
      }
    }
  }
})