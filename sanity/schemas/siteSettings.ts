import {defineType} from 'sanity'
import {ColorPreview} from './components/ColorPreview'

export default defineType({
  name: 'siteSettings',
  title: '站点设置',
  type: 'document',
  fields: [
    {
      name: 'siteTitle',
      title: '站点标题',
      type: 'localeString',
      validation: Rule => Rule.required(),
      description: '网站的全局标题'
    },
    {
      name: 'siteDescription',
      title: '站点描述',
      type: 'localeBlockContent',
      validation: Rule => Rule.required(),
      description: '网站的全局描述，用于SEO，支持富文本格式'
    },
    {
      name: 'logo',
      title: '网站Logo',
      type: 'flexibleImage',
      description: '网站的主要Logo'
    },
    {
      name: 'favicon',
      title: '网站图标',
      type: 'flexibleImage',
      description: '网站的favicon图标'
    },
    {
      name: 'contactInfo',
      title: '联系信息',
      type: 'object',
      fields: [
        {
          name: 'email',
          title: '邮箱',
          type: 'string',
          validation: Rule => Rule.email()
        },
        {
          name: 'phone',
          title: '电话',
          type: 'string'
        },
        {
          name: 'address',
          title: '地址',
          type: 'localeText'
        }
      ]
    },
    {
      name: 'socialMedia',
      title: '社交媒体',
      type: 'array',
      of: [
        {
          type: 'object',
          fields: [
            {
              name: 'platform',
              title: '平台',
              type: 'string',
              options: {
                list: [
                  {title: '微信', value: 'wechat'},
                  {title: '微博', value: 'weibo'},
                  {title: 'QQ', value: 'qq'},
                  {title: 'Facebook', value: 'facebook'},
                  {title: 'Twitter', value: 'twitter'},
                  {title: 'Instagram', value: 'instagram'},
                  {title: 'LinkedIn', value: 'linkedin'},
                  {title: 'YouTube', value: 'youtube'},
                  {title: 'TikTok', value: 'tiktok'},
                  {title: 'Other', value: 'other'}
                ]
              },
              validation: Rule => Rule.required()
            },
            {
              name: 'url',
              title: 'URL',
              type: 'url',
              validation: Rule => Rule.required()
            },
            {
              name: 'label',
              title: '显示名称',
              type: 'localeString'
            }
          ],
          preview: {
            select: {
              title: 'platform',
              subtitle: 'url'
            }
          }
        }
      ]
    },
    {
      name: 'analytics',
      title: '网站分析',
      type: 'object',
      fields: [
        {
          name: 'googleAnalyticsId',
          title: 'Google Analytics ID',
          type: 'string',
          description: '如: GA_MEASUREMENT_ID'
        },
        {
          name: 'baiduAnalyticsId',
          title: '百度统计ID',
          type: 'string'
        },
        {
          name: 'otherAnalytics',
          title: '其他分析工具',
          type: 'array',
          of: [
            {
              type: 'object',
              fields: [
                {
                  name: 'name',
                  title: '工具名称',
                  type: 'string'
                },
                {
                  name: 'trackingId',
                  title: '跟踪ID',
                  type: 'string'
                },
                {
                  name: 'scriptCode',
                  title: '脚本代码',
                  type: 'text'
                }
              ]
            }
          ]
        }
      ]
    },
    {
      name: 'appearance',
      title: '外观设置',
      type: 'object',
      components: {
        field: ColorPreview
      },
      fields: [
        {
          name: 'primaryColor',
          title: '主色调',
          type: 'color',
          description: '网站的主要颜色，用于按钮、链接等重要元素',
          options: {
            disableAlpha: true,
            colorList: [
              { title: '品牌蓝', value: { hex: '#3B82F6' } },
              { title: '品牌紫', value: { hex: '#A855F7' } },
              { title: '活力红', value: { hex: '#EF4444' } },
              { title: '清新绿', value: { hex: '#10B981' } },
              { title: '阳光黄', value: { hex: '#F59E0B' } },
              { title: '深邃蓝', value: { hex: '#1E40AF' } },
              { title: '粉色系', value: { hex: '#EC4899' } },
              { title: '青色系', value: { hex: '#06B6D4' } }
            ]
          }
        },
        {
          name: 'secondaryColor',
          title: '辅助色调',
          type: 'color',
          description: '网站的辅助颜色，用于背景、装饰等次要元素',
          options: {
            disableAlpha: true,
            colorList: [
              { title: '优雅紫', value: { hex: '#9333EA' } },
              { title: '深粉色', value: { hex: '#DB2777' } },
              { title: '靛青色', value: { hex: '#4F46E5' } },
              { title: '青绿色', value: { hex: '#14B8A6' } },
              { title: '橙色系', value: { hex: '#F97316' } },
              { title: '玫瑰红', value: { hex: '#F43F5E' } },
              { title: '天空蓝', value: { hex: '#0EA5E9' } },
              { title: '紫罗兰', value: { hex: '#8B5CF6' } }
            ]
          }
        },
        {
          name: 'fontFamily',
          title: '字体设置',
          type: 'string',
          options: {
            list: [
              {title: '默认', value: 'default'},
              {title: '宋体', value: 'serif'},
              {title: '黑体', value: 'sans-serif'},
              {title: '等宽字体', value: 'monospace'}
            ]
          },
          initialValue: 'default'
        }
      ]
    },
    {
      name: 'features',
      title: '功能设置',
      type: 'object',
      fields: [
        {
          name: 'enableSearch',
          title: '启用搜索功能',
          type: 'boolean',
          initialValue: true
        },
        {
          name: 'enableNewsletter',
          title: '启用邮件订阅',
          type: 'boolean',
          initialValue: false
        },
        {
          name: 'enableComments',
          title: '启用评论功能',
          type: 'boolean',
          initialValue: false
        },
        {
          name: 'enableLiveChat',
          title: '启用在线客服',
          type: 'boolean',
          initialValue: false
        },
        {
          name: 'maintenanceMode',
          title: '维护模式',
          type: 'boolean',
          initialValue: false,
          description: '启用后，网站将显示维护页面'
        },
        {
          name: 'maintenanceMessage',
          title: '维护提示信息',
          type: 'localeText',
          description: '维护模式下显示的消息'
        }
      ]
    },
    {
      name: 'seo',
      title: 'SEO 设置',
      type: 'seo',
      description: '全局SEO设置'
    }
  ],
  preview: {
    select: {
      title: 'siteTitle.zh',
      subtitle: 'siteDescription.zh'
    }
  }
})