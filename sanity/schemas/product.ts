import React from 'react'
import {defineType, defineField} from 'sanity'
import DirectUploadGalleryInput from './components/DirectUploadGalleryInput'
import TagsInput from './components/TagsInput'

export default defineType({
  name: 'product',
  title: '产品',
  type: 'document',
  options: {
    searchableFields: ['sku']
  },
  fields: [
    defineField({
      name: 'name',
      title: '产品名称',
      type: 'localeString',
      validation: Rule => Rule.required().error('产品名称不能为空。')
    }),
    defineField({
      name: 'slug',
      title: 'URL 标识',
      type: 'slug',
      options: {
        source: 'name.zh',
        maxLength: 96,
      },
      validation: Rule => Rule.required().error('URL 标识不能为空。')
    }),
    defineField({
      name: 'sku',
      title: 'SKU',
      description: '产品唯一识别码。如果填写，请使用大写字母、数字和连字符 (例如: PROD-001)。',
      type: 'string',
      validation: Rule => Rule.custom(sku => {
        if (!sku) {
          return true
        }
        return /^[A-Z0-9\-]+$/.test(sku)
          ? true
          : 'SKU 格式不正确，只能包含大写字母、数字和连字符。'
      })
    }),
    defineField({
      name: 'shortDescription',
      title: '简短描述',
      type: 'localeText',
    }),
    defineField({
      name: 'description',
      title: '详细描述',
      type: 'localeBlockContent',
    }),
    defineField({
      name: 'gallery',
      title: '产品图册（第一张为主图）',
      type: 'array',
      of: [
        {
          type: 'flexibleImage'
        }
      ],
      options: {
        layout: 'grid'
      },
      components: {
        input: DirectUploadGalleryInput
      },
      validation: Rule => Rule.required().min(1).error('至少需要上传一张产品图片')
    }),
    defineField({
      name: 'category',
      title: '产品分类',
      type: 'reference',
      to: [{type: 'category'}],
      validation: Rule => Rule.required().error('产品分类不能为空。')
    }),
    defineField({
      name: 'ipSeries',
      title: 'IP 系列',
      type: 'reference',
      to: [{type: 'ipSeries'}]
    }),
    defineField({
      name: 'tags',
      title: '标签',
      type: 'array',
      of: [{type: 'string'}],
      components: {
        input: TagsInput as any
      },
      description: '选择产品标签，这些标签会在产品卡片上显示为徽章'
    }),
    defineField({
      name: 'price',
      title: '展示价格',
      type: 'number',
    }),
    defineField({
      name: 'currency',
      title: '货币单位',
      type: 'string',
      options: {
        list: [
          {title: '人民币', value: 'CNY'},
          {title: '美元', value: 'USD'},
          {title: '阿联酋迪拉姆', value: 'AED'}
        ]
      },
      initialValue: 'CNY'
    }),
    defineField({
      name: 'stockStatus',
      title: '库存状态',
      type: 'string',
      options: {
        list: [
          {title: '现货', value: 'in-stock'},
          {title: '预售', value: 'pre-order'},
          {title: '售罄', value: 'sold-out'}
        ]
      },
      initialValue: 'in-stock'
    }),
    defineField({
      name: 'isPublished',
      title: '上架状态',
      type: 'boolean',
      initialValue: false
    }),
    defineField({
      name: 'publishedAt',
      title: '发布时间',
      type: 'datetime',
    }),
    defineField({
      name: 'seo',
      title: 'SEO 设置',
      type: 'seo',
    })
  ],
  preview: {
    select: {
      title: 'name.zh',
      subtitle: 'category.name.zh',
      gallery: 'gallery',
      published: 'isPublished',
      stockStatus: 'stockStatus'
    },
    prepare({title, subtitle, gallery, published, stockStatus}) {
      const statusIcon = published ? '✅' : '📝'
      const stockIcon = {
        'in-stock': '📦',
        'pre-order': '⏰',
        'sold-out': '❌'
      }[stockStatus] || '❓'
      
      // Handle different image types for media preview
      let media = undefined
      
      // Use first image from gallery as main image
      const mainImage = gallery && gallery.length > 0 ? gallery[0] : null
      
      if (mainImage) {
        // Handle flexible image format
        if (mainImage.imageType === 'upload' && mainImage.uploadedImage) {
          media = mainImage.uploadedImage
        }
        else if (mainImage.imageType === 'external' && mainImage.externalUrl) {
          // For external images, create a simple preview
          try {
            media = {
              url: mainImage.externalUrl,
              alt: mainImage.alt || '外部图片'
            }
          } catch (error) {
            media = undefined
          }
        }
      }
      
      return {
        title: title || '未命名产品',
        subtitle: `${subtitle || '未分类'} ${statusIcon} ${stockIcon}`,
        media
      }
    }
  }
})
