import localeString from './localeString'
import localeText from './localeText'
import localeBlockContent from './localeBlockContent'
import seo from './seo'
import flexibleImage from './components/flexibleImage'
import product from './product'
import category from './category'
import ipSeries from './ipSeries'
import aboutPage from './aboutPage'
import contactPage from './contactPage'
// Homepage - 模块化子页面结构
import homepageBasic from './homepage/homepageBasic'
import homepageFeaturedProducts from './homepage/homepageFeaturedProducts'
import homepageFeatures from './homepage/homepageFeatures'
import homepageBrandStory from './homepage/homepageBrandStory'
import homepageStats from './homepage/homepageStats'
import homepageSeo from './homepage/homepageSeo'
import siteSettings from './siteSettings'
import navigation from './navigation'
import featuredProducts from './featuredProducts'

export const schemaTypes = [
  // Object types
  localeString,
  localeText,
  localeBlockContent,
  seo,
  flexibleImage,
  
  // Document types
  product,
  category,
  ipSeries,
  
  // Page types (singletons)
  // Homepage - 使用模块化子页面结构 (V2重构后的新版本)
  homepageBasic,
  homepageFeaturedProducts,
  homepageFeatures,
  homepageBrandStory,
  homepageStats,
  homepageSeo,
  aboutPage,
  contactPage,
  
  // Site management
  siteSettings,
  navigation,
  featuredProducts,
]