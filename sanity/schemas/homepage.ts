import {defineType} from 'sanity'

export default defineType({
  name: 'homepage',
  title: '首页管理',
  type: 'document',
  fields: [
    {
      name: 'title',
      title: '页面标题',
      type: 'localeString',
      validation: Rule => Rule.required(),
      description: '页面的主标题，用于SEO和浏览器标签'
    },
    {
      name: 'heroSection',
      title: '首页头部区域',
      type: 'object',
      fields: [
        {
          name: 'titleDisplayMode',
          title: '标题显示模式',
          type: 'string',
          options: {
            list: [
              {title: '文本渐变', value: 'text'},
              {title: '图片Logo', value: 'image'}
            ],
            layout: 'radio'
          },
          initialValue: 'text',
          description: '选择首页标题的显示方式'
        },
        {
          name: 'mainTitle',
          title: '品牌名称',
          type: 'localeString',
          validation: Rule => Rule.custom((value, context) => {
            const parent = context.parent as any
            if (parent?.titleDisplayMode === 'text' && !value?.zh && !value?.en) {
              return '选择文本模式时必须填写品牌名称'
            }
            return true
          }),
          description: '网站的主要品牌名称（文本模式时显示）',
          hidden: ({parent}) => parent?.titleDisplayMode === 'image'
        },
        {
          name: 'titleImage',
          title: '标题图片',
          type: 'flexibleImage',
          validation: Rule => Rule.custom((value, context) => {
            const parent = context.parent as any
            if (parent?.titleDisplayMode === 'image' && !value) {
              return '选择图片模式时必须上传图片'
            }
            return true
          }),
          description: '用于显示的品牌Logo或标题图片',
          hidden: ({parent}) => parent?.titleDisplayMode !== 'image'
        },
        {
          name: 'titleGradient',
          title: '文本渐变配置',
          type: 'object',
          description: '配置标题文本的渐变效果',
          hidden: ({parent}) => parent?.titleDisplayMode !== 'text',
          fields: [
            {
              name: 'usePreset',
              title: '使用预设',
              type: 'boolean',
              initialValue: true,
              description: '使用预设渐变或自定义颜色'
            },
            {
              name: 'preset',
              title: '预设渐变',
              type: 'string',
              options: {
                list: [
                  {title: '蓝紫渐变', value: 'blue-purple'},
                  {title: '橙红渐变', value: 'orange-red'},
                  {title: '绿青渐变', value: 'green-cyan'},
                  {title: '粉紫渐变', value: 'pink-purple'},
                  {title: '彩虹渐变', value: 'rainbow'}
                ]
              },
              initialValue: 'blue-purple',
              hidden: ({parent}) => !parent?.usePreset
            },
            {
              name: 'fromColor',
              title: '起始颜色',
              type: 'color',
              description: '渐变的起始颜色',
              hidden: ({parent}) => parent?.usePreset !== false
            },
            {
              name: 'viaColor',
              title: '中间颜色（可选）',
              type: 'color',
              description: '渐变的中间颜色，留空则为两色渐变',
              hidden: ({parent}) => parent?.usePreset !== false
            },
            {
              name: 'toColor',
              title: '结束颜色',
              type: 'color',
              description: '渐变的结束颜色',
              hidden: ({parent}) => parent?.usePreset !== false
            },
            {
              name: 'direction',
              title: '渐变方向',
              type: 'string',
              options: {
                list: [
                  {title: '从左到右', value: 'to right'},
                  {title: '从右到左', value: 'to left'},
                  {title: '从上到下', value: 'to bottom'},
                  {title: '从下到上', value: 'to top'},
                  {title: '左上到右下', value: 'to bottom right'},
                  {title: '右上到左下', value: 'to bottom left'}
                ]
              },
              initialValue: 'to right'
            }
          ]
        },
        {
          name: 'subtitle',
          title: '副标题',
          type: 'localeString',
          validation: Rule => Rule.required(),
          description: '品牌的简短描述或口号'
        },
        {
          name: 'description',
          title: '详细描述',
          type: 'localeBlockContent',
          validation: Rule => Rule.required(),
          description: '品牌的详细介绍，支持富文本格式（字体大小、加粗、斜体、列表等）'
        },
        {
          name: 'ctaButton',
          title: '行动按钮',
          type: 'object',
          fields: [
            {
              name: 'text',
              title: '按钮文字',
              type: 'localeString',
              validation: Rule => Rule.required()
            },
            {
              name: 'url',
              title: '链接地址',
              type: 'string',
              validation: Rule => Rule.required()
            }
          ]
        }
      ]
    },
    {
      name: 'featuresSection',
      title: '特色功能区域',
      type: 'object',
      fields: [
        {
          name: 'showSection',
          title: '显示特色功能区域',
          type: 'boolean',
          initialValue: true,
          description: '控制是否在首页显示特色功能区域'
        },
        {
          name: 'sectionTitle',
          title: '区域标题',
          type: 'localeString',
          description: '可选的区域标题',
          hidden: ({parent}) => !parent?.showSection
        },
        {
          name: 'sectionDescription',
          title: '区域描述',
          type: 'localeText',
          description: '可选的区域描述文字，显示在标题下方',
          hidden: ({parent}) => !parent?.showSection
        },
        {
          name: 'layout',
          title: '布局样式',
          type: 'string',
          options: {
            list: [
              {title: '网格布局 (3列)', value: 'grid-3'},
              {title: '网格布局 (4列)', value: 'grid-4'},
              {title: '水平排列', value: 'horizontal'},
              {title: '垂直堆叠', value: 'vertical'}
            ]
          },
          initialValue: 'grid-3',
          description: '选择功能卡片的布局方式',
          hidden: ({parent}) => !parent?.showSection
        },
        {
          name: 'features',
          title: '特色功能',
          type: 'array',
          of: [
            {
              type: 'object',
              fields: [
                {
                  name: 'icon',
                  title: '图标',
                  type: 'string',
                  description: '图标的emoji或图标代码',
                  validation: Rule => Rule.required()
                },
                {
                  name: 'title',
                  title: '功能标题',
                  type: 'localeString',
                  validation: Rule => Rule.required()
                },
                {
                  name: 'description',
                  title: '功能描述',
                  type: 'localeBlockContent',
                  validation: Rule => Rule.required(),
                  description: '支持富文本格式'
                },
                {
                  name: 'linkUrl',
                  title: '链接地址',
                  type: 'string',
                  description: '可选：点击卡片跳转的链接地址'
                },
                {
                  name: 'isExternal',
                  title: '外部链接',
                  type: 'boolean',
                  initialValue: false,
                  description: '是否为外部链接（将在新窗口打开）',
                  hidden: ({parent}) => !parent?.linkUrl
                },
                {
                  name: 'order',
                  title: '排序',
                  type: 'number',
                  description: '数字越小排序越靠前',
                  validation: Rule => Rule.min(1).max(100)
                }
              ],
              preview: {
                select: {
                  title: 'title.zh',
                  subtitle: 'description.zh',
                  icon: 'icon',
                  order: 'order'
                },
                prepare({title, subtitle, icon, order}) {
                  return {
                    title: `${icon} ${title}${order ? ` (${order})` : ''}`,
                    subtitle: subtitle
                  }
                }
              }
            }
          ],
          validation: Rule => Rule.min(1).max(6),
          description: '添加1-6个特色功能，建议3个最佳',
          hidden: ({parent}) => !parent?.showSection
        }
      ]
    },
    {
      name: 'brandStorySection',
      title: '品牌故事区域',
      type: 'object',
      fields: [
        {
          name: 'title',
          title: '区域标题',
          type: 'localeString',
          validation: Rule => Rule.required()
        },
        {
          name: 'description',
          title: '品牌故事描述',
          type: 'localeBlockContent',
          validation: Rule => Rule.required(),
          description: '支持富文本格式（字体大小、加粗、斜体、列表、链接等）'
        },
        {
          name: 'backgroundImage',
          title: '背景图片',
          type: 'flexibleImage',
          description: '可选的背景图片'
        },
        {
          name: 'showProducts',
          title: '显示相关商品',
          type: 'boolean',
          initialValue: false,
          description: '是否在品牌故事区域展示相关商品'
        },
        {
          name: 'products',
          title: '相关商品',
          type: 'array',
          of: [
            {
              type: 'reference',
              to: [{type: 'product'}],
              options: {
                filter: 'isPublished == true'
              }
            }
          ],
          validation: Rule => Rule.min(2).max(4),
          description: '选择2-4个代表品牌故事的商品',
          hidden: ({parent}) => !parent?.showProducts
        },
        {
          name: 'productSettings',
          title: '商品展示设置',
          type: 'object',
          fields: [
            {
              name: 'showPrices',
              title: '显示价格',
              type: 'boolean',
              initialValue: true
            },
            {
              name: 'showCategories',
              title: '显示分类标签',
              type: 'boolean',
              initialValue: true
            },
            {
              name: 'showBadges',
              title: '显示商品标识',
              type: 'boolean',
              initialValue: true,
              description: '显示"新品"、"热门"等标识'
            },
            {
              name: 'layout',
              title: '布局方式',
              type: 'string',
              options: {
                list: [
                  {title: '网格布局', value: 'grid'},
                  {title: '轮播展示', value: 'carousel'}
                ]
              },
              initialValue: 'grid'
            }
          ],
          hidden: ({parent}) => !parent?.showProducts
        }
      ]
    },
    {
      name: 'statsSection',
      title: '数据统计区域',
      type: 'object',
      fields: [
        {
          name: 'showStats',
          title: '显示统计数据',
          type: 'boolean',
          initialValue: true
        },
        {
          name: 'stats',
          title: '统计数据',
          type: 'array',
          of: [
            {
              type: 'object',
              fields: [
                {
                  name: 'number',
                  title: '数字',
                  type: 'string',
                  validation: Rule => Rule.required(),
                  description: '显示的数字，如 "1000+" 或 "99%"'
                },
                {
                  name: 'label',
                  title: '标签',
                  type: 'localeString',
                  validation: Rule => Rule.required(),
                  description: '数字的说明文字'
                },
                {
                  name: 'color',
                  title: '颜色',
                  type: 'string',
                  options: {
                    list: [
                      {title: '蓝色', value: 'blue'},
                      {title: '绿色', value: 'green'},
                      {title: '红色', value: 'red'},
                      {title: '紫色', value: 'purple'},
                      {title: '橙色', value: 'orange'}
                    ]
                  },
                  initialValue: 'blue'
                }
              ],
              preview: {
                select: {
                  title: 'number',
                  subtitle: 'label.zh'
                }
              }
            }
          ],
          validation: Rule => Rule.min(1).max(8)
        }
      ]
    },
    {
      name: 'seo',
      title: 'SEO 设置',
      type: 'seo',
      description: '搜索引擎优化设置'
    }
  ],
  preview: {
    select: {
      title: 'title.zh',
      subtitle: 'heroSection.subtitle.zh'
    }
  }
})