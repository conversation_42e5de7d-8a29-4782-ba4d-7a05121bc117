import {defineType} from 'sanity'

export default defineType({
  name: 'contactPage',
  title: '联系我们页面',
  type: 'document',
  fields: [
    {
      name: 'title',
      title: '页面标题',
      type: 'localeString',
      validation: Rule => Rule.required()
    },
    {
      name: 'heroSection',
      title: '头部区域',
      type: 'object',
      fields: [
        {
          name: 'headline',
          title: '主标题',
          type: 'localeString',
          initialValue: {
            zh: '联系我们',
            en: 'Contact Us',
            ar: 'اتصل بنا'
          }
        },
        {
          name: 'subtitle',
          title: '副标题',
          type: 'localeText',
          initialValue: {
            zh: '我们很乐意听取您的声音，如有任何问题或建议，请随时与我们联系。',
            en: 'We\'d love to hear from you. If you have any questions or suggestions, please feel free to contact us.',
            ar: 'نحن نحب أن نسمع منك. إذا كان لديك أي أسئلة أو اقتراحات، فلا تتردد في الاتصال بنا.'
          }
        }
      ]
    },
    {
      name: 'contactInfo',
      title: '联系信息',
      type: 'object',
      fields: [
        {
          name: 'sectionTitle',
          title: '区块标题',
          type: 'localeString',
        },
        {
          name: 'address',
          title: '地址',
          type: 'object',
          fields: [
            {
              name: 'label',
              title: '地址标签',
              type: 'localeString',
              initialValue: {
                zh: '公司地址',
                en: 'Address',
                ar: 'العنوان'
              }
            },
            {
              name: 'value',
              title: '详细地址',
              type: 'localeText',
              initialValue: {
                zh: '中国广东省深圳市南山区科技园大道888号',
                en: '888 Technology Park Avenue, Nanshan District, Shenzhen, Guangdong, China',
                ar: '888 شارع الحديقة التقنية، منطقة نانشان، شينزين، قوانغدونغ، الصين'
              }
            }
          ]
        },
        {
          name: 'phone',
          title: '电话',
          type: 'object',
          fields: [
            {
              name: 'label',
              title: '电话标签',
              type: 'localeString',
              initialValue: {
                zh: '电话',
                en: 'Phone',
                ar: 'الهاتف'
              }
            },
            {
              name: 'value',
              title: '电话号码',
              type: 'string',
              initialValue: '+86 ************'
            }
          ]
        },
        {
          name: 'email',
          title: '邮箱',
          type: 'object',
          fields: [
            {
              name: 'label',
              title: '邮箱标签',
              type: 'localeString',
              initialValue: {
                zh: '邮箱',
                en: 'Email',
                ar: 'البريد الإلكتروني'
              }
            },
            {
              name: 'value',
              title: '邮箱地址',
              type: 'string',
              validation: Rule => Rule.email(),
              initialValue: '<EMAIL>'
            }
          ]
        },
        {
          name: 'workingHours',
          title: '工作时间',
          type: 'object',
          fields: [
            {
              name: 'label',
              title: '工作时间标签',
              type: 'localeString',
              initialValue: {
                zh: '工作时间',
                en: 'Working Hours',
                ar: 'ساعات العمل'
              }
            },
            {
              name: 'value',
              title: '工作时间',
              type: 'localeText',
              initialValue: {
                zh: '周一至周五：09:00-18:00\n周六至周日：10:00-17:00',
                en: 'Monday - Friday: 09:00-18:00\nSaturday - Sunday: 10:00-17:00',
                ar: 'الأثنين - الجمعة: 09:00-18:00\nالسبت - الأحد: 10:00-17:00'
              }
            }
          ]
        }
      ]
    },
    {
      name: 'contactForm',
      title: '联系表单设置',
      type: 'object',
      fields: [
        {
          name: 'formTitle',
          title: '表单标题',
          type: 'localeString',
          initialValue: {
            zh: '留言咨询',
            en: 'Leave a Message',
            ar: 'اترك رسالة'
          }
        },
        {
          name: 'formDescription',
          title: '表单描述',
          type: 'localeBlockContent',
          description: '支持富文本格式'
        },
        {
          name: 'nameLabel',
          title: '姓名字段标签',
          type: 'localeString',
          initialValue: {
            zh: '姓名',
            en: 'Name',
            ar: 'الاسم'
          }
        },
        {
          name: 'emailLabel',
          title: '邮箱字段标签',
          type: 'localeString',
          initialValue: {
            zh: '邮箱',
            en: 'Email',
            ar: 'البريد الإلكتروني'
          }
        },
        {
          name: 'subjectLabel',
          title: '主题字段标签',
          type: 'localeString',
          initialValue: {
            zh: '主题',
            en: 'Subject',
            ar: 'الموضوع'
          }
        },
        {
          name: 'messageLabel',
          title: '消息字段标签',
          type: 'localeString',
          initialValue: {
            zh: '消息内容',
            en: 'Message',
            ar: 'الرسالة'
          }
        },
        {
          name: 'submitButtonText',
          title: '提交按钮文字',
          type: 'localeString',
          initialValue: {
            zh: '发送消息',
            en: 'Send Message',
            ar: 'إرسال الرسالة'
          }
        },
        {
          name: 'successMessage',
          title: '成功提示消息',
          type: 'localeString',
          initialValue: {
            zh: '消息已成功发送，我们将尽快回复您！',
            en: 'Message sent successfully! We will get back to you soon.',
            ar: 'تم إرسال الرسالة بنجاح! سنعود إليك قريباً.'
          }
        },
        {
          name: 'errorMessage',
          title: '错误提示消息',
          type: 'localeString',
          initialValue: {
            zh: '消息发送失败，请稍后重试或直接联系我们。',
            en: 'Failed to send message. Please try again later or contact us directly.',
            ar: 'فشل إرسال الرسالة. يرجى المحاولة مرة أخرى لاحقاً أو الاتصال بنا مباشرة.'
          }
        }
      ]
    },
    {
      name: 'mapSection',
      title: '地图区域',
      type: 'object',
      fields: [
        {
          name: 'showMap',
          title: '显示地图',
          type: 'boolean',
          initialValue: false
        },
        {
          name: 'mapTitle',
          title: '地图标题',
          type: 'localeString',
        },
        {
          name: 'latitude',
          title: '纬度',
          type: 'number',
          description: '地图定位纬度'
        },
        {
          name: 'longitude',
          title: '经度',
          type: 'number',
          description: '地图定位经度'
        },
        {
          name: 'mapDescription',
          title: '地图说明',
          type: 'localeText',
        }
      ]
    },
    {
      name: 'socialLinks',
      title: '社交媒体链接',
      type: 'array',
      of: [
        {
          type: 'object',
          fields: [
            {
              name: 'platform',
              title: '平台名称',
              type: 'string',
              options: {
                list: [
                  {title: '微信', value: 'wechat'},
                  {title: '微博', value: 'weibo'},
                  {title: 'QQ', value: 'qq'},
                  {title: 'Facebook', value: 'facebook'},
                  {title: 'Twitter', value: 'twitter'},
                  {title: 'Instagram', value: 'instagram'},
                  {title: 'LinkedIn', value: 'linkedin'},
                  {title: 'YouTube', value: 'youtube'},
                  {title: '其他', value: 'other'}
                ]
              }
            },
            {
              name: 'label',
              title: '显示名称',
              type: 'localeString',
            },
            {
              name: 'url',
              title: 'URL链接',
              type: 'url',
            },
            {
              name: 'icon',
              title: '图标',
              type: 'flexibleImage',
              description: '可选的自定义图标'
            }
          ],
          preview: {
            select: {
              title: 'platform',
              subtitle: 'label.zh',
              media: 'icon'
            }
          }
        }
      ]
    },
    {
      name: 'seo',
      title: 'SEO 设置',
      type: 'seo',
    }
  ],
  preview: {
    select: {
      title: 'title.zh',
      subtitle: 'title.en'
    }
  }
})