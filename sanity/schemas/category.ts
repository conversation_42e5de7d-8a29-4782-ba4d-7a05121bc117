import {defineType, defineField} from 'sanity'

export default defineType({
  name: 'category',
  title: '产品分类',
  type: 'document',
  fields: [
    defineField({
      name: 'name',
      title: '分类名称',
      type: 'localeString',
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'slug',
      title: 'URL 标识',
      type: 'slug',
      options: {
        source: 'name.zh',
        maxLength: 96,
      },
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'description',
      title: '分类描述',
      type: 'localeBlockContent',
      description: '支持富文本格式（字体大小、加粗、斜体、列表等）'
    }),
    defineField({
      name: 'image',
      title: '分类图片',
      type: 'flexibleImage'
    }),
    defineField({
      name: 'sortOrder',
      title: '排序',
      type: 'number',
      description: '数字越小越靠前'
    })
  ],
  preview: {
    select: {
      title: 'name.zh',
      image: 'image',
      sortOrder: 'sortOrder'
    },
    prepare({title, image, sortOrder}) {
      // Handle flexibleImage type for media preview
      let media = undefined
      
      if (image) {
        // Handle flexible image format
        if (image.imageType === 'upload' && image.uploadedImage) {
          media = image.uploadedImage
        }
        else if (image.imageType === 'external' && image.externalUrl) {
          // For external images, create a simple preview
          try {
            media = {
              url: image.externalUrl,
              alt: image.alt || '外部图片'
            }
          } catch (error) {
            media = undefined
          }
        }
      }
      
      return {
        title: title || '未命名分类',
        subtitle: `排序: ${sortOrder || 0}`,
        media
      }
    }
  }
})