import {defineType} from 'sanity'

export default defineType({
  name: 'aboutPage',
  title: '关于我们页面',
  type: 'document',
  initialValue: {
    title: {
      zh: '关于我们',
      en: 'About Us',
      ar: 'من نحن'
    },
    heroSection: {
      headline: {
        zh: [
          {
            _type: 'block',
            _key: 'headline-block-zh',
            style: 'normal',
            children: [
              {
                _type: 'span',
                text: '探索无限动漫世界',
                marks: []
              }
            ]
          }
        ],
        en: [
          {
            _type: 'block',
            _key: 'headline-block-en',
            style: 'normal',
            children: [
              {
                _type: 'span',
                text: 'Explore the Infinite Anime Universe',
                marks: []
              }
            ]
          }
        ],
        ar: [
          {
            _type: 'block',
            _key: 'headline-block-ar',
            style: 'normal',
            children: [
              {
                _type: 'span',
                text: 'استكشف عالم الأنمي اللامحدود',
                marks: []
              }
            ]
          }
        ]
      },
      subtitle: {
        zh: [
          {
            _type: 'block',
            _key: 'subtitle-block-zh',
            style: 'normal',
            children: [
              {
                _type: 'span',
                text: 'MyNgaPop 致力于为全球动漫爱好者提供最优质的周边产品，从精美手办到创意文具，我们用心打造每一件作品，让您的动漫梦想触手可及。',
                marks: []
              }
            ]
          }
        ],
        en: [
          {
            _type: 'block',
            _key: 'subtitle-block-en',
            style: 'normal',
            children: [
              {
                _type: 'span',
                text: 'MyNgaPop is dedicated to providing the finest anime merchandise for fans worldwide. From exquisite figures to creative stationery, we craft every item with passion to bring your anime dreams within reach.',
                marks: []
              }
            ]
          }
        ],
        ar: [
          {
            _type: 'block',
            _key: 'subtitle-block-ar',
            style: 'normal',
            children: [
              {
                _type: 'span',
                text: 'تلتزم MyNgaPop بتقديم أفضل منتجات الأنمي لمحبي الأنمي حول العالم. من الشخصيات الرائعة إلى القرطاسية الإبداعية، نحن نصنع كل عنصر بشغف لنجعل أحلام الأنمي الخاصة بك في متناول اليد.',
                marks: []
              }
            ]
          }
        ]
      }
    },
    sections: [
      {
        _type: 'textSection',
        sectionTitle: {
          zh: '我们的故事',
          en: 'Our Story',
          ar: 'قصتنا'
        },
        content: {
          zh: [
            {
              _type: 'block',
              _key: 'story-block-zh',
              style: 'normal',
              children: [
                {
                  _type: 'span',
                  text: 'MyNgaPop 成立于动漫文化蓬勃发展的时代，我们深深理解每一位动漫爱好者内心的热情与渴望。从最初的小小梦想，到如今成为连接全球动漫爱好者的桥梁，我们始终坚持品质至上的理念。',
                  marks: []
                }
              ]
            }
          ],
          en: [
            {
              _type: 'block',
              _key: 'story-block-en',
              style: 'normal',
              children: [
                {
                  _type: 'span',
                  text: 'MyNgaPop was founded during the flourishing era of anime culture, and we deeply understand the passion and desire in every anime fan\'s heart. From our initial small dream to now becoming a bridge connecting anime fans worldwide, we have always adhered to the concept of quality first.',
                  marks: []
                }
              ]
            }
          ],
          ar: [
            {
              _type: 'block',
              _key: 'story-block-ar',
              style: 'normal',
              children: [
                {
                  _type: 'span',
                  text: 'تأسست MyNgaPop خلال العصر المزدهر لثقافة الأنمي، ونحن نفهم عمق الشغف والرغبة في قلب كل محب للأنمي. من حلمنا الصغير الأولي إلى أن نصبح الآن جسرًا يربط محبي الأنمي حول العالم، لقد التزمنا دائمًا بمفهوم الجودة أولاً.',
                  marks: []
                }
              ]
            }
          ]
        }
      }
    ]
  },
  fields: [
    {
      name: 'title',
      title: '页面标题',
      type: 'localeString',
      validation: Rule => Rule.required()
    },
    {
      name: 'heroSection',
      title: '头部区域',
      type: 'object',
      fields: [
        {
          name: 'headline',
          title: '主标题',
          type: 'localeBlockContent',
        },
        {
          name: 'subtitle',
          title: '副标题',
          type: 'localeBlockContent',
        },
        {
          name: 'heroImage',
          title: '头部图片',
          type: 'flexibleImage'
        }
      ]
    },
    {
      name: 'sections',
      title: '内容区块',
      type: 'array',
      of: [
        {
          name: 'textSection',
          title: '文本区块',
          type: 'object',
          fields: [
            {
              name: 'sectionTitle',
              title: '区块标题',
              type: 'localeString',
            },
            {
              name: 'content',
              title: '内容',
              type: 'localeBlockContent',
            }
          ],
          preview: {
            select: {
              title: 'sectionTitle.zh',
              subtitle: 'sectionTitle.en'
            }
          }
        },
        {
          name: 'imageTextSection',
          title: '图文区块',
          type: 'object',
          fields: [
            {
              name: 'sectionTitle',
              title: '区块标题',
              type: 'localeString',
            },
            {
              name: 'content',
              title: '内容',
              type: 'localeBlockContent',
            },
            {
              name: 'image',
              title: '图片',
              type: 'flexibleImage'
            },
            {
              name: 'imagePosition',
              title: '图片位置',
              type: 'string',
              options: {
                list: [
                  {title: '左侧', value: 'left'},
                  {title: '右侧', value: 'right'},
                ],
                layout: 'radio'
              },
              initialValue: 'right'
            }
          ],
          preview: {
            select: {
              title: 'sectionTitle.zh',
              subtitle: 'sectionTitle.en',
              media: 'image'
            }
          }
        },
        {
          name: 'teamSection',
          title: '团队区块',
          type: 'object',
          fields: [
            {
              name: 'sectionTitle',
              title: '区块标题',
              type: 'localeString',
            },
            {
              name: 'teamMembers',
              title: '团队成员',
              type: 'array',
              of: [
                {
                  type: 'object',
                  fields: [
                    {
                      name: 'name',
                      title: '姓名',
                      type: 'localeString',
                      validation: Rule => Rule.required()
                    },
                    {
                      name: 'position',
                      title: '职位',
                      type: 'localeString',
                    },
                    {
                      name: 'bio',
                      title: '个人简介',
                      type: 'localeText',
                    },
                    {
                      name: 'photo',
                      title: '照片',
                      type: 'flexibleImage'
                    }
                  ],
                  preview: {
                    select: {
                      title: 'name.zh',
                      subtitle: 'position.zh',
                      media: 'photo'
                    }
                  }
                }
              ]
            }
          ],
          preview: {
            select: {
              title: 'sectionTitle.zh',
              subtitle: 'sectionTitle.en'
            }
          }
        }
      ]
    },
    {
      name: 'seo',
      title: 'SEO 设置',
      type: 'seo',
    }
  ],
  preview: {
    select: {
      title: 'title.zh',
      subtitle: 'title.en'
    }
  }
})