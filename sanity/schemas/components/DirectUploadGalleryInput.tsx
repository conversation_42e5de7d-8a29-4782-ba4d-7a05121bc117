import React, {useState, useCallback, useRef} from 'react'
import {ArrayOfObjectsInputProps, ObjectItem, set, unset, insert, useClient} from 'sanity'
import {
  Button,
  Card,
  Checkbox,
  Dialog,
  Flex,
  Grid,
  Heading,
  Stack,
  Text,
  useToast,
  Box,
  Badge
} from '@sanity/ui'
import styled from 'styled-components'

// Styled components for better UX
const GalleryContainer = styled(Card)`
  border: 2px dashed #e1e3e6;
  transition: all 0.2s ease;
  min-height: 200px;
  
  &.drag-over {
    border-color: #0070f3;
    background-color: #f0f8ff;
    transform: scale(1.02);
  }
  
  &:hover {
    border-color: #0070f3;
  }
`

const BatchActionBar = styled(Card)`
  position: sticky;
  top: 0;
  z-index: 20;
  border-bottom: 1px solid #e1e3e6;
`

const UploadProgress = styled(Card)`
  margin-top: 16px;
  background-color: #f8f9fa;
`

const ProgressBar = styled.div<{ value: number }>`
  width: 100%;
  height: 8px;
  background-color: #e1e3e6;
  border-radius: 4px;
  overflow: hidden;
  
  &::after {
    content: '';
    display: block;
    height: 100%;
    width: ${props => props.value}%;
    background-color: #0070f3;
    transition: width 0.3s ease;
  }
`

interface DirectUploadGalleryInputProps extends ArrayOfObjectsInputProps {
  // Additional props can be added here if needed
}

interface FlexibleImageItem {
  _key: string
  _type: string
  imageType?: 'upload' | 'external'
  uploadedImage?: any
  externalUrl?: string
  alt?: string
  caption?: string
}

interface UploadStatus {
  fileName: string
  progress: number
  status: 'uploading' | 'success' | 'error'
  error?: string
}

export default function DirectUploadGalleryInput(props: DirectUploadGalleryInputProps) {
  const {value = [], onChange, renderDefault} = props
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set())
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [isDragOver, setIsDragOver] = useState(false)
  const [uploadStatuses, setUploadStatuses] = useState<UploadStatus[]>([])
  const [isUploading, setIsUploading] = useState(false)
  
  const toast = useToast()
  const client = useClient()
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleItemSelect = useCallback((key: string, checked: boolean) => {
    setSelectedItems(prev => {
      const newSet = new Set(prev)
      if (checked) {
        newSet.add(key)
      } else {
        newSet.delete(key)
      }
      return newSet
    })
  }, [])

  const handleSelectAll = useCallback(() => {
    if (selectedItems.size === value.length) {
      setSelectedItems(new Set())
    } else {
      setSelectedItems(new Set(value.map((item: any) => item._key)))
    }
  }, [selectedItems.size, value])

  const handleBatchDelete = useCallback(() => {
    if (selectedItems.size === 0) return
    
    const newValue = value.filter((item: any) => !selectedItems.has(item._key))
    onChange(set(newValue))
    setSelectedItems(new Set())
    setShowDeleteDialog(false)
    
    toast.push({
      status: 'success',
      title: '批量删除成功',
      description: `已删除 ${selectedItems.size} 张图片`
    })
  }, [selectedItems, value, onChange, toast])

  // Upload files to Sanity CDN
  const uploadFiles = useCallback(async (files: FileList) => {
    setIsUploading(true)
    const fileArray = Array.from(files)
    
    // Initialize upload statuses
    const initialStatuses: UploadStatus[] = fileArray.map(file => ({
      fileName: file.name,
      progress: 0,
      status: 'uploading'
    }))
    setUploadStatuses(initialStatuses)

    const uploadedItems: FlexibleImageItem[] = []

    try {
      // Upload each file
      for (let i = 0; i < fileArray.length; i++) {
        const file = fileArray[i]
        
        try {
          // Update progress to show upload started
          setUploadStatuses(prev => prev.map((status, index) => 
            index === i ? { ...status, progress: 10 } : status
          ))

          // Upload to Sanity
          const asset = await client.assets.upload('image', file, {
            filename: file.name
          })

          // Update progress to 100%
          setUploadStatuses(prev => prev.map((status, index) => 
            index === i ? { ...status, progress: 100, status: 'success' } : status
          ))

          // Create flexible image item
          const newItem: FlexibleImageItem = {
            _key: `upload-${Date.now()}-${i}`,
            _type: 'flexibleImage',
            imageType: 'upload',
            uploadedImage: {
              _type: 'image',
              asset: {
                _type: 'reference',
                _ref: asset._id
              },
              alt: file.name.split('.')[0], // Use filename without extension as default alt
              caption: ''
            }
          }

          uploadedItems.push(newItem)

        } catch (error) {
          console.error('Upload failed for file:', file.name, error)
          setUploadStatuses(prev => prev.map((status, index) => 
            index === i ? { 
              ...status, 
              progress: 0, 
              status: 'error',
              error: error instanceof Error ? error.message : '上传失败'
            } : status
          ))
        }
      }

      // Add successfully uploaded items to the gallery
      if (uploadedItems.length > 0) {
        const newValue = [...value, ...uploadedItems]
        onChange(set(newValue))
        
        toast.push({
          status: 'success',
          title: '上传成功',
          description: `成功上传 ${uploadedItems.length} 张图片到 Sanity CDN`
        })
      }

      // Clear upload statuses after a delay
      setTimeout(() => {
        setUploadStatuses([])
      }, 3000)

    } catch (error) {
      console.error('Batch upload failed:', error)
      toast.push({
        status: 'error',
        title: '上传失败',
        description: '批量上传过程中发生错误'
      })
    } finally {
      setIsUploading(false)
    }
  }, [value, onChange, client, toast])

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    // Only set drag over to false if we're leaving the container entirely
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      setIsDragOver(false)
    }
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
    
    const files = e.dataTransfer.files
    if (files.length > 0) {
      // Filter for image files
      const imageFiles = Array.from(files).filter(file => 
        file.type.startsWith('image/')
      )
      
      if (imageFiles.length === 0) {
        toast.push({
          status: 'warning',
          title: '无效文件',
          description: '请拖拽图片文件 (JPG, PNG, WebP, etc.)'
        })
        return
      }

      if (imageFiles.length !== files.length) {
        toast.push({
          status: 'warning',
          title: '部分文件已忽略',
          description: '只处理图片文件，其他文件类型已忽略'
        })
      }

      // Create a new FileList with only image files
      const dt = new DataTransfer()
      imageFiles.forEach(file => dt.items.add(file))
      uploadFiles(dt.files)
    }
  }, [uploadFiles, toast])

  const handleBrowseFiles = useCallback(() => {
    fileInputRef.current?.click()
  }, [])

  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      uploadFiles(files)
    }
    // Reset the input value so the same file can be selected again
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }, [uploadFiles])

  return (
    <Stack space={4}>
      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept="image/*"
        style={{ display: 'none' }}
        onChange={handleFileInputChange}
      />

      {/* Batch Action Bar */}
      <BatchActionBar padding={3} tone="default">
        <Flex align="center" justify="space-between">
          <Flex align="center" gap={3}>
            <Checkbox
              checked={selectedItems.size === value.length && value.length > 0}
              indeterminate={selectedItems.size > 0 && selectedItems.size < value.length}
              onChange={handleSelectAll}
            />
            <Text size={1} muted>
              {selectedItems.size > 0 
                ? `已选择 ${selectedItems.size} 项` 
                : `共 ${value.length} 项`}
            </Text>
            {selectedItems.size > 0 && (
              <Badge tone="primary" text={`${selectedItems.size}`} />
            )}
          </Flex>
          
          <Flex gap={2}>
            {/* Browse Files Button */}
            <Button
              mode="ghost"
              text="📁 添加图片"
              tone="primary"
              onClick={handleBrowseFiles}
              disabled={isUploading}
            />
            
            {/* Batch Delete Button */}
            <Button
              mode="ghost"
              text="🗑️ 批量删除"
              tone="critical"
              disabled={selectedItems.size === 0}
              onClick={() => setShowDeleteDialog(true)}
            />
          </Flex>
        </Flex>
      </BatchActionBar>

      {/* Upload Progress */}
      {uploadStatuses.length > 0 && (
        <UploadProgress padding={3}>
          <Stack space={3}>
            <Heading size={1}>上传进度</Heading>
            {uploadStatuses.map((status, index) => (
              <Box key={index}>
                <Flex align="center" justify="space-between" marginBottom={2}>
                  <Text size={1}>{status.fileName}</Text>
                  <Text size={1} muted>
                    {status.status === 'success' && '✅ 完成'}
                    {status.status === 'error' && '❌ 失败'}
                    {status.status === 'uploading' && `${status.progress}%`}
                  </Text>
                </Flex>
                {status.status !== 'error' && (
                  <ProgressBar value={status.progress} />
                )}
                {status.error && (
                  <Text size={1} tone="critical">{status.error}</Text>
                )}
              </Box>
            ))}
          </Stack>
        </UploadProgress>
      )}

      {/* Drag & Drop Zone */}
      <GalleryContainer
        className={isDragOver ? 'drag-over' : ''}
        padding={4}
        tone="transparent"
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        {value.length === 0 ? (
          <Box padding={6} textAlign="center">
            <Stack space={3} align="center">
              <Text style={{fontSize: '3em', opacity: 0.5}}>🖼️</Text>
              <Heading size={2}>拖拽图片到此处上传</Heading>
              <Text size={2} muted>
                支持同时上传多张图片到 Sanity CDN
              </Text>
              <Text size={1} muted>
                或点击"添加图片"按钮选择图片
              </Text>
            </Stack>
          </Box>
        ) : (
          <Box>
            {/* Selection overlay for batch operations */}
            <Grid columns={[2, 3, 4]} gap={2} style={{marginBottom: '1rem'}}>
              {value.map((item: FlexibleImageItem) => {
                const isSelected = selectedItems.has(item._key)
                
                return (
                  <Card
                    key={`selection-${item._key}`}
                    padding={2}
                    tone={isSelected ? 'primary' : 'transparent'}
                    style={{textAlign: 'center'}}
                  >
                    <Checkbox
                      checked={isSelected}
                      onChange={(checked) => handleItemSelect(item._key, checked)}
                    />
                    <Text size={1} muted style={{marginTop: '0.5rem'}}>
                      图片 #{value.indexOf(item) + 1}
                    </Text>
                  </Card>
                )
              })}
            </Grid>
            
            {/* Default Sanity array input */}
            <Box>
              {renderDefault(props)}
            </Box>
            
            {/* Drop zone overlay when dragging */}
            {isDragOver && (
              <Box
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  backgroundColor: 'rgba(0, 112, 243, 0.1)',
                  border: '2px dashed #0070f3',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  zIndex: 10
                }}
              >
                <Text size={3} weight="bold" style={{color: '#0070f3'}}>
                  拖拽图片到此处上传
                </Text>
              </Box>
            )}
          </Box>
        )}
      </GalleryContainer>

      {/* Batch Delete Confirmation Dialog */}
      {showDeleteDialog && (
        <Dialog
          header="确认批量删除"
          id="batch-delete-dialog"
          onClose={() => setShowDeleteDialog(false)}
          footer={
            <Flex gap={2} justify="flex-end">
              <Button
                mode="ghost"
                text="取消"
                onClick={() => setShowDeleteDialog(false)}
              />
              <Button
                mode="default"
                tone="critical"
                text="🗑️ 确认删除"
                onClick={handleBatchDelete}
              />
            </Flex>
          }
        >
          <Stack space={4}>
            <Text>
              您确定要删除选中的 <strong>{selectedItems.size}</strong> 张图片吗？
            </Text>
            <Text size={1} muted>
              此操作无法撤销。
            </Text>
          </Stack>
        </Dialog>
      )}
    </Stack>
  )
}
