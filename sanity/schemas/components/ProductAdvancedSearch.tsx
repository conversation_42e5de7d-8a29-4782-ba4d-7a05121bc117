import React, {useState, useEffect, useCallback, useMemo} from 'react'
import {useClient} from 'sanity'
import {useRouter} from 'sanity/router'
import {
  Box,
  Button,
  Card,
  Container,
  Flex,
  Grid,
  Heading,
  Select,
  Stack,
  Text,
  TextInput,
  Badge,
  Spinner,
  Dialog,
  Switch,
  Label,
  Autocomplete
} from '@sanity/ui'
import {
  ProductFilter,
  buildProductQuery,
  getCategoriesQuery,
  getIpSeriesQuery,
  getTagsQuery,
  STOCK_STATUS_OPTIONS,
  CURRENCY_OPTIONS,
  SORT_OPTIONS,
  clearFilter,
  isFilterEmpty,
  loadSavedFilters,
  saveFilter,
  deleteSavedFilter,
  SavedFilter,
  generateImageUrl
} from '../../lib/productFilters'
// 导入新的现代化组件
import { FilterCard, BasicFilterCard, CategoryFilterCard, PriceFilterCard, StatusFilterCard, FilterGrid } from './FilterCard'
import { ModernProductCard, Product as ModernProduct } from './ModernProductCard'
import { StockStatusSelector } from './StockStatusSelector'
import { ProductStatusBadge } from './ProductStatusBadge'

interface CategoryOption {
  _id: string
  title: string
  value: string
  name: {zh: string, en: string, ar: string}
}

interface IpSeriesOption {
  _id: string
  title: string
  value: string
  name: {zh: string, en: string, ar: string}
}

// 使用ModernProductCard中的Product类型定义
type Product = ModernProduct

/**
 * 高级产品搜索组件
 * 提供全面的产品过滤、搜索和排序功能
 */
export function ProductAdvancedSearch() {
  const client = useClient({apiVersion: '2024-01-01'})
  const router = useRouter()
  
  // 状态管理
  const [filters, setFilters] = useState<ProductFilter>(clearFilter())
  const [products, setProducts] = useState<Product[]>([])
  const [categories, setCategories] = useState<CategoryOption[]>([])
  const [ipSeries, setIpSeries] = useState<IpSeriesOption[]>([])
  const [tags, setTags] = useState<string[]>([])
  const [sortBy, setSortBy] = useState('name asc')
  const [loading, setLoading] = useState(false)
  const [showAdvanced, setShowAdvanced] = useState(false)
  const [savedFilters, setSavedFilters] = useState<SavedFilter[]>([])
  const [saveDialogOpen, setSaveDialogOpen] = useState(false)
  const [filterName, setFilterName] = useState('')

  // 防抖搜索
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null)

  // 加载初始数据
  useEffect(() => {
    Promise.all([
      client.fetch(getCategoriesQuery()),
      client.fetch(getIpSeriesQuery()),
      client.fetch(getTagsQuery())
    ]).then(([cats, ips, tagList]) => {
      setCategories(cats)
      setIpSeries(ips)
      setTags(tagList)
    })

    setSavedFilters(loadSavedFilters())
  }, [client])

  // 搜索产品
  const searchProducts = useCallback(async (currentFilters: ProductFilter, sort: string) => {
    setLoading(true)
    try {
      const query = buildProductQuery(currentFilters, sort)
      const result = await client.fetch(query)
      setProducts(result)
    } catch (error) {
      console.error('Search error:', error)
    } finally {
      setLoading(false)
    }
  }, [client])

  // 防抖搜索效果
  useEffect(() => {
    if (searchTimeout) {
      clearTimeout(searchTimeout)
    }

    const timeout = setTimeout(() => {
      searchProducts(filters, sortBy)
    }, 300)

    setSearchTimeout(timeout)

    return () => {
      if (timeout) clearTimeout(timeout)
    }
  }, [filters, sortBy, searchProducts])

  // 更新过滤器
  const updateFilter = useCallback((key: keyof ProductFilter, value: any) => {
    setFilters(prev => ({...prev, [key]: value}))
  }, [])

  // 重置过滤器
  const resetFilters = useCallback(() => {
    setFilters(clearFilter())
    setSortBy('name asc')
  }, [])

  // 保存过滤器
  const handleSaveFilter = useCallback(() => {
    if (filterName.trim() && !isFilterEmpty(filters)) {
      saveFilter(filterName.trim(), filters)
      setSavedFilters(loadSavedFilters())
      setFilterName('')
      setSaveDialogOpen(false)
    }
  }, [filterName, filters])

  // 加载保存的过滤器
  const loadFilter = useCallback((savedFilter: SavedFilter) => {
    setFilters(savedFilter.filter)
  }, [])

  // 删除保存的过滤器
  const handleDeleteFilter = useCallback((id: string) => {
    deleteSavedFilter(id)
    setSavedFilters(loadSavedFilters())
  }, [])

  // 标签输入处理
  const handleTagInput = useCallback((value: string) => {
    const tagList = value.split(',').map(tag => tag.trim()).filter(Boolean)
    updateFilter('tags', tagList)
  }, [updateFilter])


  // 渲染紧凑型产品卡片
  const renderProductCard = useCallback((product: Product) => (
    <ModernProductCard
      key={product._id}
      product={product}
      layout="compact"
      onClick={() => {
        const href = router.resolveIntentLink('edit', {type: 'product', id: product._id})
        if (href) {
          window.open(href, '_blank')
        }
      }}
      showPrice={true}
      showDescription={false}
      maxTags={2}
      imageSize="small"
    />
  ), [router])

  const isFilterActive = !isFilterEmpty(filters)

  return (
    <Container width={5}>
      <Stack space={4}>
        {/* 标题和控制栏 */}
        <Flex justify="space-between" align="center">
          <Flex align="center" gap={3}>
            <Box
              style={{
                width: '32px',
                height: '32px',
                backgroundColor: 'var(--card-bg-color)',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '16px',
                border: '1px solid var(--card-border-color)'
              }}
            >
              🔍
            </Box>
          </Flex>
          <Flex gap={2}>
            <Button
              text={showAdvanced ? '⚙️ 高级选项' : '⚙️ 展开高级'}
              mode={showAdvanced ? 'default' : 'ghost'}
              tone={showAdvanced ? 'primary' : 'default'}
              onClick={() => setShowAdvanced(!showAdvanced)}
              style={{
                backgroundColor: showAdvanced ? 'var(--card-accent-fg-color)' : 'transparent',
                borderRadius: '6px'
              }}
            />
            <Button
              text="重置"
              icon={() => (
                <span style={{fontSize: '14px', marginRight: '4px'}}>↺</span>
              )}
              tone="critical"
              mode="ghost"
              disabled={!isFilterActive}
              onClick={resetFilters}
            />
            <Button
              text="保存"
              icon={() => (
                <span style={{fontSize: '14px', marginRight: '4px'}}>💾</span>
              )}
              tone="positive"
              mode="ghost"
              disabled={isFilterEmpty(filters)}
              onClick={() => setSaveDialogOpen(true)}
            />
          </Flex>
        </Flex>

        {/* 保存的过滤器 */}
        {savedFilters.length > 0 && (
          <Card padding={3} radius={3} tone="primary">
            <Stack space={2}>
              <Text weight="semibold" size={1}>保存的过滤器</Text>
              <Flex wrap="wrap" gap={2}>
                {savedFilters.map(saved => (
                  <Flex key={saved.id} align="center" gap={1}>
                    <Button
                      text={saved.name}
                      mode="ghost"
                      tone="primary"
                      size="small"
                      onClick={() => loadFilter(saved)}
                    />
                    <Button
                      text="🗑️"
                      mode="bleed"
                      tone="critical"
                      size="small"
                      onClick={() => handleDeleteFilter(saved.id)}
                    />
                  </Flex>
                ))}
              </Flex>
            </Stack>
          </Card>
        )}

        {/* 优化的搜索栏设计 */}
        <Card padding={4} radius={3} shadow={2} style={{
          border: '1px solid #E8EAED',
          background: 'linear-gradient(to bottom, #FAFBFC 0%, #FFFFFF 100%)'
        }}>
          <Flex gap={4} align="stretch">
            {/* 左侧：全局搜索 */}
            <Box flex={3} style={{minWidth: '300px'}}>
              <Label style={{
                marginBottom: '12px', 
                fontWeight: '600', 
                display: 'flex', 
                alignItems: 'center', 
                gap: '8px',
                color: '#1A1A1A',
                fontSize: '15px'
              }}>
                <Box style={{fontSize: '18px'}}>🔍 全局搜索</Box>
              </Label>
              <TextInput
                placeholder="搜索SKU、产品名称、描述、标签..."
                value={filters.searchAll || ''}
                onChange={(event) => updateFilter('searchAll', event.currentTarget.value)}
                style={{
                  fontSize: '15px',
                  borderRadius: '10px',
                  backgroundColor: '#F8F9FA',
                  border: '2px solid #E1E4E8',
                  padding: '14px 18px',
                  transition: 'all 0.2s',
                  ':hover': {
                    borderColor: '#C8CDD3'
                  },
                  ':focus': {
                    borderColor: '#0969DA',
                    backgroundColor: '#FFFFFF'
                  }
                }}
              />
            </Box>

            {/* 垂直分割线 */}
            <Box style={{
              width: '1px',
              backgroundColor: '#E1E4E8',
              margin: '0 8px',
              alignSelf: 'stretch'
            }} />

            {/* 右侧：筛选条件（垂直排列） */}
            <Box flex={2} style={{minWidth: '240px'}}>
              <Stack space={3}>
                {/* 分类选择器 */}
                <Box>
                  <Label style={{
                    marginBottom: '6px', 
                    fontWeight: '500',
                    fontSize: '13px',
                    color: '#57606A'
                  }}>📂 分类</Label>
                  <Select
                    value={filters.category || ''}
                    onChange={(event) => updateFilter('category', event.currentTarget.value)}
                    disabled={!!filters.searchAll}
                    style={{
                      borderRadius: '8px',
                      fontSize: '14px',
                      backgroundColor: '#F8F9FA',
                      border: '1px solid #E1E4E8',
                      padding: '6px 12px',
                      height: '36px'
                    }}
                  >
                    <option value="">所有分类</option>
                    {categories.map(cat => (
                      <option key={cat._id} value={cat._id}>
                        {cat.title}
                      </option>
                    ))}
                  </Select>
                </Box>

                {/* IP系列选择器 */}
                <Box>
                  <Label style={{
                    marginBottom: '6px', 
                    fontWeight: '500',
                    fontSize: '13px',
                    color: '#57606A'
                  }}>🎬 IP系列</Label>
                  <Select
                    value={filters.ipSeries || ''}
                    onChange={(event) => updateFilter('ipSeries', event.currentTarget.value)}
                    disabled={!!filters.searchAll}
                    style={{
                      borderRadius: '8px',
                      fontSize: '14px',
                      backgroundColor: '#F8F9FA',
                      border: '1px solid #E1E4E8',
                      padding: '6px 12px',
                      height: '36px'
                    }}
                  >
                    <option value="">所有IP系列</option>
                    {ipSeries.map(ip => (
                      <option key={ip._id} value={ip._id}>
                        {ip.title}
                      </option>
                    ))}
                  </Select>
                </Box>

                {/* 排序方式选择器 */}
                <Box>
                  <Label style={{
                    marginBottom: '6px', 
                    fontWeight: '500',
                    fontSize: '13px',
                    color: '#57606A'
                  }}>📊 排序方式</Label>
                  <Select
                    value={sortBy}
                    onChange={(event) => setSortBy(event.currentTarget.value)}
                    style={{
                      borderRadius: '8px',
                      fontSize: '14px',
                      backgroundColor: '#F8F9FA',
                      border: '1px solid #E1E4E8',
                      padding: '6px 12px',
                      height: '36px'
                    }}
                  >
                    {SORT_OPTIONS.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.title}
                      </option>
                    ))}
                  </Select>
                </Box>
              </Stack>
            </Box>
          </Flex>
        </Card>

        {/* 高级过滤器 */}
        {showAdvanced && (
          <>
            {/* 产品信息过滤 */}
            <FilterCard title="产品信息" icon="📝" collapsible defaultExpanded>
              <Stack space={4}>
                <FilterGrid columns={[1, 2]} gap={3}>
                  <Box>
                    <Label style={{marginBottom: '8px', fontWeight: '500'}}>产品名称</Label>
                    <TextInput
                      placeholder="按产品名称过滤"
                      value={filters.name || ''}
                      onChange={(event) => updateFilter('name', event.currentTarget.value)}
                      disabled={!!filters.searchAll}
                      style={{borderRadius: '8px'}}
                    />
                  </Box>

                  <Box>
                    <Label style={{marginBottom: '8px', fontWeight: '500'}}>标签 (用逗号分隔)</Label>
                    <Autocomplete
                      placeholder="输入标签，用逗号分隔"
                      value={filters.tags?.join(', ') || ''}
                      onChange={(value) => handleTagInput(value)}
                      options={tags.map(tag => ({value: tag, title: tag}))}
                      disabled={!!filters.searchAll}
                      style={{borderRadius: '8px'}}
                    />
                  </Box>
                </FilterGrid>
              </Stack>
            </FilterCard>

            {/* 价格过滤 */}
            <PriceFilterCard title="价格范围" collapsible defaultExpanded>
              <Stack space={4}>
                <FilterGrid columns={[1, 2, 3]} gap={3}>
                  <Box>
                    <Label style={{marginBottom: '8px', fontWeight: '500'}}>最低价格</Label>
                    <TextInput
                      type="number"
                      placeholder="0"
                      value={filters.minPrice?.toString() || ''}
                      onChange={(event) => updateFilter('minPrice', parseFloat(event.currentTarget.value) || undefined)}
                      disabled={!!filters.searchAll}
                      style={{borderRadius: '8px'}}
                    />
                  </Box>

                  <Box>
                    <Label style={{marginBottom: '8px', fontWeight: '500'}}>最高价格</Label>
                    <TextInput
                      type="number"
                      placeholder="无限制"
                      value={filters.maxPrice?.toString() || ''}
                      onChange={(event) => updateFilter('maxPrice', parseFloat(event.currentTarget.value) || undefined)}
                      disabled={!!filters.searchAll}
                      style={{borderRadius: '8px'}}
                    />
                  </Box>

                  <Box>
                    <Label style={{marginBottom: '8px', fontWeight: '500'}}>货币</Label>
                    <Select
                      value={filters.currency || ''}
                      onChange={(event) => updateFilter('currency', event.currentTarget.value)}
                      disabled={!!filters.searchAll}
                      style={{borderRadius: '8px'}}
                    >
                      <option value="">所有货币</option>
                      {CURRENCY_OPTIONS.map(currency => (
                        <option key={currency.value} value={currency.value}>
                          {currency.title}
                        </option>
                      ))}
                    </Select>
                  </Box>
                </FilterGrid>

                <Box>
                  <Flex align="center" gap={2}>
                    <Switch
                      checked={filters.isPublished === true}
                      onChange={(event) => updateFilter('isPublished', event.currentTarget.checked ? true : null)}
                      disabled={!!filters.searchAll}
                    />
                    <Label style={{fontWeight: '500'}}>仅显示已上架产品</Label>
                  </Flex>
                </Box>
              </Stack>
            </PriceFilterCard>

            {/* 库存状态 */}
            <StatusFilterCard title="库存状态" collapsible defaultExpanded>
              <StockStatusSelector
                selectedStatus={filters.stockStatus || []}
                onChange={(newStatus) => updateFilter('stockStatus', newStatus)}
                disabled={!!filters.searchAll}
                size="medium"
                layout="grid"
              />
            </StatusFilterCard>
          </>
        )}

        {/* 结果统计 */}
        <Card padding={3} radius={3} tone="transparent">
          <Flex justify="space-between" align="center">
            <Flex align="center" gap={3}>
              <Box
                style={{
                  backgroundColor: loading ? 'var(--card-accent-fg-color)' : 'var(--card-bg-color)',
                  borderRadius: '20px',
                  padding: '8px 16px',
                  border: '1px solid var(--card-border-color)'
                }}
              >
                <Text weight="medium" size={1}>
                  {loading ? '🔄 搜索中...' : (
                    <>
                      📊 找到 <strong style={{color: 'var(--accent-fg-color)'}}>{products.length}</strong> 个产品
                    </>
                  )}
                </Text>
              </Box>
              {isFilterActive && (
                <Badge tone="primary" mode="outline" style={{borderRadius: '12px'}}>
                  🔍 已应用过滤器
                </Badge>
              )}
              {!isFilterEmpty(filters) && (
                <Badge tone="caution" mode="outline" style={{borderRadius: '12px'}}>
                  ⚙️ {Object.values(filters).filter(v => 
                    v !== undefined && v !== null && v !== '' && 
                    !(Array.isArray(v) && v.length === 0)
                  ).length} 个筛选条件
                </Badge>
              )}
            </Flex>
            {loading && (
              <Box style={{display: 'flex', alignItems: 'center', gap: '8px'}}>
                <Spinner size={1} />
                <Text size={1} muted>处理中</Text>
              </Box>
            )}
          </Flex>
        </Card>

        {/* 产品网格 - 自适应布局 */}
        {products.length > 0 && (
          <Box style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fill, 200px)',
            gap: '12px',
            justifyContent: 'center'
          }}>
            {products.map(renderProductCard)}
          </Box>
        )}

        {/* 无结果提示 */}
        {!loading && products.length === 0 && (
          <Card padding={6} radius={3} tone="transparent" style={{textAlign: 'center'}}>
            <Stack space={3} align="center">
              <Box
                style={{
                  fontSize: '48px',
                  opacity: 0.5
                }}
              >
                🔍
              </Box>
              <Text weight="medium" size={2}>
                没有找到符合条件的产品
              </Text>
              <Text muted size={1}>
                {isFilterActive 
                  ? '尝试调整筛选条件或清除过滤器' 
                  : '暂无产品数据，请稍后再试'
                }
              </Text>
              {isFilterActive && (
                <Button
                  text="🔄 清除所有过滤器"
                  mode="ghost"
                  tone="primary"
                  onClick={resetFilters}
                  style={{marginTop: '8px'}}
                />
              )}
            </Stack>
          </Card>
        )}

        {/* 保存过滤器对话框 */}
        {saveDialogOpen && (
          <Dialog
            header="保存过滤器"
            id="save-filter-dialog"
            onClose={() => setSaveDialogOpen(false)}
            footer={
              <Flex gap={2} justify="flex-end">
                <Button
                  text="取消"
                  mode="ghost"
                  onClick={() => setSaveDialogOpen(false)}
                />
                <Button
                  text="保存"
                  tone="positive"
                  onClick={handleSaveFilter}
                  disabled={!filterName.trim()}
                />
              </Flex>
            }
          >
            <Stack space={3}>
              <Text>为当前过滤器条件命名：</Text>
              <TextInput
                placeholder="过滤器名称"
                value={filterName}
                onChange={(event) => setFilterName(event.currentTarget.value)}
                onKeyDown={(event) => {
                  if (event.key === 'Enter') {
                    handleSaveFilter()
                  }
                }}
              />
            </Stack>
          </Dialog>
        )}
      </Stack>
    </Container>
  )
}
