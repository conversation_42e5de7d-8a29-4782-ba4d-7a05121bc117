import React, { useCallback } from 'react'
import { <PERSON><PERSON>, Button, Text, Card, Flex, Badge } from '@sanity/ui'
import { CloseIcon } from '@sanity/icons'
import { StringInputProps, set, unset } from 'sanity'

// Predefined tags with translations
const PREDEFINED_TAGS = [
  { value: 'new', label: '新品', color: 'positive', bgColor: '#e6f7e6' },
  { value: 'popular', label: '热门', color: 'purple', bgColor: '#f3e6ff' },
  { value: 'limited', label: '限定', color: 'critical', bgColor: '#ffe6e6' },
  { value: 'preorder', label: '预售', color: 'caution', bgColor: '#fff7e6' },
  { value: 'exclusive', label: '独家', color: 'primary', bgColor: '#e6f0ff' },
  { value: 'sale', label: '促销', color: 'warning', bgColor: '#fff0e6' }
]

export default function TagsInput(props: StringInputProps) {
  const { value = [], onChange } = props
  
  // Ensure value is always an array
  const tags = Array.isArray(value) ? value : []

  const handleToggleTag = useCallback(
    (tagValue: string) => {
      const currentTags = [...tags]
      const index = currentTags.indexOf(tagValue)
      
      if (index > -1) {
        // Remove tag
        currentTags.splice(index, 1)
      } else {
        // Add tag
        currentTags.push(tagValue)
      }
      
      onChange(currentTags.length > 0 ? set(currentTags) : unset())
    },
    [tags, onChange]
  )

  const handleRemoveTag = useCallback(
    (tagValue: string) => {
      const currentTags = tags.filter(t => t !== tagValue)
      onChange(currentTags.length > 0 ? set(currentTags) : unset())
    },
    [tags, onChange]
  )

  return (
    <Stack space={3}>
      <Text size={1} weight="semibold" muted>
        点击下方标签进行选择
      </Text>
      
      <Flex gap={2} wrap="wrap">
        {PREDEFINED_TAGS.map((tag) => {
          const isSelected = tags.includes(tag.value)
          
          return (
            <Button
              key={tag.value}
              mode={isSelected ? 'default' : 'ghost'}
              tone={isSelected ? tag.color as any : 'default'}
              fontSize={1}
              padding={2}
              onClick={() => handleToggleTag(tag.value)}
              text={tag.label}
            />
          )
        })}
      </Flex>

      {tags.length > 0 && (
        <Card padding={3} radius={2} tone="default" border>
          <Stack space={2}>
            <Text size={1} weight="semibold" muted>
              已选择的标签（点击 × 删除）：
            </Text>
            <Flex gap={2} wrap="wrap">
              {tags.map((tagValue) => {
                const tag = PREDEFINED_TAGS.find(t => t.value === tagValue)
                const isCustomTag = !tag
                
                return (
                  <Badge
                    key={tagValue}
                    tone={tag?.color as any || 'default'}
                    mode="default"
                    padding={2}
                    radius={2}
                    style={{ 
                      display: 'inline-flex', 
                      alignItems: 'center', 
                      gap: '8px'
                    }}
                  >
                    <Text size={1} weight="medium">
                      {tag?.label || tagValue}
                      {isCustomTag && ' (自定义)'}
                    </Text>
                    <Button
                      mode="bleed"
                      tone="default"
                      padding={0}
                      fontSize={0}
                      icon={CloseIcon}
                      onClick={() => handleRemoveTag(tagValue)}
                      style={{ width: '18px', height: '18px', opacity: 0.7 }}
                      title={`删除标签: ${tag?.label || tagValue}`}
                    />
                  </Badge>
                )
              })}
            </Flex>
          </Stack>
        </Card>
      )}
    </Stack>
  )
}