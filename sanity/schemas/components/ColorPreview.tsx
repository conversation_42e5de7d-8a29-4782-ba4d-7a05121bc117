import React from 'react'
import { ObjectFieldProps } from 'sanity'

export function ColorPreview(props: ObjectFieldProps) {
  const { value } = props
  const primaryColor = value?.primaryColor?.hex || '#3B82F6'
  const secondaryColor = value?.secondaryColor?.hex || '#A855F7'
  
  return (
    <div>
      {/* Original field rendering */}
      {props.renderDefault(props)}
      
      {/* Color preview section */}
      <div style={{
        marginTop: '20px',
        padding: '20px',
        backgroundColor: '#f9fafb',
        borderRadius: '8px',
        border: '1px solid #e5e7eb'
      }}>
        <h4 style={{ margin: '0 0 16px 0', fontSize: '14px', fontWeight: '600' }}>
          颜色预览
        </h4>
        
        {/* Color swatches */}
        <div style={{ display: 'flex', gap: '12px', marginBottom: '20px' }}>
          <div>
            <div style={{
              width: '80px',
              height: '80px',
              backgroundColor: primaryColor,
              borderRadius: '8px',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
            }} />
            <p style={{ marginTop: '8px', fontSize: '12px', color: '#6b7280' }}>
              主色调<br />
              {primaryColor}
            </p>
          </div>
          
          <div>
            <div style={{
              width: '80px',
              height: '80px',
              backgroundColor: secondaryColor,
              borderRadius: '8px',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
            }} />
            <p style={{ marginTop: '8px', fontSize: '12px', color: '#6b7280' }}>
              辅助色调<br />
              {secondaryColor}
            </p>
          </div>
        </div>
        
        {/* Sample UI preview */}
        <div style={{
          padding: '20px',
          backgroundColor: '#ffffff',
          borderRadius: '8px',
          border: '1px solid #e5e7eb'
        }}>
          <h5 style={{ margin: '0 0 12px 0', fontSize: '13px', fontWeight: '500' }}>
            示例效果
          </h5>
          
          {/* Sample button */}
          <button style={{
            padding: '10px 20px',
            backgroundColor: primaryColor,
            color: '#ffffff',
            border: 'none',
            borderRadius: '6px',
            fontSize: '14px',
            fontWeight: '500',
            cursor: 'pointer',
            marginRight: '12px'
          }}>
            主要按钮
          </button>
          
          <button style={{
            padding: '10px 20px',
            backgroundColor: 'transparent',
            color: primaryColor,
            border: `2px solid ${primaryColor}`,
            borderRadius: '6px',
            fontSize: '14px',
            fontWeight: '500',
            cursor: 'pointer',
            marginRight: '12px'
          }}>
            次要按钮
          </button>
          
          {/* Sample gradient */}
          <div style={{
            marginTop: '16px',
            padding: '20px',
            background: `linear-gradient(135deg, ${primaryColor} 0%, ${secondaryColor} 100%)`,
            borderRadius: '8px',
            color: '#ffffff',
            fontSize: '14px',
            fontWeight: '500',
            textAlign: 'center'
          }}>
            渐变背景示例
          </div>
          
          {/* Sample text */}
          <p style={{
            marginTop: '16px',
            fontSize: '14px',
            lineHeight: '1.6'
          }}>
            这是普通文本，<a href="#" style={{ color: primaryColor, textDecoration: 'none', fontWeight: '500' }}>这是链接文本</a>。
            背景可以使用 <span style={{ backgroundColor: secondaryColor + '20', padding: '2px 6px', borderRadius: '4px' }}>辅助色调的淡色版本</span>。
          </p>
        </div>
      </div>
    </div>
  )
}