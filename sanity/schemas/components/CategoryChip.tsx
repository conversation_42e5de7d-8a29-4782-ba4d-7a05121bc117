import React from 'react'
import {
  Badge,
  Box,
  Flex,
  Text
} from '@sanity/ui'

export interface CategoryInfo {
  _id: string
  name: {zh: string, en: string, ar: string}
}

export interface IPSeriesInfo {
  _id: string
  name: {zh: string, en: string, ar: string}
}

export interface CategoryChipProps {
  category: CategoryInfo
  size?: 'small' | 'medium' | 'large'
  showIcon?: boolean
  clickable?: boolean
  onClick?: () => void
}

export interface IPSeriesChipProps {
  ipSeries: IPSeriesInfo
  size?: 'small' | 'medium' | 'large'
  showIcon?: boolean
  clickable?: boolean
  onClick?: () => void
}

/**
 * 获取分类的预定义颜色和图标
 */
function getCategoryStyle(categoryName: string) {
  const name = categoryName.toLowerCase()
  
  // 根据分类名称返回不同的样式
  if (name.includes('手办') || name.includes('figure')) {
    return { color: '#E91E63', bgColor: '#FCE4EC', icon: '🎭' }
  }
  if (name.includes('毛绒') || name.includes('plush')) {
    return { color: '#FF9800', bgColor: '#FFF3E0', icon: '🧸' }
  }
  if (name.includes('徽章') || name.includes('badge')) {
    return { color: '#2196F3', bgColor: '#E3F2FD', icon: '🏷️' }
  }
  if (name.includes('钥匙扣') || name.includes('keychain')) {
    return { color: '#9C27B0', bgColor: '#F3E5F5', icon: '🔑' }
  }
  if (name.includes('海报') || name.includes('poster')) {
    return { color: '#607D8B', bgColor: '#ECEFF1', icon: '🖼️' }
  }
  if (name.includes('服装') || name.includes('clothes')) {
    return { color: '#795548', bgColor: '#EFEBE9', icon: '👕' }
  }
  
  // 默认样式
  return { color: '#666', bgColor: '#F5F5F5', icon: '📦' }
}

/**
 * 获取IP系列的预定义颜色和图标
 */
function getIPSeriesStyle(seriesName: string) {
  const name = seriesName.toLowerCase()
  
  // 根据IP系列名称返回不同的样式
  if (name.includes('初音') || name.includes('miku')) {
    return { color: '#00BCD4', bgColor: '#E0F7FA', icon: '🎵' }
  }
  if (name.includes('海贼王') || name.includes('one piece')) {
    return { color: '#FF5722', bgColor: '#FBE9E7', icon: '🏴‍☠️' }
  }
  if (name.includes('火影') || name.includes('naruto')) {
    return { color: '#FF9800', bgColor: '#FFF3E0', icon: '🍥' }
  }
  if (name.includes('龙珠') || name.includes('dragon ball')) {
    return { color: '#FFC107', bgColor: '#FFFDE7', icon: '🐉' }
  }
  if (name.includes('进击') || name.includes('attack on titan')) {
    return { color: '#8BC34A', bgColor: '#F1F8E9', icon: '⚔️' }
  }
  if (name.includes('鬼灭') || name.includes('demon slayer')) {
    return { color: '#E91E63', bgColor: '#FCE4EC', icon: '🗡️' }
  }
  
  // 默认样式
  return { color: '#9C27B0', bgColor: '#F3E5F5', icon: '🎬' }
}

/**
 * 分类芯片组件
 */
export function CategoryChip({
  category,
  size = 'medium',
  showIcon = true,
  clickable = false,
  onClick
}: CategoryChipProps) {
  const categoryName = category.name?.zh || ''
  const style = getCategoryStyle(categoryName)
  
  const fontSize = size === 'small' ? 0 : size === 'large' ? 2 : 1
  const padding = size === 'small' ? 1 : size === 'large' ? 3 : 2
  const iconSize = size === 'small' ? '12px' : size === 'large' ? '16px' : '14px'

  const chipContent = (
    <Flex align="center" gap={1}>
      {showIcon && (
        <Box style={{ fontSize: iconSize }}>
          {style.icon}
        </Box>
      )}
      <Text size={fontSize} weight="medium">
        {categoryName}
      </Text>
    </Flex>
  )

  if (clickable) {
    return (
      <Box
        as="button"
        padding={padding}
        style={{
          backgroundColor: style.bgColor,
          color: style.color,
          border: `1px solid ${style.color}`,
          borderRadius: '16px',
          cursor: 'pointer',
          transition: 'all 0.2s ease-in-out',
          ':hover': {
            backgroundColor: style.color,
            color: 'white',
            transform: 'translateY(-1px)',
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
          }
        }}
        onClick={onClick}
      >
        {chipContent}
      </Box>
    )
  }

  return (
    <Badge
      tone="primary"
      mode="outline"
      fontSize={fontSize}
      padding={padding}
      style={{
        backgroundColor: style.bgColor,
        color: style.color,
        borderColor: style.color,
        borderRadius: '16px'
      }}
    >
      {chipContent}
    </Badge>
  )
}

/**
 * IP系列芯片组件
 */
export function IPSeriesChip({
  ipSeries,
  size = 'medium',
  showIcon = true,
  clickable = false,
  onClick
}: IPSeriesChipProps) {
  const seriesName = ipSeries.name?.zh || ''
  const style = getIPSeriesStyle(seriesName)
  
  const fontSize = size === 'small' ? 0 : size === 'large' ? 2 : 1
  const padding = size === 'small' ? 1 : size === 'large' ? 3 : 2
  const iconSize = size === 'small' ? '12px' : size === 'large' ? '16px' : '14px'

  const chipContent = (
    <Flex align="center" gap={1}>
      {showIcon && (
        <Box style={{ fontSize: iconSize }}>
          {style.icon}
        </Box>
      )}
      <Text size={fontSize} weight="medium">
        {seriesName}
      </Text>
    </Flex>
  )

  if (clickable) {
    return (
      <Box
        as="button"
        padding={padding}
        style={{
          backgroundColor: style.bgColor,
          color: style.color,
          border: `1px solid ${style.color}`,
          borderRadius: '20px',
          cursor: 'pointer',
          transition: 'all 0.2s ease-in-out',
          ':hover': {
            backgroundColor: style.color,
            color: 'white',
            transform: 'translateY(-1px)',
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
          }
        }}
        onClick={onClick}
      >
        {chipContent}
      </Box>
    )
  }

  return (
    <Badge
      tone="caution"
      mode="outline"
      fontSize={fontSize}
      padding={padding}
      style={{
        backgroundColor: style.bgColor,
        color: style.color,
        borderColor: style.color,
        borderRadius: '20px'
      }}
    >
      {chipContent}
    </Badge>
  )
}

/**
 * 芯片组合显示组件
 */
export interface ChipGroupProps {
  category?: CategoryInfo
  ipSeries?: IPSeriesInfo
  size?: 'small' | 'medium' | 'large'
  showIcons?: boolean
  clickable?: boolean
  onCategoryClick?: () => void
  onIPSeriesClick?: () => void
  layout?: 'horizontal' | 'vertical' | 'wrap'
}

export function ChipGroup({
  category,
  ipSeries,
  size = 'medium',
  showIcons = true,
  clickable = false,
  onCategoryClick,
  onIPSeriesClick,
  layout = 'horizontal'
}: ChipGroupProps) {
  const direction = layout === 'vertical' ? 'column' : 'row'
  const wrap = layout === 'wrap' ? 'wrap' : 'nowrap'
  const gap = layout === 'vertical' ? 2 : 1

  return (
    <Flex direction={direction} wrap={wrap} gap={gap} align="flex-start">
      {category && (
        <CategoryChip
          category={category}
          size={size}
          showIcon={showIcons}
          clickable={clickable}
          onClick={onCategoryClick}
        />
      )}
      {ipSeries && (
        <IPSeriesChip
          ipSeries={ipSeries}
          size={size}
          showIcon={showIcons}
          clickable={clickable}
          onClick={onIPSeriesClick}
        />
      )}
    </Flex>
  )
}