import React, {useState, useEffect} from 'react'
import {useClient} from 'sanity'
import {<PERSON>, Badge, Flex, Spinner, Text} from '@sanity/ui'
import {getPredefinedFilterCountQueries, PREDEFINED_FILTERS} from '../../lib/productFilters'

interface FilterCounts {
  [key: string]: number
}

interface FilterListWithCountsProps {
  S: any // Structure builder
}

/**
 * 创建带数量统计的预定义过滤器列表
 */
export function createFilterListWithCounts(S: any) {
  const FilterListWithCounts: React.FC = () => {
    const client = useClient({apiVersion: '2024-01-01'})
    const [counts, setCounts] = useState<FilterCounts>({})
    const [loading, setLoading] = useState(true)

    useEffect(() => {
      const fetchCounts = async () => {
        try {
          setLoading(true)
          const queries = getPredefinedFilterCountQueries()
          const countPromises = Object.entries(queries).map(async ([key, query]) => {
            const count = await client.fetch(query)
            return [key, count]
          })

          const results = await Promise.all(countPromises)
          const countsMap = Object.fromEntries(results)
          setCounts(countsMap)
        } catch (error) {
          console.error('Failed to fetch filter counts:', error)
        } finally {
          setLoading(false)
        }
      }

      fetchCounts()
      
      // 每30秒刷新一次数量
      const interval = setInterval(fetchCounts, 30000)
      return () => clearInterval(interval)
    }, [client])

    return (
      <Box>
        {Object.entries(PREDEFINED_FILTERS).map(([key, config]) => {
          const count = counts[key]
          const displayTitle = loading ? 
            `${config.icon} ${config.title}` : 
            `${config.icon} ${config.title} ${count !== undefined ? `(${count})` : ''}`

          return (
            <Box key={key} marginBottom={1}>
              <Text>{displayTitle}</Text>
              {loading && <Spinner size={0} style={{marginLeft: 8}} />}
            </Box>
          )
        })}
      </Box>
    )
  }

  // 创建预定义过滤器列表项
  return Object.entries(PREDEFINED_FILTERS).map(([key, config]) =>
    S.listItem()
      .id(`products-${key}`)
      .title(`${config.icon} ${config.title}`)
      .child(
        S.documentList()
          .id(`products-${key}-list`)
          .title(config.title)
          .filter(config.filter)
          .apiVersion('2024-01-01')
          .defaultOrdering([{field: '_updatedAt', direction: 'desc'}])
          .child((documentId: string) =>
            S.document()
              .documentId(documentId)
              .schemaType('product')
          )
      )
  )
}

/**
 * 创建带数量统计的分类浏览器
 */
export function createCategoryBrowserWithCounts(S: any) {
  return S.listItem()
    .id('products-by-category')
    .title('📂 按分类浏览')
    .child(
      S.documentList()
        .id('category-list')
        .title('选择分类')
        .filter('_type == "category"')
        .apiVersion('2024-01-01')
        .defaultOrdering([{field: 'name.zh', direction: 'asc'}])
        .child((categoryId: string) =>
          S.documentList()
            .id(`products-by-category-${categoryId}`)
            .title('该分类下的产品')
            .filter('_type == "product" && category._ref == $categoryId')
            .params({ categoryId })
            .apiVersion('2024-01-01')
            .defaultOrdering([{field: '_updatedAt', direction: 'desc'}])
            .child((documentId: string) =>
              S.document()
                .documentId(documentId)
                .schemaType('product')
            )
        )
    )
}

/**
 * 创建带数量统计的IP系列浏览器
 */
export function createIpSeriesBrowserWithCounts(S: any) {
  return S.listItem()
    .id('products-by-ip-series')
    .title('🎬 按IP系列浏览')
    .child(
      S.documentList()
        .id('ip-series-list')
        .title('选择IP系列')
        .filter('_type == "ipSeries"')
        .apiVersion('2024-01-01')
        .defaultOrdering([{field: 'name.zh', direction: 'asc'}])
        .child((seriesId: string) =>
          S.documentList()
            .id(`products-by-ip-series-${seriesId}`)
            .title('该IP系列下的产品')
            .filter('_type == "product" && ipSeries._ref == $seriesId')
            .params({ seriesId })
            .apiVersion('2024-01-01')
            .defaultOrdering([{field: '_updatedAt', direction: 'desc'}])
            .child((documentId: string) =>
              S.document()
                .documentId(documentId)
                .schemaType('product')
            )
        )
    )
}