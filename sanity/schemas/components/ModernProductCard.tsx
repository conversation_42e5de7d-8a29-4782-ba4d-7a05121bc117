import React from 'react'
import {
  Box,
  Card,
  Stack,
  Text,
  Flex,
  Badge
} from '@sanity/ui'
import { CategoryChip, IPSeriesChip, ChipGroup, CategoryInfo, IPSeriesInfo } from './CategoryChip'
import { ProductStatusBadge } from './ProductStatusBadge'
import { generateImageUrl } from '../../lib/productFilters'

export interface Product {
  _id: string
  sku?: string
  name: {zh: string, en: string, ar: string}
  category?: CategoryInfo
  ipSeries?: IPSeriesInfo
  price?: number
  currency?: string
  stockStatus?: string
  isPublished?: boolean
  publishedAt?: string
  tags?: string[]
  gallery?: {
    imageType?: 'upload' | 'external'
    uploadedImage?: {
      asset?: {
        _id: string
        url: string
        metadata?: {
          dimensions?: {
            width: number
            height: number
          }
        }
      }
      alt?: string
      hotspot?: any
      crop?: any
    }
    externalUrl?: string
    alt?: string
    fallbackImage?: {
      asset?: {
        _id: string
        url: string
        metadata?: {
          dimensions?: {
            width: number
            height: number
          }
        }
      }
    }
  }[]
  shortDescription?: {zh: string, en: string, ar: string}
}

export interface ModernProductCardProps {
  product: Product
  layout?: 'compact' | 'comfortable' | 'spacious'
  onClick?: () => void
  showPrice?: boolean
  showDescription?: boolean
  maxTags?: number
  imageSize?: 'small' | 'medium' | 'large'
}

/**
 * 现代化的产品卡片组件
 * 提供更美观的布局和更好的信息展示
 */
export function ModernProductCard({
  product,
  layout = 'comfortable',
  onClick,
  showPrice = true,
  showDescription = true,
  maxTags = 3,
  imageSize = 'medium'
}: ModernProductCardProps) {
  const getImageAlt = (): string => {
    const mainImage = product.gallery?.[0]
    if (mainImage?.imageType === 'upload') {
      return mainImage.uploadedImage?.alt || product.name?.zh || '产品图片'
    }
    if (mainImage?.imageType === 'external') {
      return mainImage.alt || product.name?.zh || '产品图片'
    }
    return mainImage?.alt || product.name?.zh || '产品图片'
  }

  const renderProductImage = () => {
    const mainImage = product.gallery?.[0]
    const sizeMap = {
      small: { width: '100%', height: '100%' },
      medium: { width: '100%', height: '100%' },
      large: { width: '100%', height: '100%' }
    }
    const { width, height } = sizeMap[imageSize]
    const imageUrl = generateImageUrl(mainImage, 200, 200)
    const altText = getImageAlt()
    
    if (!imageUrl) {
      return (
        <Box
          style={{
            width: '100%',
            height: '100%',
            backgroundColor: '#F8F9FA',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '32px',
            color: '#999',
            position: 'relative'
          }}
        >
          🖼️
          <Text size={0} muted style={{ marginTop: '4px', fontSize: '10px' }}>
            暂无图片
          </Text>
        </Box>
      )
    }

    return (
      <Box style={{ position: 'relative' }}>
        <img
          src={imageUrl}
          alt={altText}
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'cover',
            objectPosition: 'center',
            backgroundColor: '#F8F9FA',
            transition: 'transform 0.2s ease-in-out'
          }}
          onError={(e) => {
            const target = e.target as HTMLImageElement
            target.style.display = 'none'
            const placeholder = target.nextElementSibling as HTMLElement
            if (placeholder) {
              placeholder.style.display = 'flex'
            }
          }}
        />
        {/* 错误时的占位符 */}
        <Box
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            backgroundColor: '#F8F9FA',
            display: 'none',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '20px',
            color: '#999'
          }}
        >
          {product.gallery?.[0]?.imageType === 'external' ? '🌐' : '🖼️'}
          <Text size={0} muted style={{ fontSize: '8px' }}>
            加载失败
          </Text>
        </Box>
        {/* 外部图片标识 */}
        {product.gallery?.[0]?.imageType === 'external' && (
          <Box
            style={{
              position: 'absolute',
              top: '6px',
              right: '6px',
              width: '20px',
              height: '20px',
              backgroundColor: 'rgba(0,0,0,0.7)',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '10px'
            }}
          >
            🌐
          </Box>
        )}
        {/* 发布状态指示器 */}
        {!product.isPublished && (
          <Box
            style={{
              position: 'absolute',
              top: '6px',
              left: '6px',
              backgroundColor: 'rgba(255, 152, 0, 0.9)',
              borderRadius: '8px',
              padding: '2px 6px',
              fontSize: '10px',
              color: 'white',
              fontWeight: 'bold'
            }}
          >
            草稿
          </Box>
        )}
      </Box>
    )
  }

  const cardPadding = layout === 'compact' ? 1 : layout === 'spacious' ? 3 : 2
  const gap = layout === 'compact' ? 2 : layout === 'spacious' ? 4 : 3
  const titleSize = layout === 'compact' ? 1 : layout === 'spacious' ? 3 : 2

  const isCompactLayout = layout === 'compact'
  
  return (
    <Card
      padding={0}
      radius={3}
      shadow={1}
      style={{
        cursor: onClick ? 'pointer' : 'default',
        border: '1px solid #E8EAED',
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        position: 'relative',
        overflow: 'hidden',
        background: '#FFFFFF',
        width: '200px',
        height: '200px',
        display: 'flex',
        flexDirection: 'column'
      }}
      onClick={onClick}
    >
      <Flex gap={0} align="stretch" direction="column" style={{height: '100%'}}>
        {/* 产品图片区域 - 100px */}
        <Box style={{ 
          height: '100px',
          flexShrink: 0,
          position: 'relative',
          overflow: 'hidden',
          backgroundColor: '#F8F9FA'
        }}>
          {renderProductImage()}
        </Box>

        {/* 产品信息区域 - 100px */}
        <Box style={{ 
          height: '100px',
          padding: '8px',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between',
          backgroundColor: '#FFFFFF',
          overflow: 'visible',
          position: 'relative'
        }}>
          <Stack space={0}>
            {/* 产品名称 */}
            <Text 
              weight="semibold" 
              size={0} 
              style={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                color: '#1A1A1A',
                lineHeight: 2.3,
                marginBottom: '3px',
                fontSize: '12px'
              }}
            >
              {product.name?.zh || '未命名产品'}
            </Text>

            {/* SKU */}
            {product.sku && (
              <Box style={{marginBottom: '3px'}}>
                <Badge fontSize={0} style={{
                  padding: '1px 4px',
                  fontFamily: 'monospace',
                  fontSize: '10px',
                  backgroundColor: '#FFEBEE',
                  color: '#C62828',
                  borderRadius: '3px'
                }}>
                  {product.sku}
                </Badge>
              </Box>
            )}

            {/* 分类 和 IP */}
            <Flex gap={1} align="center" wrap="wrap" style={{marginBottom: '3px'}}>
              {product.category && (
                <Badge
                  tone="primary"
                  fontSize={0}
                  style={{
                    padding: '1px 4px',
                    borderRadius: '8px',
                    fontSize: '9px',
                    lineHeight: '1.2',
                  }}
                >
                  {product.category.name?.zh}
                </Badge>
              )}
              {product.ipSeries && (
                <Badge
                  tone="primary"
                  fontSize={0}
                  style={{
                    padding: '1px 4px',
                    borderRadius: '8px',
                    fontSize: '9px',
                    lineHeight: '1.2',
                  }}
                >
                  {product.ipSeries.name?.zh}
                </Badge>
              )}
            </Flex>

            {/* 状态标签 */}
            <Flex gap={1} align="center" wrap="wrap" style={{marginBottom: '3px'}}>
              <Badge 
                tone={product.isPublished ? 'positive' : 'caution'} 
                fontSize={0}
                style={{
                  padding: '1px 4px',
                  borderRadius: '8px',
                  fontSize: '9px',
                  lineHeight: '1.3'
                }}
              >
                {product.isPublished ? '上架' : '草稿'}
              </Badge>

              {product.stockStatus && (
                <Badge 
                  tone={
                    product.stockStatus === 'in-stock' ? 'positive' : 
                    product.stockStatus === 'pre-order' ? 'caution' : 'critical'
                  }
                  fontSize={0}
                  style={{
                    padding: '1px 4px',
                    borderRadius: '8px',
                    fontSize: '9px',
                    lineHeight: '1.2'
                  }}
                >
                  {product.stockStatus === 'in-stock' && '现货'}
                  {product.stockStatus === 'pre-order' && '预售'}
                  {product.stockStatus === 'sold-out' && '售罄'}
                </Badge>
              )}
            </Flex>

          </Stack>
          
          {/* 价格 - 固定在底部 */}
          {showPrice && product.price !== undefined && product.price > 0 && (
            <Box style={{ marginTop: 'auto' }}>
              <Text size={1} weight="bold" style={{ 
                color: '#D32F2F',
                fontSize: '14px'
              }}>
                {product.currency === 'CNY' ? '¥' : 
                 product.currency === 'USD' ? '$' : 
                 product.currency === 'AED' ? 'د.إ' : ''}
                {product.price.toLocaleString()}
              </Text>
            </Box>
          )}
        </Box>
      </Flex>

      {/* 悬停时的装饰效果 */}
      {onClick && (
        <>
          <Box
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              height: '3px',
              background: 'linear-gradient(90deg, #1976D2, #42A5F5)',
              transform: 'scaleX(0)',
              transformOrigin: 'left',
              transition: 'transform 0.3s ease-in-out'
            }}
            className="hover-accent"
          />
          <style jsx>{`
            .hover-accent {
              transform: scaleX(0);
            }
            Card:hover .hover-accent {
              transform: scaleX(1);
            }
          `}</style>
        </>
      )}
    </Card>
  )
}

/**
 * 预定义的产品卡片变体
 */
export const CompactProductCard = (props: Omit<ModernProductCardProps, 'layout'>) => (
  <ModernProductCard {...props} layout="compact" />
)

export const ComfortableProductCard = (props: Omit<ModernProductCardProps, 'layout'>) => (
  <ModernProductCard {...props} layout="comfortable" />
)

export const SpaciousProductCard = (props: Omit<ModernProductCardProps, 'layout'>) => (
  <ModernProductCard {...props} layout="spacious" />
)
