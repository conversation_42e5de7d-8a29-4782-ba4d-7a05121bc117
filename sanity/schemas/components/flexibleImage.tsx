import React from 'react'
import {defineType, defineField} from 'sanity'
import ExternalImagePreview from './ExternalImagePreview'
import ImageErrorBoundary from './ImageErrorBoundary'

// Validation function for external image URLs
const validateImageUrl = (url: string): boolean | string => {
  if (!url) return true // Allow empty URLs
  
  try {
    const parsedUrl = new URL(url)
    
    // Check if domain is allowed
    const allowedDomains = [
      'images.unsplash.com',
      'unsplash.com',
      'source.unsplash.com'
    ]
    
    const isAllowedDomain = allowedDomains.some(domain => 
      parsedUrl.hostname === domain || parsedUrl.hostname.endsWith('.' + domain)
    )
    
    if (!isAllowedDomain) {
      return `只允许来自以下域名的图片: ${allowedDomains.join(', ')}`
    }
    
    // Check if URL looks like an image
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.webp', '.avif', '.gif']
    const hasImageExtension = imageExtensions.some(ext => 
      parsedUrl.pathname.toLowerCase().includes(ext)
    )
    
    // For Unsplash, we allow URLs without explicit extensions
    if (!hasImageExtension && !parsedUrl.hostname.includes('unsplash')) {
      return '请提供有效的图片 URL'
    }
    
    return true
  } catch (error) {
    return '请提供有效的 URL'
  }
}

export default defineType({
  name: 'flexibleImage',
  title: '灵活图片',
  type: 'object',
  fields: [
    defineField({
      name: 'imageType',
      title: '图片类型',
      type: 'string',
      options: {
        list: [
          {title: '上传到 Sanity', value: 'upload'},
          {title: '外部链接', value: 'external'}
        ],
        layout: 'radio'
      },
      initialValue: 'upload'
    }),
    defineField({
      name: 'uploadedImage',
      title: '上传图片',
      type: 'image',
      options: {
        hotspot: true,
        metadata: ['blurhash', 'lqip', 'palette']
      },
      fields: [
        {
          name: 'alt',
          type: 'string',
          title: '替代文本',
          description: '图片的描述文本，用于辅助功能和SEO'
        },
        {
          name: 'caption',
          type: 'string',
          title: '图片说明',
          description: '可选的图片说明文字'
        }
      ],
      hidden: ({parent}) => parent?.imageType !== 'upload'
    }),
    defineField({
      name: 'externalUrl',
      title: '外部图片链接',
      type: 'url',
      description: '支持 Unsplash 等外部图片源',
      validation: Rule => Rule.custom(validateImageUrl),
      hidden: ({parent}) => parent?.imageType !== 'external',
      components: {
        input: (props) => {
          const {value, onChange} = props
          return (
            <div>
              <div style={{marginBottom: '10px'}}>
                <props.renderDefault {...props} />
              </div>
              {value && (
                <ImageErrorBoundary>
                  <ExternalImagePreview
                    url={value}
                    alt="外部图片预览"
                  />
                </ImageErrorBoundary>
              )}
            </div>
          )
        }
      }
    }),
    defineField({
      name: 'alt',
      title: '替代文本',
      type: 'string',
      description: '图片的描述文本，用于辅助功能和SEO',
      hidden: ({parent}) => parent?.imageType !== 'external'
    }),
    defineField({
      name: 'caption',
      title: '图片说明',
      type: 'string',
      description: '可选的图片说明文字',
      hidden: ({parent}) => parent?.imageType !== 'external'
    }),
    defineField({
      name: 'fallbackImage',
      title: '备用图片',
      type: 'image',
      description: '外部图片加载失败时使用的备用图片',
      options: {
        hotspot: true
      },
      hidden: ({parent}) => parent?.imageType !== 'external'
    }),
  ],
  preview: {
    select: {
      imageType: 'imageType',
      uploadedImage: 'uploadedImage',
      externalUrl: 'externalUrl',
      alt: 'alt'
    },
    prepare(selection) {
      const {imageType, uploadedImage, externalUrl, alt} = selection
      
      if (imageType === 'upload' && uploadedImage) {
        return {
          title: alt || uploadedImage.alt || '上传的图片',
          subtitle: '来源: Sanity',
          media: uploadedImage
        }
      }
      
      if (imageType === 'external' && externalUrl) {
        // For external images, create a simple image object for preview
        try {
          const hostname = new URL(externalUrl).hostname
          return {
            title: alt || '外部图片',
            subtitle: `来源: ${hostname}`,
            media: {
              url: externalUrl,
              alt: alt || '外部图片'
            }
          }
        } catch (error) {
          return {
            title: alt || '外部图片',
            subtitle: '来源: 外部链接 (URL 错误)',
            media: undefined
          }
        }
      }
      
      return {
        title: '未配置图片',
        subtitle: '请选择图片类型并添加图片',
        media: undefined
      }
    }
  }
})