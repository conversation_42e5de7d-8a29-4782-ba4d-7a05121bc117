import React from 'react'
import {
  Box,
  Card,
  Stack,
  Text,
  Flex,
  Button
} from '@sanity/ui'

export interface FilterCardProps {
  title: string
  icon?: string
  children: React.ReactNode
  collapsible?: boolean
  defaultExpanded?: boolean
  tone?: 'default' | 'primary' | 'positive' | 'caution' | 'critical'
  size?: 'small' | 'medium' | 'large'
}

/**
 * 现代化的过滤器卡片组件
 * 提供分组显示过滤条件的能力，支持折叠/展开功能
 */
export function FilterCard({
  title,
  icon,
  children,
  collapsible = false,
  defaultExpanded = true,
  tone = 'default',
  size = 'medium'
}: FilterCardProps) {
  const [expanded, setExpanded] = React.useState(defaultExpanded)

  const cardPadding = size === 'small' ? 3 : size === 'large' ? 5 : 4
  const headerPadding = size === 'small' ? 2 : size === 'large' ? 4 : 3

  return (
    <Card
      radius={3}
      shadow={1}
      tone={tone}
      style={{
        border: '1px solid var(--card-border-color)',
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        overflow: 'hidden',
        background: 'linear-gradient(135deg, var(--card-bg-color) 0%, var(--card-bg2-color, #fafbfc) 100%)',
        position: 'relative'
      }}
    >
      {/* 卡片头部 */}
      <Box
        padding={headerPadding}
        style={{
          backgroundColor: tone === 'default' 
            ? 'var(--card-bg-color)' 
            : `var(--card-${tone}-bg-color)`,
          borderBottom: expanded ? '1px solid var(--card-border-color)' : 'none',
          transition: 'all 0.2s ease-in-out'
        }}
      >
        <Flex align="center" justify="space-between">
          <Flex align="center" gap={2}>
            {icon && (
              <Box
                style={{
                  fontSize: size === 'small' ? '14px' : size === 'large' ? '18px' : '16px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: size === 'small' ? '24px' : size === 'large' ? '32px' : '28px',
                  height: size === 'small' ? '24px' : size === 'large' ? '32px' : '28px',
                  borderRadius: '6px',
                  backgroundColor: 'var(--card-accent-fg-color)',
                  color: 'var(--white)'
                }}
              >
                {icon}
              </Box>
            )}
            <Text
              weight="semibold"
              size={size === 'small' ? 1 : size === 'large' ? 3 : 2}
              style={{
                color: 'var(--card-fg-color)'
              }}
            >
              {title}
            </Text>
          </Flex>
          
          {collapsible && (
            <Button
              mode="bleed"
              tone="default"
              icon={() => (
                <Box
                  style={{
                    fontSize: '12px',
                    transform: expanded ? 'rotate(180deg)' : 'rotate(0deg)',
                    transition: 'transform 0.2s ease-in-out'
                  }}
                >
                  ▼
                </Box>
              )}
              onClick={() => setExpanded(!expanded)}
              padding={1}
            />
          )}
        </Flex>
      </Box>

      {/* 卡片内容 */}
      {expanded && (
        <Box
          padding={cardPadding}
          style={{
            backgroundColor: 'var(--card-bg-color)',
            animation: collapsible ? 'fadeIn 0.2s ease-in-out' : 'none'
          }}
        >
          {children}
        </Box>
      )}

      <style jsx global>{`
        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: translateY(-10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        @keyframes slideIn {
          from {
            opacity: 0;
            transform: translateX(-20px);
          }
          to {
            opacity: 1;
            transform: translateX(0);
          }
        }
        
        @keyframes pulse {
          0%, 100% {
            transform: scale(1);
          }
          50% {
            transform: scale(1.02);
          }
        }
        
        /* FilterCard卡片动画 */
        [data-ui="Card"]:hover {
          transform: translateY(-2px) !important;
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1) !important;
          border-color: var(--accent-fg-color) !important;
        }
        
        /* 按钮动画 */
        [data-ui="Button"]:hover {
          transform: scale(1.05) !important;
        }
        
        [data-ui="Button"]:active {
          transform: scale(0.98) !important;
        }
        
        /* 无障碍性支持 */
        @media (prefers-reduced-motion: reduce) {
          * {
            animation: none !important;
            transition: none !important;
          }
        }
      `}</style>
    </Card>
  )
}

/**
 * 预定义的过滤器卡片变体
 */
export const BasicFilterCard = (props: Omit<FilterCardProps, 'icon' | 'tone'>) => (
  <FilterCard icon="🔍" tone="default" {...props} />
)

export const CategoryFilterCard = (props: Omit<FilterCardProps, 'icon' | 'tone'>) => (
  <FilterCard icon="📂" tone="primary" {...props} />
)

export const PriceFilterCard = (props: Omit<FilterCardProps, 'icon' | 'tone'>) => (
  <FilterCard icon="💰" tone="positive" {...props} />
)

export const StatusFilterCard = (props: Omit<FilterCardProps, 'icon' | 'tone'>) => (
  <FilterCard icon="📊" tone="caution" {...props} />
)

/**
 * 紧凑型过滤器网格布局
 */
export interface FilterGridProps {
  children: React.ReactNode
  columns?: number[]
  gap?: number
}

export function FilterGrid({ 
  children, 
  columns = [1, 2, 3], 
  gap = 4 
}: FilterGridProps) {
  const gridId = React.useId()
  
  return (
    <>
      <Box
        style={{
          display: 'grid',
          gap: `${gap * 0.25}rem`,
          gridTemplateColumns: `repeat(${columns[0]}, 1fr)`
        }}
        className={`filter-grid-${gridId}`}
      >
        {children}
      </Box>
      
      {/* Responsive CSS */}
      <style jsx global>{`
        .filter-grid-${gridId} {
          display: grid;
          gap: ${gap * 0.25}rem;
        }
        
        /* Mobile */
        @media (max-width: 768px) {
          .filter-grid-${gridId} {
            grid-template-columns: repeat(${columns[0]}, 1fr);
          }
        }
        
        /* Tablet */
        @media (min-width: 769px) and (max-width: 1024px) {
          .filter-grid-${gridId} {
            grid-template-columns: repeat(${columns[1] || columns[0]}, 1fr);
          }
        }
        
        /* Desktop */
        @media (min-width: 1025px) {
          .filter-grid-${gridId} {
            grid-template-columns: repeat(${columns[2] || columns[1] || columns[0]}, 1fr);
          }
        }
        
        /* Large Desktop */
        @media (min-width: 1440px) {
          .filter-grid-${gridId} {
            grid-template-columns: repeat(${Math.min((columns[2] || columns[1] || columns[0]) + 1, 4)}, 1fr);
          }
        }
      `}</style>
    </>
  )
}