import React from 'react'
import {Card, Text, Box} from '@sanity/ui'

interface Props {
  children: React.ReactNode
  fallback?: React.ReactNode
}

interface State {
  hasError: boolean
  error?: Error
}

export class ImageErrorBoundary extends React.Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Image component error:', error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback
      }

      return (
        <Card padding={3} tone="caution">
          <Box>
            <Text size={1} weight="medium">图片组件错误</Text>
            <Text size={1} muted style={{marginTop: '4px'}}>
              {this.state.error?.message || '渲染图片时发生未知错误'}
            </Text>
            <button
              onClick={() => this.setState({ hasError: false, error: undefined })}
              style={{
                marginTop: '8px',
                padding: '4px 8px',
                fontSize: '12px',
                backgroundColor: '#f0f0f0',
                border: '1px solid #ccc',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              重试
            </button>
          </Box>
        </Card>
      )
    }

    return this.props.children
  }
}

export default ImageErrorBoundary