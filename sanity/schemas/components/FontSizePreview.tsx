import React from 'react'
import { Tooltip, Box, Text } from '@sanity/ui'

interface FontSizePreviewProps {
  size: string
  children: React.ReactNode
}

const fontSizeStyles = {
  small: { fontSize: '14px' },
  default: { fontSize: '16px' },
  medium: { fontSize: '20px' },
  large: { fontSize: '24px' },
  xlarge: { fontSize: '32px' },
  xxlarge: { fontSize: '40px' }
}

// Font size labels for tooltip display
const fontSizeLabels = {
  zh: {
    small: '小号 (14px)',
    default: '默认 (16px)',
    medium: '中号 (20px)',
    large: '大号 (24px)',
    xlarge: '超大号 (32px)',
    xxlarge: '特大号 (40px)'
  },
  en: {
    small: 'Small (14px)',
    default: 'Default (16px)',
    medium: 'Medium (20px)',
    large: 'Large (24px)',
    xlarge: 'Extra Large (32px)',
    xxlarge: 'XX Large (40px)'
  },
  ar: {
    small: 'صغير (14px)',
    default: 'افتراضي (16px)',
    medium: 'متوسط (20px)',
    large: 'كبير (24px)',
    xlarge: 'كبير جداً (32px)',
    xxlarge: 'كبير للغاية (40px)'
  }
}

// For frontend display (not used in editor)
export default function FontSizePreview({ size, children }: FontSizePreviewProps) {
  const sizeStyle = fontSizeStyles[size as keyof typeof fontSizeStyles] || fontSizeStyles.default
  
  return (
    <span 
      style={{
        fontSize: sizeStyle.fontSize,
        lineHeight: '1.5',
      }}
    >
      {children}
    </span>
  )
}

// For Sanity Studio editor annotation
export function FontSizeAnnotation(props: any) {
  const size = props.value?.size || 'default'
  const sizeStyle = fontSizeStyles[size as keyof typeof fontSizeStyles] || fontSizeStyles.default
  
  // 如果 renderDefault 不存在，说明这是旧版本的 Sanity 或者不同的调用方式
  // 我们需要兼容处理
  if (!props.renderDefault) {
    return <>{props.children}</>
  }
  
  // Detect current locale from Sanity Studio
  // Default to Chinese if locale detection fails
  let locale: 'zh' | 'en' | 'ar' = 'zh'
  if (typeof window !== 'undefined') {
    const studioLocale = (window as any).__studio?.locale || 'zh'
    if (['en', 'ar'].includes(studioLocale)) {
      locale = studioLocale as 'zh' | 'en' | 'ar'
    }
  }
  
  // Get the appropriate label for the current size and locale
  const sizeLabel = fontSizeLabels[locale][size as keyof typeof fontSizeStyles] || fontSizeLabels[locale].default
  
  // Use Sanity UI's Tooltip for custom hover content
  return (
    <Tooltip
      content={
        <Box padding={2}>
          <Text size={1} weight="medium">
            {sizeLabel}
          </Text>
        </Box>
      }
      placement="top"
      portal
    >
      <span 
        style={{
          // 使用更温和的视觉提示
          color: size !== 'default' ? '#1e40af' : 'inherit',
          backgroundColor: size !== 'default' ? 'rgba(219, 234, 254, 0.2)' : 'transparent',
          borderRadius: '2px',
          padding: '0 2px',
          // 不使用 position: relative 避免影响布局
          display: 'inline',
          // Add hover effect for better interactivity
          transition: 'background-color 0.2s ease',
          cursor: 'pointer',
        }}
        data-font-size={size}
        onMouseEnter={(e) => {
          if (size !== 'default') {
            e.currentTarget.style.backgroundColor = 'rgba(219, 234, 254, 0.4)'
          }
        }}
        onMouseLeave={(e) => {
          if (size !== 'default') {
            e.currentTarget.style.backgroundColor = 'rgba(219, 234, 254, 0.2)'
          }
        }}
      >
        {/* 关键修复 - 使用 renderDefault 来保持编辑器功能 */}
        {props.renderDefault(props)}
      </span>
    </Tooltip>
  )
}