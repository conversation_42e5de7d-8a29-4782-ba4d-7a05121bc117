import React, {useState, useEffect} from 'react'
import {Box, Card, Flex, Text, Skeleton} from '@sanity/ui'

interface ExternalImagePreviewProps {
  url: string
  alt?: string
  caption?: string
}

export const ExternalImagePreview: React.FC<ExternalImagePreviewProps> = ({
  url,
  alt = '外部图片',
  caption
}) => {
  const [imageLoaded, setImageLoaded] = useState(false)
  const [imageError, setImageError] = useState(false)
  const [imageDimensions, setImageDimensions] = useState<{width: number, height: number} | null>(null)

  useEffect(() => {
    if (!url) return

    // Reset states when URL changes
    setImageLoaded(false)
    setImageError(false)
    setImageDimensions(null)

    const img = new Image()
    
    // Add a timeout to handle hanging requests
    const timeout = setTimeout(() => {
      setImageError(true)
    }, 10000) // 10 second timeout

    img.onload = () => {
      clearTimeout(timeout)
      setImageLoaded(true)
      setImageDimensions({width: img.width, height: img.height})
    }
    
    img.onerror = () => {
      clearTimeout(timeout)
      setImageError(true)
    }
    
    // Add crossOrigin to handle CORS issues
    img.crossOrigin = 'anonymous'
    img.src = url

    // Cleanup function
    return () => {
      clearTimeout(timeout)
      img.onload = null
      img.onerror = null
    }
  }, [url])

  if (!url) {
    return (
      <Card padding={3} tone="caution">
        <Text size={1}>未提供图片URL</Text>
      </Card>
    )
  }

  if (imageError) {
    return (
      <Card padding={3} tone="critical">
        <Flex direction="column" gap={2}>
          <Text size={1} weight="medium">图片加载失败</Text>
          <Text size={1} muted>
            URL: {url}
          </Text>
          <Text size={1} muted>
            可能原因：网络问题、CORS限制或图片不存在
          </Text>
          <button 
            onClick={() => {
              setImageError(false)
              setImageLoaded(false)
              // Trigger re-render to retry loading
              const img = new Image()
              img.crossOrigin = 'anonymous'
              img.onload = () => {
                setImageLoaded(true)
                setImageDimensions({width: img.width, height: img.height})
              }
              img.onerror = () => setImageError(true)
              img.src = url
            }}
            style={{
              padding: '4px 8px',
              fontSize: '12px',
              backgroundColor: '#f0f0f0',
              border: '1px solid #ccc',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            重试加载
          </button>
        </Flex>
      </Card>
    )
  }

  return (
    <Card padding={2}>
      <Flex direction="column" gap={2}>
        {/* Image Preview */}
        <Box style={{position: 'relative', width: '100%', maxWidth: '300px'}}>
          {!imageLoaded && (
            <Skeleton style={{width: '100%', height: '200px'}} />
          )}
          {imageLoaded && (
            <img
              src={url}
              alt={alt}
              style={{
                width: '100%',
                height: 'auto',
                maxHeight: '200px',
                objectFit: 'cover',
                borderRadius: '4px',
                border: '1px solid var(--card-border-color)'
              }}
            />
          )}
        </Box>
        
        {/* Image Metadata */}
        {imageLoaded && (
          <Flex direction="column" gap={1}>
            <Text size={1} muted>
              来源: {new URL(url).hostname}
            </Text>
            {imageDimensions && (
              <Text size={1} muted>
                尺寸: {imageDimensions.width} × {imageDimensions.height}
              </Text>
            )}
            {caption && (
              <Text size={1} muted>
                说明: {caption}
              </Text>
            )}
          </Flex>
        )}
      </Flex>
    </Card>
  )
}

export default ExternalImagePreview