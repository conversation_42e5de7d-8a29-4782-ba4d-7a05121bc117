import React from 'react'
import {
  Box,
  Card,
  Flex,
  Text,
  Stack,
  Badge
} from '@sanity/ui'
import { STOCK_STATUS_OPTIONS } from '../../lib/productFilters'

export interface StockStatusSelectorProps {
  selectedStatus: string[]
  onChange: (selectedStatus: string[]) => void
  disabled?: boolean
  size?: 'small' | 'medium' | 'large'
  layout?: 'grid' | 'horizontal' | 'vertical'
}

/**
 * 现代化的库存状态选择器组件
 * 使用卡片式设计替换传统的复选框
 */
export function StockStatusSelector({
  selectedStatus = [],
  onChange,
  disabled = false,
  size = 'medium',
  layout = 'grid'
}: StockStatusSelectorProps) {
  const handleStatusToggle = (status: string) => {
    if (disabled) return
    
    const newStatus = selectedStatus.includes(status)
      ? selectedStatus.filter(s => s !== status)
      : [...selectedStatus, status]
    
    onChange(newStatus)
  }

  const cardSize = size === 'small' ? 3 : size === 'large' ? 5 : 4
  const textSize = size === 'small' ? 0 : size === 'large' ? 2 : 1
  const iconSize = size === 'small' ? '16px' : size === 'large' ? '24px' : '20px'

  const renderStatusCard = (option: typeof STOCK_STATUS_OPTIONS[0]) => {
    const isSelected = selectedStatus.includes(option.value)
    
    // 根据状态类型设置颜色
    const getStatusColors = (statusValue: string) => {
      switch (statusValue) {
        case 'in-stock':
          return {
            bgColor: isSelected ? '#E8F5E8' : '#F8F9FA',
            borderColor: isSelected ? '#4CAF50' : '#E0E0E0',
            textColor: isSelected ? '#2E7D32' : '#666',
            hoverBg: '#F1F8F1'
          }
        case 'pre-order':
          return {
            bgColor: isSelected ? '#FFF3E0' : '#F8F9FA',
            borderColor: isSelected ? '#FF9800' : '#E0E0E0',
            textColor: isSelected ? '#F57C00' : '#666',
            hoverBg: '#FFF8F0'
          }
        case 'sold-out':
          return {
            bgColor: isSelected ? '#FFEBEE' : '#F8F9FA',
            borderColor: isSelected ? '#F44336' : '#E0E0E0',
            textColor: isSelected ? '#C62828' : '#666',
            hoverBg: '#FFF5F5'
          }
        default:
          return {
            bgColor: isSelected ? '#E3F2FD' : '#F8F9FA',
            borderColor: isSelected ? '#2196F3' : '#E0E0E0',
            textColor: isSelected ? '#1565C0' : '#666',
            hoverBg: '#F3F9FF'
          }
      }
    }

    const colors = getStatusColors(option.value)

    return (
      <Card
        key={option.value}
        as="button"
        padding={cardSize}
        radius={3}
        style={{
          backgroundColor: colors.bgColor,
          border: `2px solid ${colors.borderColor}`,
          cursor: disabled ? 'not-allowed' : 'pointer',
          transition: 'all 0.2s ease-in-out',
          opacity: disabled ? 0.6 : 1,
          width: '100%',
          ':hover': disabled ? {} : {
            backgroundColor: colors.hoverBg,
            transform: 'translateY(-2px)',
            boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
          },
          ':active': disabled ? {} : {
            transform: 'translateY(0px)'
          }
        }}
        onClick={() => handleStatusToggle(option.value)}
      >
        <Flex direction="column" align="center" gap={2}>
          {/* 状态图标 */}
          <Box
            style={{
              fontSize: iconSize,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: size === 'small' ? '32px' : size === 'large' ? '48px' : '40px',
              height: size === 'small' ? '32px' : size === 'large' ? '48px' : '40px',
              borderRadius: '50%',
              backgroundColor: isSelected ? colors.borderColor : '#F0F0F0',
              color: isSelected ? 'white' : colors.textColor,
              transition: 'all 0.2s ease-in-out'
            }}
          >
            {option.icon}
          </Box>
          
          {/* 状态文本 */}
          <Text
            size={textSize}
            weight={isSelected ? 'semibold' : 'medium'}
            style={{
              color: colors.textColor,
              textAlign: 'center'
            }}
          >
            {option.title}
          </Text>
          
          {/* 选中指示器 */}
          {isSelected && (
            <Box
              style={{
                position: 'absolute',
                top: '8px',
                right: '8px',
                width: '16px',
                height: '16px',
                borderRadius: '50%',
                backgroundColor: colors.borderColor,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '10px',
                color: 'white'
              }}
            >
              ✓
            </Box>
          )}
        </Flex>
      </Card>
    )
  }

  const getLayoutStyles = () => {
    switch (layout) {
      case 'horizontal':
        return {
          display: 'flex',
          flexDirection: 'row' as const,
          gap: '12px',
          flexWrap: 'wrap' as const
        }
      case 'vertical':
        return {
          display: 'flex',
          flexDirection: 'column' as const,
          gap: '12px'
        }
      case 'grid':
      default:
        return {
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))',
          gap: '12px'
        }
    }
  }

  return (
    <Stack space={3}>
      {/* 选择器容器 */}
      <Box style={getLayoutStyles()}>
        {STOCK_STATUS_OPTIONS.map(renderStatusCard)}
      </Box>
      
      {/* 选中状态摘要 */}
      {selectedStatus.length > 0 && (
        <Box>
          <Flex wrap="wrap" gap={1}>
            <Text size={0} muted>已选择:</Text>
            {selectedStatus.map(status => {
              const option = STOCK_STATUS_OPTIONS.find(opt => opt.value === status)
              return option ? (
                <Badge
                  key={status}
                  tone="primary"
                  mode="outline"
                  fontSize={0}
                  style={{ borderRadius: '12px' }}
                >
                  <Flex align="center" gap={1}>
                    <span style={{ fontSize: '10px' }}>{option.icon}</span>
                    <span>{option.title}</span>
                  </Flex>
                </Badge>
              ) : null
            })}
          </Flex>
        </Box>
      )}
    </Stack>
  )
}

/**
 * 紧凑型库存状态选择器
 */
export function CompactStockStatusSelector(props: Omit<StockStatusSelectorProps, 'size' | 'layout'>) {
  return (
    <StockStatusSelector
      {...props}
      size="small"
      layout="horizontal"
    />
  )
}

/**
 * 垂直布局的库存状态选择器
 */
export function VerticalStockStatusSelector(props: Omit<StockStatusSelectorProps, 'layout'>) {
  return (
    <StockStatusSelector
      {...props}
      layout="vertical"
    />
  )
}