import React from 'react'
import {Badge, Flex, Box} from '@sanity/ui'
import {getStockStatusInfo, getCurrencyInfo, formatPrice} from '../../lib/productFilters'

export interface ProductStatusBadgeProps {
  isPublished?: boolean
  stockStatus?: string
  price?: number
  currency?: string
  size?: 'small' | 'medium' | 'large'
  showPrice?: boolean
}

/**
 * 产品状态徽章组件
 * 显示产品的上架状态、库存状态和价格信息
 */
export function ProductStatusBadge({
  isPublished,
  stockStatus,
  price,
  currency = 'CNY',
  size = 'small',
  showPrice = false
}: ProductStatusBadgeProps) {
  const publishStatusColor = isPublished ? 'positive' : 'caution'
  const publishStatusText = isPublished ? '已上架' : '草稿'
  const publishStatusIcon = isPublished ? '✅' : '📝'

  const stockInfo = stockStatus ? getStockStatusInfo(stockStatus) : null
  const stockStatusColor = stockStatus === 'in-stock' ? 'positive' : 
                          stockStatus === 'pre-order' ? 'caution' : 'critical'

  return (
    <Flex align="center" gap={2} wrap="wrap">
      {/* 上架状态徽章 */}
      <Badge 
        tone={publishStatusColor} 
        mode="outline"
        radius={3}
        fontSize={size === 'large' ? 1 : 0}
        padding={size === 'large' ? 2 : 1}
      >
        <Flex align="center" gap={1}>
          <span>{publishStatusIcon}</span>
          <span>{publishStatusText}</span>
        </Flex>
      </Badge>

      {/* 库存状态徽章 */}
      {stockInfo && (
        <Badge 
          tone={stockStatusColor} 
          mode="outline"
          radius={3}
          fontSize={size === 'large' ? 1 : 0}
          padding={size === 'large' ? 2 : 1}
        >
          <Flex align="center" gap={1}>
            <span>{stockInfo.icon}</span>
            <span>{stockInfo.title}</span>
          </Flex>
        </Badge>
      )}

      {/* 价格徽章 */}
      {showPrice && price !== undefined && price > 0 && (
        <Badge 
          tone="primary" 
          mode="outline"
          radius={3}
          fontSize={size === 'large' ? 1 : 0}
          padding={size === 'large' ? 2 : 1}
        >
          <Flex align="center" gap={1}>
            <span>💰</span>
            <span>{formatPrice(price, currency)}</span>
          </Flex>
        </Badge>
      )}
    </Flex>
  )
}

/**
 * 简化版状态徽章，只显示图标
 */
export function ProductStatusIcons({
  isPublished,
  stockStatus,
  size = 'small'
}: Pick<ProductStatusBadgeProps, 'isPublished' | 'stockStatus' | 'size'>) {
  const iconSize = size === 'large' ? '1.2em' : size === 'medium' ? '1em' : '0.9em'
  
  return (
    <Flex align="center" gap={1}>
      <Box style={{ fontSize: iconSize }}>
        {isPublished ? '✅' : '📝'}
      </Box>
      {stockStatus && (
        <Box style={{ fontSize: iconSize }}>
          {getStockStatusInfo(stockStatus).icon}
        </Box>
      )}
    </Flex>
  )
}

/**
 * 产品状态文本，用于预览
 */
export function getProductStatusText(isPublished?: boolean, stockStatus?: string): string {
  const publishText = isPublished ? '已上架' : '草稿'
  const stockInfo = stockStatus ? getStockStatusInfo(stockStatus) : null
  const stockText = stockInfo ? stockInfo.title : ''
  
  return [publishText, stockText].filter(Boolean).join(' • ')
}

/**
 * 产品状态图标，用于列表预览
 */
export function getProductStatusIcons(isPublished?: boolean, stockStatus?: string): string {
  const publishIcon = isPublished ? '✅' : '📝'
  const stockInfo = stockStatus ? getStockStatusInfo(stockStatus) : null
  const stockIcon = stockInfo ? stockInfo.icon : ''
  
  return [publishIcon, stockIcon].filter(Boolean).join(' ')
}