import React, {useState, useCallback, useRef} from 'react'
import {ArrayOfObjectsInputProps, ObjectItem, set, unset, insert} from 'sanity'
import {
  <PERSON><PERSON>,
  Card,
  Checkbox,
  Dialog,
  Flex,
  Grid,
  Heading,
  Stack,
  Text,
  useToast,
  Box,
  Badge
} from '@sanity/ui'
// Using simple text icons instead of @sanity/icons to avoid import issues
import styled from 'styled-components'

// Styled components for better UX
const GalleryContainer = styled(Card)`
  border: 2px dashed #e1e3e6;
  transition: border-color 0.2s ease;
  
  &.drag-over {
    border-color: #0070f3;
    background-color: #f0f8ff;
  }
`


const BatchActionBar = styled(Card)`
  position: sticky;
  top: 0;
  z-index: 20;
  border-bottom: 1px solid #e1e3e6;
`

interface BatchGalleryInputProps extends ArrayOfObjectsInputProps {
  // Additional props can be added here if needed
}

interface FlexibleImageItem {
  _key: string
  imageType?: 'upload' | 'external'
  uploadedImage?: any
  externalUrl?: string
  alt?: string
  caption?: string
}

export default function BatchGalleryInput(props: BatchGalleryInputProps) {
  const {value = [], onChange, renderDefault} = props
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set())
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [isDragOver, setIsDragOver] = useState(false)
  const toast = useToast()



  const handleItemSelect = useCallback((key: string, checked: boolean) => {
    setSelectedItems(prev => {
      const newSet = new Set(prev)
      if (checked) {
        newSet.add(key)
      } else {
        newSet.delete(key)
      }
      return newSet
    })
  }, [])

  const handleSelectAll = useCallback(() => {
    if (selectedItems.size === value.length) {
      setSelectedItems(new Set())
    } else {
      setSelectedItems(new Set(value.map((item: any) => item._key)))
    }
  }, [selectedItems.size, value])

  const handleBatchDelete = useCallback(() => {
    if (selectedItems.size === 0) return
    
    const newValue = value.filter((item: any) => !selectedItems.has(item._key))
    onChange(set(newValue))
    setSelectedItems(new Set())
    setShowDeleteDialog(false)
    
    toast.push({
      status: 'success',
      title: '批量删除成功',
      description: `已删除 ${selectedItems.size} 张图片`
    })
  }, [selectedItems, value, onChange, toast])

  const handleBatchAdd = useCallback(() => {
    // Add empty flexible image items that will be filled by user
    const itemsToAdd = 5 // Add 5 empty slots at once
    const newItems = Array.from({length: itemsToAdd}, (_, index) => ({
      _key: `new-${Date.now()}-${index}`,
      _type: 'flexibleImage',
      imageType: 'upload',
      // Add required fields to prevent validation errors
      uploadedImage: undefined,
      externalUrl: undefined,
      alt: '',
      caption: '',
      fallbackImage: undefined
    }))
    
    // Insert new items at the end of the array
    const newValue = [...value, ...newItems]
    onChange(set(newValue))
    
    toast.push({
      status: 'success',
      title: '批量添加成功',
      description: `已添加 ${itemsToAdd} 个空白图片槽位`
    })
  }, [value, onChange, toast])

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }, [])

  const handleDragLeave = useCallback(() => {
    setIsDragOver(false)
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
    
    // For now, just add empty slots when files are dropped
    // Users can then manually upload to each slot
    const files = e.dataTransfer.files
    if (files.length > 0) {
      handleBatchAdd()
    }
  }, [handleBatchAdd])

  return (
    <Stack space={4}>
      {/* Batch Action Bar */}
      <BatchActionBar padding={3} tone="default">
        <Flex align="center" justify="space-between">
          <Flex align="center" gap={3}>
            <Checkbox
              checked={selectedItems.size === value.length && value.length > 0}
              indeterminate={selectedItems.size > 0 && selectedItems.size < value.length}
              onChange={handleSelectAll}
            />
            <Text size={1} muted>
              {selectedItems.size > 0 
                ? `已选择 ${selectedItems.size} 项` 
                : `共 ${value.length} 项`}
            </Text>
            {selectedItems.size > 0 && (
              <Badge tone="primary" text={`${selectedItems.size}`} />
            )}
          </Flex>
          
          <Flex gap={2}>
            {/* Batch Add Button */}
            <Button
              mode="ghost"
              text="📁 批量添加"
              tone="primary"
              onClick={handleBatchAdd}
            />
            
            {/* Batch Delete Button */}
            <Button
              mode="ghost"
              text="🗑️ 批量删除"
              tone="critical"
              disabled={selectedItems.size === 0}
              onClick={() => setShowDeleteDialog(true)}
            />
          </Flex>
        </Flex>
      </BatchActionBar>


      {/* Drag & Drop Zone */}
      <GalleryContainer
        className={isDragOver ? 'drag-over' : ''}
        padding={4}
        tone="transparent"
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        {value.length === 0 ? (
          <Box padding={6} textAlign="center">
            <Stack space={3} align="center">
              <Text style={{fontSize: '2em', opacity: 0.5}}>📁</Text>
              <Text size={2} muted>
                点击"批量添加"按钮添加图片槽位，或拖拽到此处快速添加
              </Text>
            </Stack>
          </Box>
        ) : (
          <Box>
            {/* Selection overlay for batch operations */}
            <Grid columns={[2, 3, 4]} gap={2} style={{marginBottom: '1rem'}}>
              {value.map((item: FlexibleImageItem) => {
                const isSelected = selectedItems.has(item._key)
                
                return (
                  <Card
                    key={`selection-${item._key}`}
                    padding={2}
                    tone={isSelected ? 'primary' : 'transparent'}
                    style={{textAlign: 'center'}}
                  >
                    <Checkbox
                      checked={isSelected}
                      onChange={(checked) => handleItemSelect(item._key, checked)}
                    />
                    <Text size={1} muted style={{marginTop: '0.5rem'}}>
                      图片 #{value.indexOf(item) + 1}
                    </Text>
                  </Card>
                )
              })}
            </Grid>
            
            {/* Default Sanity array input */}
            <Box>
              {renderDefault(props)}
            </Box>
          </Box>
        )}
      </GalleryContainer>

      {/* Batch Delete Confirmation Dialog */}
      {showDeleteDialog && (
        <Dialog
          header="确认批量删除"
          id="batch-delete-dialog"
          onClose={() => setShowDeleteDialog(false)}
          footer={
            <Flex gap={2} justify="flex-end">
              <Button
                mode="ghost"
                text="取消"
                onClick={() => setShowDeleteDialog(false)}
              />
              <Button
                mode="default"
                tone="critical"
                text="🗑️ 确认删除"
                onClick={handleBatchDelete}
              />
            </Flex>
          }
        >
          <Stack space={4}>
            <Text>
              您确定要删除选中的 <strong>{selectedItems.size}</strong> 张图片吗？
            </Text>
            <Text size={1} muted>
              此操作无法撤销。
            </Text>
          </Stack>
        </Dialog>
      )}
    </Stack>
  )
}