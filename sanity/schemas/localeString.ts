import {defineType} from 'sanity'

export default defineType({
  name: 'localeString',
  title: '多语言文本',
  type: 'object',
  initialValue: {
    zh: '',
    en: '',
    ar: ''
  },
  fields: [
    {
      name: 'zh',
      title: '中文',
      type: 'string',
    },
    {
      name: 'en',
      title: 'English',
      type: 'string',
    },
    {
      name: 'ar',
      title: 'العربية',
      type: 'string',
    }
  ]
})