import {defineType} from 'sanity'
import {createBlockConfig, createInitialValue} from './helpers/blockContentConfig'

export default defineType({
  name: 'localeBlockContent',
  title: '多语言富文本',
  type: 'object',
  initialValue: {
    zh: createInitialValue('zh'),
    en: createInitialValue('en'),
    ar: createInitialValue('ar')
  },
  fields: [
    {
      name: 'zh',
      title: '中文',
      type: 'array',
      of: [createBlockConfig('zh')]
    },
    {
      name: 'en',
      title: 'English',
      type: 'array',
      of: [createBlockConfig('en')]
    },
    {
      name: 'ar',
      title: 'العربية',
      type: 'array',
      of: [createBlockConfig('ar')]
    }
  ]
})