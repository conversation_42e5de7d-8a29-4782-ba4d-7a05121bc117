import {defineType} from 'sanity'

export default defineType({
  name: 'homepageBrandStory',
  title: '品牌故事',
  type: 'document',
  fields: [
    {
      name: 'showSection',
      title: '显示品牌故事',
      type: 'boolean',
      initialValue: true,
      description: '控制是否显示品牌故事区域'
    },
    {
      name: 'title',
      title: '区域标题',
      type: 'localeString',
      validation: Rule => Rule.required(),
      hidden: ({document}) => !document?.showSection
    },
    {
      name: 'subtitle',
      title: '副标题',
      type: 'localeString',
      description: '可选的副标题，显示在主标题下方',
      hidden: ({document}) => !document?.showSection
    },
    {
      name: 'description',
      title: '品牌故事内容',
      type: 'localeBlockContent',
      validation: Rule => Rule.required(),
      description: '支持富文本格式（字体大小、加粗、斜体、列表、链接等）',
      hidden: ({document}) => !document?.showSection
    },
    {
      name: 'layout',
      title: '布局方式',
      type: 'string',
      options: {
        list: [
          {title: '居中布局', value: 'center'},
          {title: '左右布局', value: 'split'},
          {title: '全宽布局', value: 'full'},
          {title: '卡片布局', value: 'card'}
        ]
      },
      initialValue: 'center',
      description: '选择品牌故事的展示布局',
      hidden: ({document}) => !document?.showSection
    },
    {
      name: 'media',
      title: '媒体内容',
      type: 'object',
      description: '可选的图片或视频内容',
      hidden: ({document}) => !document?.showSection,
      fields: [
        {
          name: 'type',
          title: '媒体类型',
          type: 'string',
          options: {
            list: [
              {title: '无', value: 'none'},
              {title: '单张图片', value: 'image'},
              {title: '图片组', value: 'gallery'},
              {title: '视频', value: 'video'}
            ]
          },
          initialValue: 'none'
        },
        {
          name: 'image',
          title: '图片',
          type: 'flexibleImage',
          hidden: ({parent}) => parent?.type !== 'image'
        },
        {
          name: 'gallery',
          title: '图片组',
          type: 'array',
          of: [{type: 'flexibleImage'}],
          validation: Rule => Rule.max(6),
          description: '最多6张图片',
          hidden: ({parent}) => parent?.type !== 'gallery'
        },
        {
          name: 'videoUrl',
          title: '视频链接',
          type: 'string',
          description: '支持YouTube、Vimeo等视频链接',
          hidden: ({parent}) => parent?.type !== 'video'
        },
        {
          name: 'position',
          title: '媒体位置',
          type: 'string',
          options: {
            list: [
              {title: '内容上方', value: 'top'},
              {title: '内容下方', value: 'bottom'},
              {title: '内容左侧', value: 'left'},
              {title: '内容右侧', value: 'right'}
            ]
          },
          initialValue: 'right',
          hidden: ({parent}) => parent?.type === 'none'
        }
      ]
    },
    {
      name: 'backgroundImage',
      title: '背景图片',
      type: 'flexibleImage',
      description: '可选的背景图片',
      hidden: ({document}) => !document?.showSection
    },
    {
      name: 'backgroundStyle',
      title: '背景样式',
      type: 'object',
      hidden: ({document}) => !document?.showSection || !document?.backgroundImage,
      fields: [
        {
          name: 'opacity',
          title: '背景透明度',
          type: 'number',
          validation: Rule => Rule.min(0).max(1),
          initialValue: 0.1,
          description: '0-1之间，0为完全透明，1为完全不透明'
        },
        {
          name: 'blur',
          title: '模糊效果',
          type: 'boolean',
          initialValue: false,
          description: '是否对背景图片应用模糊效果'
        },
        {
          name: 'overlay',
          title: '叠加层颜色',
          type: 'string',
          options: {
            list: [
              {title: '无', value: 'none'},
              {title: '黑色', value: 'black'},
              {title: '白色', value: 'white'},
              {title: '蓝色', value: 'blue'},
              {title: '渐变', value: 'gradient'}
            ]
          },
          initialValue: 'none'
        }
      ]
    },
    {
      name: 'timeline',
      title: '时间线',
      type: 'array',
      of: [
        {
          type: 'object',
          title: '时间节点',
          fields: [
            {
              name: 'year',
              title: '年份',
              type: 'string',
              validation: Rule => Rule.required()
            },
            {
              name: 'title',
              title: '标题',
              type: 'localeString',
              validation: Rule => Rule.required()
            },
            {
              name: 'description',
              title: '描述',
              type: 'localeText',
              validation: Rule => Rule.required()
            },
            {
              name: 'image',
              title: '图片',
              type: 'flexibleImage',
              description: '可选的图片'
            }
          ],
          preview: {
            select: {
              year: 'year',
              title: 'title.zh',
              subtitle: 'description.zh'
            },
            prepare({year, title, subtitle}) {
              return {
                title: `${year} - ${title}`,
                subtitle: subtitle
              }
            }
          }
        }
      ],
      description: '可选：品牌发展时间线',
      hidden: ({document}) => !document?.showSection
    },
    {
      name: 'showProducts',
      title: '显示相关商品',
      type: 'boolean',
      initialValue: false,
      description: '是否在品牌故事区域展示相关商品',
      hidden: ({document}) => !document?.showSection
    },
    {
      name: 'products',
      title: '相关商品',
      type: 'array',
      of: [
        {
          type: 'reference',
          to: [{type: 'product'}],
          options: {
            filter: 'isPublished == true'
          }
        }
      ],
      validation: Rule => Rule.min(2).max(4),
      description: '选择2-4个代表品牌故事的商品',
      hidden: ({document}) => !document?.showProducts || !document?.showSection
    },
    {
      name: 'productSettings',
      title: '商品展示设置',
      type: 'object',
      fields: [
        {
          name: 'title',
          title: '商品区域标题',
          type: 'localeString',
          description: '如："品牌经典产品"'
        },
        {
          name: 'showPrices',
          title: '显示价格',
          type: 'boolean',
          initialValue: true
        },
        {
          name: 'showCategories',
          title: '显示分类标签',
          type: 'boolean',
          initialValue: true
        },
        {
          name: 'showBadges',
          title: '显示商品标识',
          type: 'boolean',
          initialValue: true,
          description: '显示"新品"、"热门"等标识'
        },
        {
          name: 'layout',
          title: '布局方式',
          type: 'string',
          options: {
            list: [
              {title: '网格布局', value: 'grid'},
              {title: '轮播展示', value: 'carousel'},
              {title: '横向列表', value: 'horizontal'}
            ]
          },
          initialValue: 'grid'
        }
      ],
      hidden: ({document}) => !document?.showProducts || !document?.showSection
    },
    {
      name: 'ctaButton',
      title: '行动按钮',
      type: 'object',
      description: '品牌故事底部的行动按钮',
      hidden: ({document}) => !document?.showSection,
      fields: [
        {
          name: 'show',
          title: '显示按钮',
          type: 'boolean',
          initialValue: false
        },
        {
          name: 'text',
          title: '按钮文字',
          type: 'localeString',
          hidden: ({parent}) => !parent?.show
        },
        {
          name: 'url',
          title: '链接地址',
          type: 'string',
          hidden: ({parent}) => !parent?.show
        }
      ]
    }
  ],
  preview: {
    select: {
      title: 'title.zh',
      showSection: 'showSection',
      hasTimeline: 'timeline',
      hasProducts: 'showProducts'
    },
    prepare({title, showSection, hasTimeline, hasProducts}) {
      const features = []
      if (hasTimeline?.length) features.push(`时间线(${hasTimeline.length})`)
      if (hasProducts) features.push('相关产品')
      
      return {
        title: title || '品牌故事',
        subtitle: showSection 
          ? features.length ? `已启用 - ${features.join(', ')}` : '已启用'
          : '已禁用'
      }
    }
  }
})