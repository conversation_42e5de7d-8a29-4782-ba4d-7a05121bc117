import {defineType} from 'sanity'

export default defineType({
  name: 'homepageFeaturedProducts',
  title: '精选产品',
  type: 'document',
  fields: [
    {
      name: 'showSection',
      title: '显示精选产品',
      type: 'boolean',
      initialValue: true,
      description: '控制是否在首页显示精选产品区域'
    },
    {
      name: 'sectionTitle',
      title: '模块标题',
      type: 'localeString',
      validation: Rule => Rule.required(),
      description: '精选产品区域的标题',
      hidden: ({document}) => !document?.showSection
    },
    {
      name: 'sectionSubtitle',
      title: '模块副标题',
      type: 'localeText',
      description: '精选产品区域的描述文字',
      hidden: ({document}) => !document?.showSection
    },
    {
      name: 'products',
      title: '精选产品列表',
      type: 'array',
      of: [
        {
          type: 'reference',
          to: [{type: 'product'}],
          options: {
            filter: 'isPublished == true'
          }
        }
      ],
      validation: Rule => Rule.min(3).max(8),
      description: '选择3-8个精选产品进行展示',
      hidden: ({document}) => !document?.showSection
    },
    {
      name: 'displaySettings',
      title: '显示设置',
      type: 'object',
      hidden: ({document}) => !document?.showSection,
      fields: [
        {
          name: 'showPrices',
          title: '显示价格',
          type: 'boolean',
          initialValue: true
        },
        {
          name: 'showRatings',
          title: '显示评分',
          type: 'boolean',
          initialValue: true
        },
        {
          name: 'showCategories',
          title: '显示分类标签',
          type: 'boolean',
          initialValue: true
        },
        {
          name: 'showBadges',
          title: '显示商品标识',
          type: 'boolean',
          initialValue: true,
          description: '显示"新品"、"热门"等标识'
        },
        {
          name: 'showDescription',
          title: '显示商品描述',
          type: 'boolean',
          initialValue: false,
          description: '显示商品的简短描述'
        }
      ]
    },
    {
      name: 'layoutSettings',
      title: '布局设置',
      type: 'object',
      hidden: ({document}) => !document?.showSection,
      fields: [
        {
          name: 'displayMode',
          title: '展示模式',
          type: 'string',
          options: {
            list: [
              {title: '网格布局', value: 'grid'},
              {title: '轮播展示', value: 'carousel'},
              {title: '列表展示', value: 'list'},
              {title: '特色展示', value: 'featured'}
            ]
          },
          initialValue: 'grid',
          description: '选择产品的展示方式'
        },
        {
          name: 'columnsDesktop',
          title: '桌面端列数',
          type: 'number',
          options: {
            list: [
              {title: '2列', value: 2},
              {title: '3列', value: 3},
              {title: '4列', value: 4}
            ]
          },
          initialValue: 4,
          hidden: ({parent}) => parent?.displayMode !== 'grid'
        },
        {
          name: 'columnsTablet',
          title: '平板端列数',
          type: 'number',
          options: {
            list: [
              {title: '1列', value: 1},
              {title: '2列', value: 2},
              {title: '3列', value: 3}
            ]
          },
          initialValue: 2,
          hidden: ({parent}) => parent?.displayMode !== 'grid'
        },
        {
          name: 'columnsMobile',
          title: '手机端列数',
          type: 'number',
          options: {
            list: [
              {title: '1列', value: 1},
              {title: '2列', value: 2}
            ]
          },
          initialValue: 1,
          hidden: ({parent}) => parent?.displayMode !== 'grid'
        },
        {
          name: 'autoplay',
          title: '自动播放',
          type: 'boolean',
          initialValue: true,
          description: '轮播模式下是否自动播放',
          hidden: ({parent}) => parent?.displayMode !== 'carousel'
        },
        {
          name: 'autoplayInterval',
          title: '自动播放间隔（秒）',
          type: 'number',
          initialValue: 5,
          validation: Rule => Rule.min(3).max(10),
          hidden: ({parent}) => parent?.displayMode !== 'carousel' || !parent?.autoplay
        }
      ]
    },
    {
      name: 'ctaButton',
      title: '查看更多按钮',
      type: 'object',
      description: '在精选产品底部显示的按钮',
      hidden: ({document}) => !document?.showSection,
      fields: [
        {
          name: 'show',
          title: '显示按钮',
          type: 'boolean',
          initialValue: true
        },
        {
          name: 'text',
          title: '按钮文字',
          type: 'localeString',
          hidden: ({parent}) => !parent?.show
        },
        {
          name: 'url',
          title: '链接地址',
          type: 'string',
          description: '通常链接到产品列表页',
          hidden: ({parent}) => !parent?.show
        }
      ]
    }
  ],
  preview: {
    select: {
      productCount: 'products',
      title: 'sectionTitle.zh',
      showSection: 'showSection'
    },
    prepare({productCount, title, showSection}) {
      const count = productCount ? productCount.length : 0
      return {
        title: title || '精选产品',
        subtitle: showSection ? `已启用 - ${count} 个产品` : '已禁用'
      }
    }
  }
})