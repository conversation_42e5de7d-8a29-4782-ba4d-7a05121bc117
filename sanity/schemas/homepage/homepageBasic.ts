import {defineType} from 'sanity'

export default defineType({
  name: 'homepageBasic',
  title: '基础设置',
  type: 'document',
  fields: [
    {
      name: 'title',
      title: '页面标题',
      type: 'localeString',
      validation: Rule => Rule.required(),
      description: '页面的主标题，用于SEO和浏览器标签'
    },
    {
      name: 'heroSection',
      title: '首页头部区域',
      type: 'object',
      fields: [
        {
          name: 'titleDisplayMode',
          title: '标题显示模式',
          type: 'string',
          options: {
            list: [
              {title: '文本渐变', value: 'text'},
              {title: '图片Logo', value: 'image'}
            ],
            layout: 'radio'
          },
          initialValue: 'text',
          description: '选择首页标题的显示方式'
        },
        {
          name: 'mainTitle',
          title: '品牌名称',
          type: 'localeString',
          validation: Rule => Rule.custom((value, context) => {
            const parent = context.parent as any
            if (parent?.titleDisplayMode === 'text' && !value?.zh && !value?.en) {
              return '选择文本模式时必须填写品牌名称'
            }
            return true
          }),
          description: '网站的主要品牌名称（文本模式时显示）',
          hidden: ({parent}) => parent?.titleDisplayMode === 'image'
        },
        {
          name: 'titleImage',
          title: '标题图片',
          type: 'flexibleImage',
          validation: Rule => Rule.custom((value, context) => {
            const parent = context.parent as any
            if (parent?.titleDisplayMode === 'image' && !value) {
              return '选择图片模式时必须上传图片'
            }
            return true
          }),
          description: '用于显示的品牌Logo或标题图片',
          hidden: ({parent}) => parent?.titleDisplayMode !== 'image'
        },
        {
          name: 'titleGradient',
          title: '文本渐变配置',
          type: 'object',
          description: '配置标题文本的渐变效果',
          hidden: ({parent}) => parent?.titleDisplayMode !== 'text',
          fields: [
            {
              name: 'usePreset',
              title: '使用预设',
              type: 'boolean',
              initialValue: true,
              description: '使用预设渐变或自定义颜色'
            },
            {
              name: 'preset',
              title: '预设渐变',
              type: 'string',
              options: {
                list: [
                  {title: '蓝紫渐变', value: 'blue-purple'},
                  {title: '橙红渐变', value: 'orange-red'},
                  {title: '绿青渐变', value: 'green-cyan'},
                  {title: '粉紫渐变', value: 'pink-purple'},
                  {title: '彩虹渐变', value: 'rainbow'}
                ]
              },
              initialValue: 'blue-purple',
              hidden: ({parent}) => !parent?.usePreset
            },
            {
              name: 'fromColor',
              title: '起始颜色',
              type: 'color',
              description: '渐变的起始颜色',
              hidden: ({parent}) => parent?.usePreset !== false
            },
            {
              name: 'viaColor',
              title: '中间颜色（可选）',
              type: 'color',
              description: '渐变的中间颜色，留空则为两色渐变',
              hidden: ({parent}) => parent?.usePreset !== false
            },
            {
              name: 'toColor',
              title: '结束颜色',
              type: 'color',
              description: '渐变的结束颜色',
              hidden: ({parent}) => parent?.usePreset !== false
            },
            {
              name: 'direction',
              title: '渐变方向',
              type: 'string',
              options: {
                list: [
                  {title: '从左到右', value: 'to right'},
                  {title: '从右到左', value: 'to left'},
                  {title: '从上到下', value: 'to bottom'},
                  {title: '从下到上', value: 'to top'},
                  {title: '左上到右下', value: 'to bottom right'},
                  {title: '右上到左下', value: 'to bottom left'}
                ]
              },
              initialValue: 'to right'
            }
          ]
        },
        {
          name: 'subtitle',
          title: '副标题',
          type: 'localeString',
          validation: Rule => Rule.required(),
          description: '品牌的简短描述或口号'
        },
        {
          name: 'description',
          title: '详细描述',
          type: 'localeBlockContent',
          validation: Rule => Rule.required(),
          description: '品牌的详细介绍，支持富文本格式（字体大小、加粗、斜体、列表等）'
        },
        {
          name: 'ctaButton',
          title: '行动按钮',
          type: 'object',
          fields: [
            {
              name: 'text',
              title: '按钮文字',
              type: 'localeString',
              validation: Rule => Rule.required()
            },
            {
              name: 'url',
              title: '链接地址',
              type: 'string',
              validation: Rule => Rule.required()
            }
          ]
        }
      ]
    }
  ],
  preview: {
    select: {
      title: 'title.zh',
      subtitle: 'heroSection.subtitle.zh'
    },
    prepare({title, subtitle}) {
      return {
        title: title || '基础设置',
        subtitle: subtitle || '首页基础配置'
      }
    }
  }
})