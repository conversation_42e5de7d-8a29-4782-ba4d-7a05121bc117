import {defineType} from 'sanity'

export default defineType({
  name: 'homepageSeo',
  title: 'SEO设置',
  type: 'document',
  fields: [
    {
      name: 'metaTitle',
      title: 'Meta标题',
      type: 'localeString',
      validation: Rule => Rule.required(),
      description: '搜索引擎结果中显示的标题（建议50-60个字符）'
    },
    {
      name: 'metaDescription',
      title: 'Meta描述',
      type: 'localeText',
      validation: Rule => Rule.required(),
      description: '搜索引擎结果中显示的描述（建议150-160个字符）'
    },
    {
      name: 'keywords',
      title: '关键词',
      type: 'localeString',
      description: '用逗号分隔的关键词列表'
    },
    {
      name: 'ogTitle',
      title: 'Open Graph标题',
      type: 'localeString',
      description: '社交媒体分享时显示的标题（留空则使用Meta标题）'
    },
    {
      name: 'ogDescription',
      title: 'Open Graph描述',
      type: 'localeText',
      description: '社交媒体分享时显示的描述（留空则使用Meta描述）'
    },
    {
      name: 'ogImage',
      title: 'Open Graph图片',
      type: 'flexibleImage',
      description: '社交媒体分享时显示的图片（建议1200x630像素）'
    },
    {
      name: 'twitterCard',
      title: 'Twitter卡片类型',
      type: 'string',
      options: {
        list: [
          {title: '摘要', value: 'summary'},
          {title: '大图摘要', value: 'summary_large_image'},
          {title: '应用', value: 'app'},
          {title: '播放器', value: 'player'}
        ]
      },
      initialValue: 'summary_large_image'
    },
    {
      name: 'canonicalUrl',
      title: '规范链接',
      type: 'string',
      description: '页面的规范URL，用于避免重复内容问题'
    },
    {
      name: 'robots',
      title: '搜索引擎爬虫设置',
      type: 'object',
      fields: [
        {
          name: 'index',
          title: '允许索引',
          type: 'boolean',
          initialValue: true,
          description: '是否允许搜索引擎索引此页面'
        },
        {
          name: 'follow',
          title: '跟踪链接',
          type: 'boolean',
          initialValue: true,
          description: '是否允许搜索引擎跟踪页面中的链接'
        },
        {
          name: 'archive',
          title: '允许存档',
          type: 'boolean',
          initialValue: true,
          description: '是否允许搜索引擎缓存页面'
        },
        {
          name: 'snippet',
          title: '显示摘要',
          type: 'boolean',
          initialValue: true,
          description: '是否允许在搜索结果中显示文本摘要'
        },
        {
          name: 'imageindex',
          title: '索引图片',
          type: 'boolean',
          initialValue: true,
          description: '是否允许搜索引擎索引页面中的图片'
        }
      ]
    },
    {
      name: 'structuredData',
      title: '结构化数据',
      type: 'object',
      description: 'Schema.org结构化数据配置',
      fields: [
        {
          name: 'type',
          title: '页面类型',
          type: 'string',
          options: {
            list: [
              {title: '网站', value: 'WebSite'},
              {title: '组织', value: 'Organization'},
              {title: '商店', value: 'Store'},
              {title: '品牌', value: 'Brand'}
            ]
          },
          initialValue: 'WebSite'
        },
        {
          name: 'organizationName',
          title: '组织名称',
          type: 'string'
        },
        {
          name: 'logo',
          title: '组织Logo',
          type: 'flexibleImage',
          description: '用于结构化数据的Logo图片'
        },
        {
          name: 'sameAs',
          title: '社交媒体链接',
          type: 'array',
          of: [{type: 'string'}],
          description: '组织的社交媒体主页链接'
        },
        {
          name: 'contactPoint',
          title: '联系方式',
          type: 'object',
          fields: [
            {
              name: 'telephone',
              title: '电话',
              type: 'string'
            },
            {
              name: 'contactType',
              title: '联系类型',
              type: 'string',
              options: {
                list: [
                  {title: '客户服务', value: 'customer service'},
                  {title: '技术支持', value: 'technical support'},
                  {title: '销售', value: 'sales'}
                ]
              }
            },
            {
              name: 'areaServed',
              title: '服务地区',
              type: 'string'
            },
            {
              name: 'availableLanguage',
              title: '支持语言',
              type: 'array',
              of: [{type: 'string'}],
              options: {
                list: [
                  {title: '中文', value: 'zh'},
                  {title: '英文', value: 'en'},
                  {title: '阿拉伯语', value: 'ar'}
                ]
              }
            }
          ]
        }
      ]
    },
    {
      name: 'alternateLanguages',
      title: '多语言设置',
      type: 'array',
      of: [
        {
          type: 'object',
          fields: [
            {
              name: 'language',
              title: '语言',
              type: 'string',
              options: {
                list: [
                  {title: '中文', value: 'zh'},
                  {title: '英文', value: 'en'},
                  {title: '阿拉伯语', value: 'ar'}
                ]
              }
            },
            {
              name: 'url',
              title: 'URL',
              type: 'string'
            }
          ]
        }
      ],
      description: '配置多语言版本的URL'
    },
    {
      name: 'additionalMetaTags',
      title: '附加Meta标签',
      type: 'array',
      of: [
        {
          type: 'object',
          fields: [
            {
              name: 'name',
              title: '标签名称',
              type: 'string'
            },
            {
              name: 'content',
              title: '标签内容',
              type: 'string'
            }
          ]
        }
      ],
      description: '添加自定义的meta标签'
    },
    {
      name: 'jsonLd',
      title: '自定义JSON-LD',
      type: 'text',
      description: '直接输入JSON-LD格式的结构化数据（高级功能）'
    }
  ],
  preview: {
    select: {
      title: 'metaTitle.zh',
      description: 'metaDescription.zh',
      ogImage: 'ogImage'
    },
    prepare({title, description, ogImage}) {
      return {
        title: title || 'SEO设置',
        subtitle: description || '首页搜索引擎优化配置',
        media: ogImage
      }
    }
  }
})