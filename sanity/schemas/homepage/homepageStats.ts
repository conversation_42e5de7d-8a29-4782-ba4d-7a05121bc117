import {defineType} from 'sanity'

export default defineType({
  name: 'homepageStats',
  title: '数据展示',
  type: 'document',
  fields: [
    {
      name: 'showStats',
      title: '显示统计数据',
      type: 'boolean',
      initialValue: true,
      description: '控制是否显示数据统计区域'
    },
    {
      name: 'sectionTitle',
      title: '区域标题',
      type: 'localeString',
      description: '数据展示区域的标题',
      hidden: ({document}) => !document?.showStats
    },
    {
      name: 'sectionSubtitle',
      title: '区域副标题',
      type: 'localeText',
      description: '可选的副标题描述',
      hidden: ({document}) => !document?.showStats
    },
    {
      name: 'stats',
      title: '统计数据',
      type: 'array',
      of: [
        {
          type: 'object',
          title: '数据项',
          fields: [
            {
              name: 'number',
              title: '数字',
              type: 'string',
              validation: Rule => Rule.required(),
              description: '显示的数字，如 "1000+"、"99%"、"50K"'
            },
            {
              name: 'label',
              title: '标签',
              type: 'localeString',
              validation: Rule => Rule.required(),
              description: '数字的说明文字'
            },
            {
              name: 'description',
              title: '详细描述',
              type: 'localeText',
              description: '可选的详细描述文字'
            },
            {
              name: 'icon',
              title: '图标',
              type: 'string',
              description: 'Emoji图标或图标代码'
            },
            {
              name: 'color',
              title: '颜色主题',
              type: 'string',
              options: {
                list: [
                  {title: '默认', value: 'default'},
                  {title: '蓝色', value: 'blue'},
                  {title: '绿色', value: 'green'},
                  {title: '红色', value: 'red'},
                  {title: '紫色', value: 'purple'},
                  {title: '橙色', value: 'orange'},
                  {title: '粉色', value: 'pink'},
                  {title: '黄色', value: 'yellow'}
                ]
              },
              initialValue: 'default'
            },
            {
              name: 'animated',
              title: '数字动画',
              type: 'boolean',
              initialValue: true,
              description: '是否启用数字递增动画效果'
            },
            {
              name: 'prefix',
              title: '前缀',
              type: 'string',
              description: '数字前的符号，如 "$"、"¥"'
            },
            {
              name: 'suffix',
              title: '后缀',
              type: 'string',
              description: '数字后的符号，如 "%"、"+"'
            },
            {
              name: 'order',
              title: '排序',
              type: 'number',
              validation: Rule => Rule.min(1).max(20),
              description: '数字越小排序越靠前'
            }
          ],
          preview: {
            select: {
              number: 'number',
              label: 'label.zh',
              icon: 'icon',
              color: 'color',
              order: 'order'
            },
            prepare({number, label, icon, color, order}) {
              return {
                title: `${icon ? icon + ' ' : ''}${number}`,
                subtitle: `${label}${order ? ` (排序: ${order})` : ''}`
              }
            }
          }
        }
      ],
      validation: Rule => Rule.min(1).max(12),
      description: '添加1-12个统计数据项',
      hidden: ({document}) => !document?.showStats
    },
    {
      name: 'layout',
      title: '布局方式',
      type: 'string',
      options: {
        list: [
          {title: '水平排列', value: 'horizontal'},
          {title: '网格布局 (2列)', value: 'grid-2'},
          {title: '网格布局 (3列)', value: 'grid-3'},
          {title: '网格布局 (4列)', value: 'grid-4'},
          {title: '卡片布局', value: 'cards'},
          {title: '大数字布局', value: 'big-numbers'}
        ]
      },
      initialValue: 'horizontal',
      description: '选择数据的展示布局',
      hidden: ({document}) => !document?.showStats
    },
    {
      name: 'style',
      title: '样式设置',
      type: 'object',
      hidden: ({document}) => !document?.showStats,
      fields: [
        {
          name: 'background',
          title: '背景样式',
          type: 'string',
          options: {
            list: [
              {title: '透明', value: 'transparent'},
              {title: '浅色背景', value: 'light'},
              {title: '深色背景', value: 'dark'},
              {title: '渐变背景', value: 'gradient'},
              {title: '图案背景', value: 'pattern'}
            ]
          },
          initialValue: 'transparent'
        },
        {
          name: 'showDividers',
          title: '显示分隔线',
          type: 'boolean',
          initialValue: false,
          description: '在数据项之间显示分隔线'
        },
        {
          name: 'alignment',
          title: '对齐方式',
          type: 'string',
          options: {
            list: [
              {title: '左对齐', value: 'left'},
              {title: '居中', value: 'center'},
              {title: '右对齐', value: 'right'}
            ]
          },
          initialValue: 'center'
        },
        {
          name: 'numberSize',
          title: '数字大小',
          type: 'string',
          options: {
            list: [
              {title: '小', value: 'small'},
              {title: '中', value: 'medium'},
              {title: '大', value: 'large'},
              {title: '特大', value: 'xl'}
            ]
          },
          initialValue: 'large'
        }
      ]
    },
    {
      name: 'backgroundImage',
      title: '背景图片',
      type: 'flexibleImage',
      description: '可选的背景图片',
      hidden: ({document}) => !document?.showStats
    },
    {
      name: 'comparison',
      title: '对比数据',
      type: 'object',
      description: '可选：显示与上期的对比',
      hidden: ({document}) => !document?.showStats,
      fields: [
        {
          name: 'show',
          title: '显示对比',
          type: 'boolean',
          initialValue: false
        },
        {
          name: 'period',
          title: '对比周期',
          type: 'localeString',
          description: '如："相比去年"、"环比上月"',
          hidden: ({parent}) => !parent?.show
        },
        {
          name: 'data',
          title: '对比数据',
          type: 'array',
          of: [
            {
              type: 'object',
              fields: [
                {
                  name: 'statId',
                  title: '关联的统计项',
                  type: 'string',
                  description: '输入上面统计数据的标签'
                },
                {
                  name: 'change',
                  title: '变化值',
                  type: 'string',
                  description: '如："+25%"、"-10"'
                },
                {
                  name: 'trend',
                  title: '趋势',
                  type: 'string',
                  options: {
                    list: [
                      {title: '上升', value: 'up'},
                      {title: '下降', value: 'down'},
                      {title: '持平', value: 'flat'}
                    ]
                  }
                }
              ]
            }
          ],
          hidden: ({parent}) => !parent?.show
        }
      ]
    },
    {
      name: 'ctaButton',
      title: '查看详情按钮',
      type: 'object',
      description: '在统计数据下方的按钮',
      hidden: ({document}) => !document?.showStats,
      fields: [
        {
          name: 'show',
          title: '显示按钮',
          type: 'boolean',
          initialValue: false
        },
        {
          name: 'text',
          title: '按钮文字',
          type: 'localeString',
          hidden: ({parent}) => !parent?.show
        },
        {
          name: 'url',
          title: '链接地址',
          type: 'string',
          description: '如链接到详细报告页面',
          hidden: ({parent}) => !parent?.show
        }
      ]
    }
  ],
  preview: {
    select: {
      title: 'sectionTitle.zh',
      statsCount: 'stats',
      showStats: 'showStats',
      hasComparison: 'comparison.show'
    },
    prepare({title, statsCount, showStats, hasComparison}) {
      const count = statsCount ? statsCount.length : 0
      const features = []
      if (count > 0) features.push(`${count} 个数据`)
      if (hasComparison) features.push('含对比')
      
      return {
        title: title || '数据展示',
        subtitle: showStats 
          ? features.length ? features.join(' - ') : '已启用'
          : '已禁用'
      }
    }
  }
})