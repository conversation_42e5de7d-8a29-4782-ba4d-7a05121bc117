import {defineType} from 'sanity'

export default defineType({
  name: 'homepageFeatures',
  title: '特色功能',
  type: 'document',
  fields: [
    {
      name: 'showSection',
      title: '显示特色功能区域',
      type: 'boolean',
      initialValue: true,
      description: '控制是否在首页显示特色功能区域'
    },
    {
      name: 'sectionTitle',
      title: '区域标题',
      type: 'localeString',
      description: '特色功能区域的主标题',
      hidden: ({document}) => !document?.showSection
    },
    {
      name: 'sectionDescription',
      title: '区域描述',
      type: 'localeText',
      description: '可选的区域描述文字，显示在标题下方',
      hidden: ({document}) => !document?.showSection
    },
    {
      name: 'layout',
      title: '布局样式',
      type: 'string',
      options: {
        list: [
          {title: '网格布局 (3列)', value: 'grid-3'},
          {title: '网格布局 (4列)', value: 'grid-4'},
          {title: '网格布局 (2列)', value: 'grid-2'},
          {title: '水平排列', value: 'horizontal'},
          {title: '垂直堆叠', value: 'vertical'},
          {title: '卡片布局', value: 'cards'}
        ]
      },
      initialValue: 'grid-3',
      description: '选择功能卡片的布局方式',
      hidden: ({document}) => !document?.showSection
    },
    {
      name: 'features',
      title: '特色功能列表',
      type: 'array',
      of: [
        {
          type: 'object',
          title: '功能项',
          fields: [
            {
              name: 'icon',
              title: '图标',
              type: 'string',
              description: '图标的emoji或图标代码（如：🚀 或 rocket）',
              validation: Rule => Rule.required()
            },
            {
              name: 'iconColor',
              title: '图标颜色',
              type: 'string',
              options: {
                list: [
                  {title: '默认', value: 'default'},
                  {title: '蓝色', value: 'blue'},
                  {title: '绿色', value: 'green'},
                  {title: '红色', value: 'red'},
                  {title: '紫色', value: 'purple'},
                  {title: '橙色', value: 'orange'},
                  {title: '粉色', value: 'pink'}
                ]
              },
              initialValue: 'default'
            },
            {
              name: 'title',
              title: '功能标题',
              type: 'localeString',
              validation: Rule => Rule.required()
            },
            {
              name: 'description',
              title: '功能描述',
              type: 'localeBlockContent',
              validation: Rule => Rule.required(),
              description: '支持富文本格式'
            },
            {
              name: 'linkUrl',
              title: '链接地址',
              type: 'string',
              description: '可选：点击卡片跳转的链接地址'
            },
            {
              name: 'isExternal',
              title: '外部链接',
              type: 'boolean',
              initialValue: false,
              description: '是否为外部链接（将在新窗口打开）',
              hidden: ({parent}) => !parent?.linkUrl
            },
            {
              name: 'badge',
              title: '标签',
              type: 'object',
              description: '可选的标签，如"新功能"、"热门"等',
              fields: [
                {
                  name: 'text',
                  title: '标签文字',
                  type: 'localeString'
                },
                {
                  name: 'color',
                  title: '标签颜色',
                  type: 'string',
                  options: {
                    list: [
                      {title: '蓝色', value: 'blue'},
                      {title: '绿色', value: 'green'},
                      {title: '红色', value: 'red'},
                      {title: '橙色', value: 'orange'},
                      {title: '紫色', value: 'purple'}
                    ]
                  },
                  initialValue: 'blue'
                }
              ]
            },
            {
              name: 'order',
              title: '排序',
              type: 'number',
              description: '数字越小排序越靠前',
              validation: Rule => Rule.min(1).max(100)
            }
          ],
          preview: {
            select: {
              title: 'title.zh',
              subtitle: 'description.zh',
              icon: 'icon',
              order: 'order',
              badge: 'badge.text.zh'
            },
            prepare({title, subtitle, icon, order, badge}) {
              return {
                title: `${icon} ${title}${order ? ` (${order})` : ''}`,
                subtitle: badge ? `[${badge}] ${subtitle}` : subtitle
              }
            }
          }
        }
      ],
      validation: Rule => Rule.min(1).max(8),
      description: '添加1-8个特色功能，建议3-4个最佳',
      hidden: ({document}) => !document?.showSection
    },
    {
      name: 'style',
      title: '样式设置',
      type: 'object',
      hidden: ({document}) => !document?.showSection,
      fields: [
        {
          name: 'cardStyle',
          title: '卡片样式',
          type: 'string',
          options: {
            list: [
              {title: '默认卡片', value: 'default'},
              {title: '带边框', value: 'bordered'},
              {title: '带阴影', value: 'shadow'},
              {title: '渐变背景', value: 'gradient'},
              {title: '玻璃拟态', value: 'glassmorphism'}
            ]
          },
          initialValue: 'default'
        },
        {
          name: 'iconStyle',
          title: '图标样式',
          type: 'string',
          options: {
            list: [
              {title: '默认', value: 'default'},
              {title: '圆形背景', value: 'circle'},
              {title: '方形背景', value: 'square'},
              {title: '圆角方形', value: 'rounded'}
            ]
          },
          initialValue: 'default'
        },
        {
          name: 'animation',
          title: '动画效果',
          type: 'boolean',
          initialValue: true,
          description: '是否启用悬停动画效果'
        }
      ]
    }
  ],
  preview: {
    select: {
      title: 'sectionTitle.zh',
      featureCount: 'features',
      showSection: 'showSection'
    },
    prepare({title, featureCount, showSection}) {
      const count = featureCount ? featureCount.length : 0
      return {
        title: title || '特色功能',
        subtitle: showSection ? `已启用 - ${count} 个功能` : '已禁用'
      }
    }
  }
})