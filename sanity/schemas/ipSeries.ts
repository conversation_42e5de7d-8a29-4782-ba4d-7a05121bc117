import {defineType, defineField} from 'sanity'

export default defineType({
  name: 'ipSeries',
  title: 'IP 系列',
  type: 'document',
  fields: [
    defineField({
      name: 'name',
      title: '系列名称',
      type: 'localeString',
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'slug',
      title: 'URL 标识',
      type: 'slug',
      options: {
        source: 'name.zh',
        maxLength: 96,
      },
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'description',
      title: '系列描述',
      type: 'localeBlockContent',
      description: '支持富文本格式（字体大小、加粗、斜体、列表等）'
    }),
    defineField({
      name: 'logo',
      title: '系列 Logo',
      type: 'flexibleImage'
    }),
    defineField({
      name: 'bannerImage',
      title: '横幅图片',
      type: 'flexibleImage'
    }),
    defineField({
      name: 'isActive',
      title: '是否启用',
      type: 'boolean',
      initialValue: true
    })
  ],
  preview: {
    select: {
      title: 'name.zh',
      logo: 'logo',
      isActive: 'isActive'
    },
    prepare({title, logo, isActive}) {
      // Handle flexibleImage type for media preview
      let media = undefined
      
      if (logo) {
        // Handle flexible image format
        if (logo.imageType === 'upload' && logo.uploadedImage) {
          media = logo.uploadedImage
        }
        else if (logo.imageType === 'external' && logo.externalUrl) {
          // For external images, create a simple preview
          try {
            media = {
              url: logo.externalUrl,
              alt: logo.alt || '外部图片'
            }
          } catch (error) {
            media = undefined
          }
        }
      }
      
      return {
        title: title || '未命名系列',
        subtitle: isActive ? '✅ 启用' : '❌ 停用',
        media
      }
    }
  }
})