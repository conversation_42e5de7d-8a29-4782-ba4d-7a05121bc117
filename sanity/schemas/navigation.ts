import {defineType} from 'sanity'

export default defineType({
  name: 'navigation',
  title: '导航管理',
  type: 'document',
  fields: [
    {
      name: 'title',
      title: '导航标题',
      type: 'localeString',
      validation: Rule => Rule.required(),
      description: '导航配置的标识名称'
    },
    {
      name: 'identifier',
      title: '导航标识符',
      type: 'string',
      validation: Rule => Rule.required(),
      description: '用于代码中识别导航的唯一标识符（如: main, footer, mobile）'
    },
    {
      name: 'menuItems',
      title: '菜单项',
      type: 'array',
      of: [
        {
          type: 'object',
          name: 'menuItem',
          fields: [
            {
              name: 'title',
              title: '菜单标题',
              type: 'localeString',
              validation: Rule => Rule.required()
            },
            {
              name: 'slug',
              title: '链接地址',
              type: 'string',
              validation: Rule => Rule.required(),
              description: '相对路径，如 "/about" 或 "/products"'
            },
            {
              name: 'external',
              title: '外部链接',
              type: 'boolean',
              initialValue: false,
              description: '是否为外部链接'
            },
            {
              name: 'openInNewTab',
              title: '新标签页打开',
              type: 'boolean',
              initialValue: false,
              description: '是否在新标签页打开链接'
            },
            {
              name: 'icon',
              title: '图标',
              type: 'string',
              description: '可选的图标代码或emoji'
            },
            {
              name: 'order',
              title: '排序',
              type: 'number',
              description: '菜单项的显示顺序'
            },
            {
              name: 'isActive',
              title: '启用状态',
              type: 'boolean',
              initialValue: true,
              description: '是否显示此菜单项'
            },
            {
              name: 'submenu',
              title: '子菜单',
              type: 'array',
              of: [
                {
                  type: 'object',
                  fields: [
                    {
                      name: 'title',
                      title: '子菜单标题',
                      type: 'localeString',
                      validation: Rule => Rule.required()
                    },
                    {
                      name: 'slug',
                      title: '链接地址',
                      type: 'string',
                      validation: Rule => Rule.required()
                    },
                    {
                      name: 'external',
                      title: '外部链接',
                      type: 'boolean',
                      initialValue: false
                    },
                    {
                      name: 'openInNewTab',
                      title: '新标签页打开',
                      type: 'boolean',
                      initialValue: false
                    },
                    {
                      name: 'description',
                      title: '描述',
                      type: 'localeText',
                      description: '子菜单的简短描述'
                    },
                    {
                      name: 'icon',
                      title: '图标',
                      type: 'string'
                    },
                    {
                      name: 'isActive',
                      title: '启用状态',
                      type: 'boolean',
                      initialValue: true
                    }
                  ],
                  preview: {
                    select: {
                      title: 'title.zh',
                      subtitle: 'slug'
                    }
                  }
                }
              ]
            }
          ],
          preview: {
            select: {
              title: 'title.zh',
              subtitle: 'slug',
              order: 'order'
            },
            prepare({title, subtitle, order}) {
              return {
                title: `${order ? `${order}. ` : ''}${title}`,
                subtitle: subtitle
              }
            }
          }
        }
      ]
    },
    {
      name: 'settings',
      title: '导航设置',
      type: 'object',
      fields: [
        {
          name: 'sticky',
          title: '固定导航',
          type: 'boolean',
          initialValue: false,
          description: '是否固定在页面顶部'
        },
        {
          name: 'showSearch',
          title: '显示搜索框',
          type: 'boolean',
          initialValue: true,
          description: '是否在导航中显示搜索功能'
        },
        {
          name: 'showLanguageSwitch',
          title: '显示语言切换',
          type: 'boolean',
          initialValue: true,
          description: '是否显示语言切换按钮'
        },
        {
          name: 'showCart',
          title: '显示购物车',
          type: 'boolean',
          initialValue: true,
          description: '是否显示购物车图标'
        },
        {
          name: 'backgroundColor',
          title: '背景颜色',
          type: 'string',
          description: '导航栏的背景颜色（如：#FFFFFF）',
          initialValue: '#FFFFFF',
          validation: Rule => Rule.regex(/^#[0-9A-Fa-f]{6}$/).error('请输入有效的十六进制颜色代码，如 #FFFFFF')
        },
        {
          name: 'textColor',
          title: '文字颜色',
          type: 'string',
          description: '导航栏的文字颜色（如：#333333）',
          initialValue: '#333333',
          validation: Rule => Rule.regex(/^#[0-9A-Fa-f]{6}$/).error('请输入有效的十六进制颜色代码，如 #333333')
        },
        {
          name: 'hoverColor',
          title: '悬停颜色',
          type: 'string',
          description: '菜单项悬停时的颜色（如：#FF6B6B）',
          initialValue: '#FF6B6B',
          validation: Rule => Rule.regex(/^#[0-9A-Fa-f]{6}$/).error('请输入有效的十六进制颜色代码，如 #FF6B6B')
        }
      ]
    }
  ],
  preview: {
    select: {
      title: 'title.zh',
      subtitle: 'identifier'
    }
  }
})