import FontSizePreview, { FontSizeAnnotation } from '../components/FontSizePreview'
import {TextIcon} from '@sanity/icons'

type Locale = 'zh' | 'en' | 'ar'

// 多语言标签配置
const labels = {
  zh: {
    // 样式
    normal: '正文',
    h1: '标题1',
    h2: '标题2', 
    h3: '标题3',
    blockquote: '引用',
    // 列表
    bullet: '项目符号',
    number: '数字编号',
    // 装饰器
    strong: '粗体',
    em: '斜体',
    underline: '下划线',
    // 注释
    link: '链接',
    fontSize: '字号',
    // 字号选项
    fontSizes: {
      small: '小号 (14px)',
      default: '默认 (16px)',
      medium: '中号 (20px)',
      large: '大号 (24px)',
      xlarge: '超大号 (32px)',
      xxlarge: '特大号 (40px)'
    }
  },
  en: {
    // 样式
    normal: 'Normal',
    h1: 'H1',
    h2: 'H2',
    h3: 'H3',
    blockquote: 'Quote',
    // 列表
    bullet: 'Bullet',
    number: 'Number',
    // 装饰器
    strong: 'Strong',
    em: 'Emphasis',
    underline: 'Underline',
    // 注释
    link: 'Link',
    fontSize: 'Font Size',
    // 字号选项
    fontSizes: {
      small: 'Small (14px)',
      default: 'Default (16px)',
      medium: 'Medium (20px)',
      large: 'Large (24px)',
      xlarge: 'Extra Large (32px)',
      xxlarge: 'XX Large (40px)'
    }
  },
  ar: {
    // 样式
    normal: 'عادي',
    h1: 'عنوان 1',
    h2: 'عنوان 2',
    h3: 'عنوان 3',
    blockquote: 'اقتباس',
    // 列表
    bullet: 'تعداد نقطي',
    number: 'تعداد رقمي',
    // 装饰器
    strong: 'عريض',
    em: 'مائل',
    underline: 'تسطير',
    // 注释
    link: 'رابط',
    fontSize: 'حجم الخط',
    // 字号选项
    fontSizes: {
      small: 'صغير (14px)',
      default: 'افتراضي (16px)',
      medium: 'متوسط (20px)',
      large: 'كبير (24px)',
      xlarge: 'كبير جداً (32px)',
      xxlarge: 'كبير للغاية (40px)'
    }
  }
}

/**
 * 创建富文本块配置
 * @param locale - 语言代码
 * @returns 完整的块配置对象
 */
export function createBlockConfig(locale: Locale) {
  const l = labels[locale]
  
  return {
    type: 'block',
    styles: [
      {title: l.normal, value: 'normal'},
      {title: l.h1, value: 'h1'},
      {title: l.h2, value: 'h2'},
      {title: l.h3, value: 'h3'},
      {title: l.blockquote, value: 'blockquote'},
    ],
    lists: [
      {title: l.bullet, value: 'bullet'},
      {title: l.number, value: 'number'},
    ],
    marks: {
      decorators: [
        {title: l.strong, value: 'strong'},
        {title: l.em, value: 'em'},
        {title: l.underline, value: 'underline'},
      ],
      annotations: [
        {
          title: l.link,
          name: 'link',
          type: 'object',
          fields: [
            {name: 'href', title: 'URL', type: 'url'},
          ]
        },
        {
          title: l.fontSize,
          name: 'fontSize',
          type: 'object',
          icon: TextIcon,
          fields: [
            {
              name: 'size',
              title: locale === 'zh' ? '字体大小' : locale === 'en' ? 'Text Size' : 'حجم النص',
              type: 'string',
              options: {
                list: [
                  {title: l.fontSizes.small, value: 'small'},
                  {title: l.fontSizes.default, value: 'default'},
                  {title: l.fontSizes.medium, value: 'medium'},
                  {title: l.fontSizes.large, value: 'large'},
                  {title: l.fontSizes.xlarge, value: 'xlarge'},
                  {title: l.fontSizes.xxlarge, value: 'xxlarge'}
                ]
              },
              validation: Rule => Rule.required(),
              initialValue: 'default'
            }
          ],
          components: {
            annotation: FontSizeAnnotation
          },
          options: {
            modal: {
              type: 'popover',
              width: 'medium'
            }
          }
        }
      ]
    }
  }
}

/**
 * 创建默认初始值
 * @param locale - 语言代码
 * @returns 初始值对象
 */
export function createInitialValue(locale: Locale) {
  return [
    {
      _type: 'block',
      _key: `default-${locale}`,
      style: 'normal',
      children: [
        {
          _type: 'span',
          text: '',
          marks: []
        }
      ]
    }
  ]
}