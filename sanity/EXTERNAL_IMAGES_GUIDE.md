# External Images Configuration Guide

## Overview

This guide explains how to use external images from Unsplash and other allowed sources in your Sanity Studio.

## Supported Domains

The following domains are currently whitelisted for external images:
- `images.unsplash.com`
- `unsplash.com`
- `source.unsplash.com`

## How to Use

### 1. In Sanity Studio

1. Navigate to any document with image fields (Products, Categories, Pages, etc.)
2. For any image field, you'll see a "图片类型" (Image Type) selector
3. Choose "外部链接" (External URL) instead of "上传到 Sanity" (Upload to Sanity)
4. Paste your Unsplash image URL in the "外部图片链接" field
5. Fill in the alt text and optional caption
6. Optionally add a fallback image

### 2. Example Unsplash URLs

Here are some example URLs you can use for testing:

#### High-quality anime/manga related images:
- `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=600&fit=crop`
- `https://images.unsplash.com/photo-1606041008023-472dfb5e530f?w=800&h=600&fit=crop`
- `https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=1200&h=800&fit=crop`

#### General product/lifestyle images:
- `https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800&h=600&fit=crop`
- `https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=800&h=600&fit=crop`
- `https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=800&h=600&fit=crop`

#### For hero/banner images:
- `https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=1600&h=900&fit=crop`
- `https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=1600&h=900&fit=crop`

### 3. URL Parameters

Unsplash supports various URL parameters for image optimization:
- `w=800` - Width in pixels
- `h=600` - Height in pixels
- `fit=crop` - Crop to fit dimensions
- `q=80` - Quality (1-100)
- `fm=jpg` - Format (jpg, png, webp)
- `auto=format` - Auto format selection

Example optimized URL:
```
https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=600&fit=crop&q=80&auto=format
```

## Features

### 1. Flexible Image Type Selection
- Choose between native Sanity uploads or external URLs
- Seamless switching between types
- Validation for external URLs

### 2. Domain Validation
- Only allows whitelisted domains
- Prevents potentially harmful URLs
- Clear error messages for invalid domains

### 3. Preview Integration
- Real-time preview of external images
- Image metadata display (dimensions, source)
- Error handling for broken URLs

### 4. Fallback Support
- Optional fallback images for external URLs
- Graceful degradation if external image fails

## Best Practices

### 1. Image Optimization
- Use Unsplash's built-in optimization parameters
- Specify appropriate dimensions for your use case
- Consider auto-format for better performance

### 2. Alt Text
- Always provide descriptive alt text
- Consider accessibility and SEO
- Use descriptive, not generic text

### 3. Fallback Images
- Provide fallback images for critical images
- Use similar-sized fallback images
- Test fallback behavior

### 4. Testing
- Test images in different contexts
- Verify images load correctly
- Check mobile and desktop display

## Frontend Integration

### Next.js Configuration

Ensure your `next.config.js` includes the external domains:

```javascript
module.exports = {
  images: {
    domains: [
      'images.unsplash.com',
      'unsplash.com',
      'source.unsplash.com'
    ]
  }
}
```

### Image Utility Function

Create a utility function to handle both native and external images:

```typescript
export function getImageUrl(image: FlexibleImage): string | null {
  if (!image) return null
  
  if (image.imageType === 'external' && image.externalUrl) {
    return image.externalUrl
  }
  
  if (image.imageType === 'upload' && image.uploadedImage) {
    return urlFor(image.uploadedImage).url()
  }
  
  return null
}
```

## Troubleshooting

### Common Issues

1. **Image not loading**: Check if the URL is from an allowed domain
2. **Validation errors**: Ensure the URL is properly formatted
3. **Preview not showing**: The image might be loading slowly or the URL might be invalid
4. **CORS errors**: This shouldn't happen with Unsplash, but check browser console

### Debug Steps

1. Test the image URL directly in browser
2. Check Sanity Studio console for errors
3. Verify the URL matches allowed domain patterns
4. Test with a simple Unsplash URL first

## Migration Guide

If you have existing image fields, they will continue to work. To migrate:

1. Existing native images will still work
2. New flexible image fields will default to "upload" type
3. No data migration needed - schemas are backward compatible

## Schema Updates

The following schemas have been updated to support external images:
- `product` - gallery (mainImage is now gallery[0])
- `category` - image
- `ipSeries` - logo, bannerImage
- `homepage` - backgroundImage
- `aboutPage` - heroImage, section images, team photos
- `contactPage` - social media icons
- `seo` - ogImage

## Support

For additional support or questions:
1. Check this guide first
2. Review Sanity documentation
3. Check Unsplash API documentation
4. Contact the development team