import createNextIntlPlugin from 'next-intl/plugin'
import withBundleAnalyzer from '@next/bundle-analyzer'

const withNextIntl = createNextIntlPlugin('./src/app/i18n.ts')

const bundleAnalyzer = withBundleAnalyzer({
  enabled: process.env.ANALYZE === 'true',
})

// Platform detection
const isVercel = process.env.VERCEL === '1'
const isNetlify = process.env.NETLIFY === 'true'
const isDevelopment = process.env.NODE_ENV === 'development'

console.log(`🚀 Detected platform: ${isVercel ? 'Vercel' : isNetlify ? 'Netlify' : 'Unknown/Local'}`)

/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'cdn.sanity.io',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        pathname: '/**',
      },
    ],
    formats: ['image/avif', 'image/webp'],
    // Platform-specific image optimization
    ...(isNetlify && {
      // Netlify specific optimizations
      minimumCacheTTL: 86400,
      dangerouslyAllowSVG: true,
      contentSecurityPolicy: "default-src 'self' data:; img-src 'self' data: https: blob:; style-src 'self' 'unsafe-inline'; font-src 'self' data:;",
    }),
    ...(isVercel && {
      // Vercel specific optimizations  
      minimumCacheTTL: 31536000,
    }),
  },
  
  // Production build optimizations
  productionBrowserSourceMaps: false,
  
  // Platform-specific output settings
  ...(isNetlify && {
    // Netlify doesn't need standalone mode with @netlify/plugin-nextjs
    trailingSlash: false,
    // Ensure static files are properly generated
    generateBuildId: async () => {
      // Use a stable build ID for better caching
      return `netlify-${Date.now()}`
    },
  }),
  
  // Experimental features for better performance
  experimental: {
    optimizeCss: true,
    gzipSize: true,
    ...(isVercel && {
      // Vercel-specific experimental features
      serverMinification: true,
      serverSourceMaps: false,
    }),
    ...(isNetlify && {
      // Netlify-specific experimental features
      esmExternals: true,
    }),
  },

  // Custom webpack configuration to avoid bundling Node-only modules into the browser build
  webpack: (config, {isServer, webpack, dev}) => {
    // Platform-specific webpack optimizations
    if (!dev) {
      if (isVercel) {
        // Vercel-specific optimizations
        config.optimization.splitChunks = {
          ...config.optimization.splitChunks,
          cacheGroups: {
            ...config.optimization.splitChunks.cacheGroups,
            vercel: {
              name: 'vercel-chunks',
              chunks: 'all',
              enforce: true,
            },
          },
        }
      }
      
      if (isNetlify) {
        // Netlify-specific optimizations
        config.optimization.usedExports = true
        config.optimization.sideEffects = false
      }
    }
    if (!isServer) {
      // Prevent Node-only modules from being bundled on the client
      config.resolve.alias = {
        ...config.resolve.alias,
        'node:assert': false,
        'node:util': false,
        'node:stream': false,
        'node:buffer': false,
        'node:crypto': false,
        'node:http': false,
        'node:https': false,
        'node:net': false,
        'node:tls': false,
        'node:url': false,
        'node:zlib': false,
        assert: false,
        util: false,
        stream: false,
        buffer: false,
        crypto: false,
        http: false,
        https: false,
        net: false,
        tls: false,
        url: false,
        zlib: false,
      }
      
      // Add fallbacks for browser
      config.resolve.fallback = {
        ...config.resolve.fallback,
        crypto: false,
        stream: false,
        util: false,
        buffer: false,
        assert: false,
        http: false,
        https: false,
        net: false,
        tls: false,
        url: false,
        zlib: false,
      }
      
      // Exclude undici from client bundle
      config.externals = config.externals || []
      config.externals.push('undici')
      
      // Handle node: protocol imports
      config.plugins.push(
        new webpack.NormalModuleReplacementPlugin(/^node:/, (resource) => {
          const mod = resource.request.replace(/^node:/, '')
          switch (mod) {
            case 'assert':
              resource.request = require.resolve('assert')
              break
            case 'buffer':
              resource.request = require.resolve('buffer')
              break
            case 'stream':
              resource.request = require.resolve('stream-browserify')
              break
            case 'util':
              resource.request = require.resolve('util')
              break
            case 'crypto':
              resource.request = require.resolve('crypto-browserify')
              break
            case 'http':
              resource.request = require.resolve('stream-http')
              break
            case 'https':
              resource.request = require.resolve('https-browserify')
              break
            case 'url':
              resource.request = require.resolve('url')
              break
            case 'zlib':
              resource.request = require.resolve('browserify-zlib')
              break
            default:
              // For other node: imports, just ignore them
              resource.request = 'data:text/javascript,module.exports = {}'
          }
        })
      )
    }
    
    return config
  },
}

export default bundleAnalyzer(withNextIntl(nextConfig));