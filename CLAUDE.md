# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

MyNgaPop is a cross-border anime merchandise brand website built with Next.js 15.3.5 App Router and Sanity CMS. It features multilingual support (Chinese, English, Arabic), static generation with ISR (Incremental Static Regeneration), and global CDN distribution.

## Essential Commands

### Development
```bash
# Start Next.js development server
npm run dev

# Start Sanity Studio (separate terminal)
cd sanity && npm run dev
# or: cd sanity && pnpm dev

# Install dependencies in both directories
npm install && cd sanity && npm install && cd ..
```

### Quality Checks
```bash
# TypeScript type checking
npm run type-check

# ESLint code quality checks
npm run lint

# Build production version
npm run build

# Preview production build
npm run start

# Bundle analysis
npm run analyze

# Run tests
npm test

# Check build size
npm run bundle-size

# Combined build and size check
npm run build:check
```

### Testing
```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm test -- SearchBox.test.tsx
```

### Content Management Scripts
```bash
# Promote content from staging to production
npm run promote

# Dry run of content promotion (preview changes)
npm run promote:dry-run

# Force promote content (bypass confirmations)
npm run promote:force

# Verify dataset integrity
npm run verify-datasets
```

### Sanity CMS Development
```bash
# Start Sanity Studio
cd sanity && npm run dev

# Build Sanity Studio
cd sanity && npm run build

# Deploy Sanity Studio
cd sanity && npm run deploy

# Run image migration script (if needed)
cd sanity && npm run migrate:main-image
```

## Architecture Overview

### Tech Stack
- **Frontend**: Next.js 15.3.5 with App Router
- **Styling**: Tailwind CSS 3.4+ with custom animations
- **CMS**: Sanity v3 with custom studio configuration
- **Animations**: Framer Motion 11.11+
- **Internationalization**: next-intl 3.22+
- **Deployment**: Vercel (implied from configuration)

### Key Directory Structure
```
src/
├── app/
│   ├── [locale]/          # Internationalized routes
│   │   ├── layout.tsx     # Locale-specific layout
│   │   ├── page.tsx       # Homepage
│   │   └── products/      # Product pages with filters
│   ├── api/               # API routes including search and revalidation
│   ├── globals.css        # Global styles
│   └── i18n.ts           # Internationalization config
├── components/
│   ├── ui/               # Base UI components (SearchBox, OptimizedImage, etc.)
│   ├── product/          # Product-specific components (Filter, Card, Skeleton)
│   ├── layout/           # Layout components (Header, Footer, Navigation)
│   └── common/           # Shared components (ErrorBoundary, DatasetIndicator)
├── lib/
│   └── sanity/           # Sanity client, queries, and utilities
├── messages/             # Translation files (ar.json, en.json, zh.json)
└── types/                # TypeScript definitions
```

### Sanity CMS Integration
- **Dual dataset support**: Production and staging environments
- **Content schemas**: Products, categories, IP series, pages (homepage, about, contact)
- **Multilingual content**: All content fields support zh/en/ar languages
- **Image optimization**: Configured for cdn.sanity.io with WebP/AVIF formats
- **Comprehensive GROQ queries**: Located in `src/lib/sanity/queries.ts`

### Key Components Architecture
- **Product filtering**: Dedicated filter page at `/[locale]/products/filter`
- **Search functionality**: API route with multilingual search support
- **ISR revalidation**: Webhook-triggered page regeneration
- **Image optimization**: Custom OptimizedImage component with skeleton loading

### Image Management System
- **FlexibleImage Architecture**: Supports both uploaded Sanity images and external URLs
- **Gallery-based**: Products use `gallery[]` array where first image serves as main image
- **Image Resolution**: `resolveImageUrl()` and `resolveImageAlt()` utilities in `src/lib/sanity/image.ts`
- **Performance**: Custom image loader with CDN optimization and WebP/AVIF format support

## Development Patterns

### Internationalization
- Uses `next-intl` with locale prefix strategy
- Supported locales: `['zh', 'en', 'ar']` (Chinese default)
- Translation files in `src/messages/` directory
- Middleware handles locale routing automatically

### Content Management Workflow
1. **Staging environment**: Edit content in Sanity Studio staging dataset
2. **Content promotion**: Use `npm run promote` scripts to sync to production
3. **ISR updates**: Webhook automatically revalidates affected pages
4. **Multi-dataset indicator**: Visual indicator shows current dataset in development

### Type Safety
- Full TypeScript coverage with strict mode enabled
- Sanity schema types defined in `src/types/sanity.ts`
- Path mapping configured: `@/*` maps to `src/*`
- Jest testing with React Testing Library setup
- Test files located in `__tests__` directories alongside components

### Performance Optimizations
- Image domains configured for Sanity CDN and Unsplash
- Custom webpack config excludes Node.js modules from client bundle
- Production optimizations: disabled source maps, CDN usage disabled for consistency

## Sanity Studio Management

### Access Points
- **Local development**: http://localhost:3333
- **Dataset switching**: URL parameter `?dataset=staging` or `?dataset=production`
- **Environment-based**: `SANITY_STUDIO_USE_STAGING=true` for staging mode

### Content Structure
- **Singleton documents**: Homepage, about page, contact page, site settings
- **Collection documents**: Products, categories, IP series, navigation
- **Multilingual fields**: All content supports Chinese, English, and Arabic
- **Asset management**: Images with hotspot/crop functionality

### Studio Features
- **Custom desk structure**: Organized by content type with clear sections
- **Dataset indicator**: Visual warning when using staging environment
- **Restricted actions**: Singleton documents cannot be duplicated or deleted
- **Vision tool**: GROQ query testing and debugging

## Common Development Tasks

### Adding New Product Fields
1. Update Sanity schema in `sanity/schemas/product.ts`
2. Redeploy Sanity Studio
3. Update TypeScript types in `src/types/sanity.ts`
4. Modify GROQ queries in `src/lib/sanity/queries.ts`
5. Update components to display new fields

### Adding New Language
1. Add locale to `src/i18n/routing.ts` locales array
2. Create new message file in `src/messages/[locale].json`
3. Update Sanity schemas to include new language field
4. Test locale routing and content display

### Working with Search
- Search API endpoint: `src/app/api/search/route.ts`
- Supports multilingual search across products and categories
- Returns both products and categories with relevance scoring
- Client component: `src/components/ui/SearchBox.tsx`

### Working with Images
- **FlexibleImage Type**: Use `FlexibleImage` type for all image fields
- **Gallery Management**: Products use `gallery[]` array, access main image via `gallery[0]`
- **Image Resolution**: Always use `resolveImageUrl()` utility for consistent URL generation
- **Alt Text**: Use `resolveImageAlt()` for accessibility-compliant alt text
- **Migration**: `mainImage` field has been migrated to `gallery[0]` - avoid using `mainImage` references

### GROQ Query Patterns
- **Reusable Fragments**: Use `flexibleImageQuery` fragment for consistent image querying
- **Multilingual Fields**: Access localized content via `field.zh`, `field.en`, `field.ar`
- **Performance**: Always include only necessary fields in queries to minimize payload

## Environment Configuration

### Required Variables
```bash
# Sanity configuration
NEXT_PUBLIC_SANITY_PROJECT_ID=4za4x22i
NEXT_PUBLIC_SANITY_DATASET=production
SANITY_API_TOKEN=your-read-token

# Optional staging support
NEXT_PUBLIC_USE_STAGING_DATASET=true
NEXT_PUBLIC_SANITY_DATASET_STAGING=staging
SANITY_STUDIO_USE_STAGING=true
SANITY_STUDIO_DATASET_STAGING=staging
```

## Troubleshooting

### Common Issues
- **Node.js module errors**: Webpack config excludes Node modules from client bundle
- **Sanity connection issues**: Check proxy configuration in server environment
- **Image loading failures**: Verify Sanity project ID and image domain configuration
- **Type errors**: Run `npm run type-check` to identify issues
- **Internationalization problems**: Verify message files exist for all locales

### Build Debugging
- Use `npm run build` to test production build locally
- Check for TypeScript errors with `npm run type-check`
- Verify all environment variables are set correctly
- Test Sanity connectivity and dataset access

This codebase emphasizes multilingual content management, performance optimization, and maintainable architecture with clear separation between content management (Sanity) and presentation (Next.js).

## Production Deployment

### Environment Configuration

#### Vercel Deployment (Recommended)
```bash
# .env.production (automatically set by Vercel)
NEXT_PUBLIC_SANITY_PROJECT_ID=4za4x22i
NEXT_PUBLIC_SANITY_DATASET=production
SANITY_API_TOKEN=your-production-token
NEXT_PUBLIC_BASE_URL=https://your-app.vercel.app
```

#### Netlify Deployment
```bash
# .env.production
NEXT_PUBLIC_SANITY_PROJECT_ID=4za4x22i
NEXT_PUBLIC_SANITY_DATASET=production
SANITY_API_TOKEN=your-production-token
NEXT_PUBLIC_BASE_URL=https://your-domain.netlify.app
```

### Deployment Checklist

#### Pre-deployment
- [ ] Copy `.env.production.example` to `.env.production`
- [ ] Update production environment variables
- [ ] Test build process locally with `npm run build`
- [ ] Verify Sanity CMS connectivity

#### Post-deployment Verification
- [ ] Test multilingual routing (zh/en/ar)
- [ ] Verify image optimization and loading
- [ ] Check API routes functionality
- [ ] Confirm HTTPS is working properly

### Platform-Specific Notes

#### Vercel
- Automatic build detection and optimization
- Built-in image optimization with Next.js
- Edge functions for API routes
- Automatic static asset caching

#### Netlify
- Uses `@netlify/plugin-nextjs` for optimization
- Standalone output mode configured
- Custom redirects for internationalization
- Build processing with CSS/JS minification