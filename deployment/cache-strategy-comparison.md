# 🔄 缓存策略详细对比

## 平台缓存配置映射

### Vercel (vercel.json) vs Netlify (_headers)

| 功能 | Vercel 配置 | Netlify 配置 | 一致性 |
|------|-------------|--------------|--------|
| **安全头** | `vercel.json` headers | `_headers` 全局配置 | ✅ |
| **静态资源** | `/_next/static/*` | `/_next/static/*` | ✅ |
| **字体文件** | `/(.*).woff2` 等 | `/*.woff2` 等 | ✅ |
| **图片文件** | `/(.*).jpg` 等 | `/*.jpg` 等 | ✅ |
| **API 路由** | Next.js 内置 | `_headers` /api/* | ✅ |

## 缓存时间配置

### 静态资源 (1年缓存)
```
Cache-Control: public, immutable, max-age=31536000
```

**适用文件**:
- `/_next/static/*` - Next.js 构建产物
- `*.woff2, *.woff, *.ttf` - 字体文件
- `*.ico` - 图标文件

**原理**: 这些文件有哈希值，内容变化时 URL 会变化，可以安全地长期缓存。

### 图片资源 (CDN长缓存 + 浏览器短缓存)
```
Cache-Control: public, max-age=86400, s-maxage=31536000, stale-while-revalidate=86400
```

**配置说明**:
- `max-age=86400`: 浏览器缓存1天
- `s-maxage=31536000`: CDN缓存1年
- `stale-while-revalidate=86400`: 后台更新1天

**适用文件**:
- `*.jpg, *.jpeg, *.png, *.webp, *.avif, *.svg, *.gif`

### API 响应缓存
```
Cache-Control: public, s-maxage=300, max-age=60, stale-while-revalidate=86400
```

**搜索 API** (5分钟 CDN, 1分钟浏览器):
- 搜索结果变化较快
- 短期缓存提升性能
- 后台更新保证数据新鲜度

**筛选 API** (10分钟 CDN, 2分钟浏览器):
- 筛选结果相对稳定
- 中等时长缓存平衡性能和实时性

### ISR 页面缓存

| 页面类型 | 重新验证时间 | 原因 |
|----------|--------------|------|
| 首页 | 1小时 (3600s) | 内容更新适中 |
| 产品列表 | 30分钟 (1800s) | 商品信息变化较快 |
| 产品详情 | 1小时 (3600s) | 单个商品信息稳定 |
| 筛选页面 | 15分钟 (900s) | 动态筛选需要及时更新 |
| 静态页面 | 2小时 (7200s) | 关于/联系页面变化少 |

## 安全头统一配置

两个平台都应用相同的安全头：

```
X-Content-Type-Options: nosniff
X-Frame-Options: DENY  
X-XSS-Protection: 1; mode=block
Referrer-Policy: strict-origin-when-cross-origin
```

## 平台特有优化

### Vercel 优化
- **函数超时**: 30秒 (API 路由)
- **图片优化**: 1年最小缓存 TTL
- **代码分割**: Vercel 专用 chunks
- **Edge Functions**: 全球边缘计算

### Netlify 优化  
- **函数打包**: esbuild 优化
- **图片优化**: 1天最小缓存 TTL
- **SVG 安全**: 启用受控 SVG 渲染
- **Edge Functions**: 全球边缘计算

## 缓存验证命令

### 1. 检查静态资源缓存
```bash
curl -I https://your-domain.com/_next/static/chunks/main-[hash].js
# 期望: Cache-Control: public, immutable, max-age=31536000
```

### 2. 检查图片缓存
```bash
curl -I https://your-domain.com/images/sample.jpg
# 期望: Cache-Control: public, max-age=86400, s-maxage=31536000
```

### 3. 检查 API 缓存
```bash
curl -I https://your-domain.com/api/search?q=test
# 期望: Cache-Control: public, s-maxage=300, max-age=60
```

### 4. 检查页面 ISR
```bash
curl -I https://your-domain.com/zh
# 期望: X-Vercel-Cache: HIT 或 Cache-Control 头
```

## 缓存性能指标

### 目标缓存命中率
- **静态资源**: > 95%
- **图片资源**: > 90%  
- **API 响应**: > 80%
- **页面内容**: > 85%

### 监控方法
1. **Vercel Analytics**: 自动缓存统计
2. **Netlify Analytics**: 缓存性能报告
3. **CDN 日志**: 命中率分析
4. **浏览器 DevTools**: 网络面板验证

## 缓存失效策略

### 自动失效
- **代码更新**: 静态资源哈希变化
- **ISR 超时**: 页面自动重新验证
- **Webhook**: Sanity 内容更新触发

### 手动失效
```bash
# Vercel
vercel env pull
npx vercel --prod

# Netlify  
netlify deploy --prod
```

## 最佳实践建议

1. **渐进式缓存**: 从短到长逐步调整缓存时间
2. **A/B 测试**: 对比不同缓存策略的性能影响
3. **监控告警**: 设置缓存命中率低的告警
4. **定期审查**: 每月检查缓存策略有效性
5. **文档同步**: 更新缓存配置时同步更新文档