# Netlify Deployment Guide for MyNgaPop

This guide addresses common issues and best practices for deploying the MyNgaPop Next.js application on Netlify.

## Common Issues and Solutions

### CSS MIME Type Error

**Problem**: CSS files served with incorrect MIME type causing the error:
```
Refused to execute script from '/_next/static/css/xxx.css' because its MIME type ('text/css') is not executable
```

**Solution**: The project has been configured with multiple layers of protection:

1. **Headers Configuration**: Both `public/_headers` and `netlify.toml` contain explicit Content-Type headers for CSS files
2. **Redirect Rules**: Static assets are explicitly handled with proper redirect rules
3. **Build Configuration**: Standard Next.js build with @netlify/plugin-nextjs handling optimization

### Configuration Overview

#### netlify.toml Structure
```toml
[build]
  # Standard Next.js build command
  command = "pnpm build"
  publish = ".next"

# Headers ensure correct MIME types
[[headers]]
  for = "/_next/static/css/*"
  [headers.values]
    Content-Type = "text/css; charset=utf-8"
    Cache-Control = "public, immutable, max-age=********"

# Static asset redirects ensure proper serving
[[redirects]]
  from = "/_next/static/*"
  to = "/_next/static/:splat"
  status = 200
  force = true

# Note: No catch-all redirect needed - @netlify/plugin-nextjs handles routing
```

#### Next.js Configuration
- **Output Mode**: Standard (not standalone) - Netlify plugin handles optimization
- **Trailing Slash**: Disabled for standard Next.js routing
- **Custom Build ID**: Ensures consistent caching across deployments

## Deployment Checklist

1. **Environment Variables**
   - Set all required Sanity environment variables in Netlify dashboard
   - Ensure `NETLIFY=true` is automatically set by platform

2. **Build Settings**
   - Node version: 18 (specified in netlify.toml)
   - Build command: `pnpm build`
   - Publish directory: `.next`

3. **Post-Deployment Verification**
   - Check browser console for MIME type errors
   - Verify CSS files load correctly
   - Test all locale routes (/, /zh, /en, /ar)
   - Confirm images and fonts load properly

## Troubleshooting

### Build Failures
1. Check Node version compatibility (requires v18+)
2. Verify all dependencies are installed
3. Review build logs for specific errors

### Static Asset Issues
1. Verify `_headers` file is in the public directory
2. Check that netlify.toml headers configuration is correct
3. Ensure @netlify/plugin-nextjs is installed and configured

### Routing Problems
1. Verify locale redirects are working
2. Check API routes are accessible
3. No catch-all redirect should be used with Next.js App Router

## Performance Optimization

1. **Caching**: Static assets cached for 1 year with immutable flag
2. **Image Optimization**: Next.js image optimization configured for Netlify
3. **Build Processing**: CSS/JS bundling and minification enabled

## Additional Notes

- Use standard Next.js build output (not standalone) with Netlify
- Headers are configured in both `_headers` and `netlify.toml` for redundancy
- Static file redirects ensure proper MIME types
- The @netlify/plugin-nextjs handles all routing automatically

For more details on Netlify-specific Next.js deployments, refer to the [@netlify/plugin-nextjs documentation](https://github.com/netlify/next-runtime).