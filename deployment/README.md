# 🚀 多平台部署指南

MyNgaPop 项目支持 Vercel 和 Netlify 双平台部署，本文档提供详细的部署配置和最佳实践。

## 📋 目录

- [平台检测](#平台检测)
- [Vercel 部署](#vercel-部署)
- [Netlify 部署](#netlify-部署)
- [缓存策略对比](#缓存策略对比)
- [环境变量配置](#环境变量配置)
- [最佳实践](#最佳实践)
- [故障排除](#故障排除)

## 🔍 平台检测

项目自动检测部署平台并应用相应优化：

```javascript
// next.config.mjs 中的平台检测
const isVercel = process.env.VERCEL === '1'
const isNetlify = process.env.NETLIFY === 'true'
```

### 检测结果

- **Vercel**: `VERCEL=1`
- **Netlify**: `NETLIFY=true`
- **本地开发**: 无特殊环境变量

## ⚡ Vercel 部署

### 配置文件
- `vercel.json` - Vercel 专用配置
- `next.config.mjs` - Next.js 配置（含 Vercel 优化）

### 核心配置

```json
{
  "headers": [
    {
      "source": "/_next/static/(.*)",
      "headers": [
        {
          "key": "Cache-Control",
          "value": "public, immutable, max-age=31536000"
        }
      ]
    }
  ],
  "functions": {
    "src/app/api/**/*.ts": {
      "maxDuration": 30
    }
  }
}
```

### Vercel 特有优化

- **图片缓存**: 1年最小缓存时间
- **代码分割**: Vercel chunks 优化
- **服务端压缩**: 启用
- **Source Maps**: 生产环境禁用

### 部署步骤

1. 连接 GitHub 仓库到 Vercel
2. 设置环境变量（见下文）
3. 构建配置自动检测
4. 部署完成后验证缓存头

## 🌐 Netlify 部署

### 配置文件
- `netlify.toml` - Netlify 专用配置
- `public/_headers` - Netlify 缓存头配置
- `next.config.mjs` - Next.js 配置（含 Netlify 优化）

### 核心配置

```toml
[build]
  command = "pnpm build"
  publish = ".next"

[[plugins]]
  package = "@netlify/plugin-nextjs"

[functions]
  node_bundler = "esbuild"
```

### Netlify 特有优化

- **图片缓存**: 1天浏览器缓存
- **SVG 支持**: 启用危险 SVG 渲染
- **尾随斜杠**: 启用
- **ESM 外部化**: 优化打包

### 部署步骤

1. 连接 GitHub 仓库到 Netlify
2. 安装 Netlify Next.js 插件
3. 设置环境变量（见下文）
4. 配置构建命令: `pnpm build`
5. 发布目录: `.next`

## 📊 缓存策略对比

| 资源类型 | Vercel | Netlify | 说明 |
|---------|--------|---------|------|
| 静态资源 | 1年不变缓存 | 1年不变缓存 | ✅ 完全一致 |
| 图片资源 | CDN 1年，浏览器 1天 | CDN 1年，浏览器 1天 | ✅ 完全一致 |
| API 响应 | 5-10分钟 | 5-10分钟 | ✅ 完全一致 |
| 页面 ISR | 按页面配置 | 按页面配置 | ✅ 完全一致 |
| 安全头 | vercel.json | _headers | ✅ 功能一致 |

### ISR 配置一致性

```javascript
// 所有平台统一的 ISR 配置
export const revalidate = 3600 // 1小时
```

## 🔐 环境变量配置

### 必需环境变量

```bash
# Sanity CMS 配置
NEXT_PUBLIC_SANITY_PROJECT_ID=4za4x22i
NEXT_PUBLIC_SANITY_DATASET=production
SANITY_API_TOKEN=your-read-token

# 可选：暂存环境支持
NEXT_PUBLIC_USE_STAGING_DATASET=true
NEXT_PUBLIC_SANITY_DATASET_STAGING=staging
SANITY_STUDIO_USE_STAGING=true
```

### Vercel 设置

1. 项目设置 → Environment Variables
2. 添加上述环境变量
3. 确保生产/预览/开发环境都已配置

### Netlify 设置

1. Site settings → Environment variables
2. 添加上述环境变量
3. 注意：Netlify 环境变量不区分环境

## 🎯 最佳实践

### 1. 配置分离原则

```
vercel.json     → Vercel 专用配置
netlify.toml    → Netlify 专用配置
_headers        → Netlify 缓存头
next.config.mjs → 通用配置 + 平台优化
```

### 2. 缓存策略统一

- 静态资源: **1年不变缓存**
- 动态内容: **ISR + API 缓存**
- 图片资源: **CDN 长缓存 + 浏览器短缓存**

### 3. 性能优化

#### Vercel 优化
- 启用 Edge Functions
- 使用 Vercel Analytics
- 配置地理位置路由

#### Netlify 优化
- 启用 Edge Functions
- 使用 Netlify Analytics
- 配置表单处理

### 4. 监控和调试

#### 缓存验证

```bash
# 检查缓存头
curl -I https://your-domain.com/_next/static/chunks/main.js

# 预期结果
Cache-Control: public, immutable, max-age=31536000
```

#### ISR 验证

```bash
# 检查页面重新验证
curl -I https://your-domain.com/zh/products

# 预期结果  
Cache-Control: s-maxage=3600, stale-while-revalidate
```

## 🐛 故障排除

### 常见问题

#### 1. 缓存不生效

**症状**: 静态资源重复下载
**解决**: 
- Vercel: 检查 `vercel.json` 配置
- Netlify: 检查 `_headers` 文件格式

#### 2. ISR 不工作

**症状**: 页面不自动更新
**解决**:
- 检查 `revalidate` 导出
- 验证 Webhook 配置
- 查看构建日志

#### 3. 图片优化失效

**症状**: 图片加载慢
**解决**:
- 检查 `next.config.mjs` 图片域名配置
- 验证 Sanity CDN 访问
- 检查网络策略

#### 4. 国际化路由问题

**症状**: 语言切换异常
**解决**:
- Vercel: 检查 middleware 配置
- Netlify: 检查重定向规则

### 调试工具

```bash
# 构建分析
npm run analyze

# 类型检查
npm run type-check

# 本地预览生产构建
npm run build && npm run start
```

## 📈 性能基准

### 目标指标

- **首屏加载**: < 2s
- **LCP**: < 2.5s  
- **FID**: < 100ms
- **CLS**: < 0.1
- **缓存命中率**: > 90%

### 监控工具

- **Vercel**: Vercel Analytics + Speed Insights
- **Netlify**: Netlify Analytics + Core Web Vitals
- **通用**: Google PageSpeed Insights, Lighthouse

## 🔄 更新流程

1. **开发**: 本地测试所有功能
2. **暂存**: 部署到预览环境验证
3. **生产**: 同时部署到 Vercel 和 Netlify
4. **验证**: 检查缓存策略和性能指标
5. **监控**: 持续观察错误率和性能

---

*最后更新: 2025-01-11*
*维护者: MyNgaPop 开发团队*