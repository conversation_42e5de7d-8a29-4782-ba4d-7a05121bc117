{"headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}, {"source": "/favicon.ico", "headers": [{"key": "Cache-Control", "value": "public, immutable, max-age=31536000"}]}, {"source": "/(.*).ico", "headers": [{"key": "Cache-Control", "value": "public, immutable, max-age=31536000"}]}, {"source": "/_next/static/css/(.*)", "headers": [{"key": "Content-Type", "value": "text/css; charset=utf-8"}, {"key": "Cache-Control", "value": "public, immutable, max-age=31536000"}]}, {"source": "/_next/static/js/(.*)", "headers": [{"key": "Content-Type", "value": "application/javascript; charset=utf-8"}, {"key": "Cache-Control", "value": "public, immutable, max-age=31536000"}]}, {"source": "/_next/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, immutable, max-age=31536000"}]}, {"source": "/(.*).woff2", "headers": [{"key": "Cache-Control", "value": "public, immutable, max-age=31536000"}, {"key": "Access-Control-Allow-Origin", "value": "*"}]}, {"source": "/(.*).woff", "headers": [{"key": "Cache-Control", "value": "public, immutable, max-age=31536000"}, {"key": "Access-Control-Allow-Origin", "value": "*"}]}, {"source": "/(.*).ttf", "headers": [{"key": "Cache-Control", "value": "public, immutable, max-age=31536000"}, {"key": "Access-Control-Allow-Origin", "value": "*"}]}, {"source": "/(.*).otf", "headers": [{"key": "Cache-Control", "value": "public, immutable, max-age=31536000"}, {"key": "Access-Control-Allow-Origin", "value": "*"}]}, {"source": "/(.*).eot", "headers": [{"key": "Cache-Control", "value": "public, immutable, max-age=31536000"}, {"key": "Access-Control-Allow-Origin", "value": "*"}]}, {"source": "/(.*).jpg", "headers": [{"key": "Cache-Control", "value": "public, max-age=86400, s-maxage=31536000, stale-while-revalidate=86400"}]}, {"source": "/(.*).jpeg", "headers": [{"key": "Cache-Control", "value": "public, max-age=86400, s-maxage=31536000, stale-while-revalidate=86400"}]}, {"source": "/(.*).png", "headers": [{"key": "Cache-Control", "value": "public, max-age=86400, s-maxage=31536000, stale-while-revalidate=86400"}]}, {"source": "/(.*).webp", "headers": [{"key": "Cache-Control", "value": "public, max-age=86400, s-maxage=31536000, stale-while-revalidate=86400"}]}, {"source": "/(.*).avif", "headers": [{"key": "Cache-Control", "value": "public, max-age=86400, s-maxage=31536000, stale-while-revalidate=86400"}]}, {"source": "/(.*).svg", "headers": [{"key": "Cache-Control", "value": "public, max-age=86400, s-maxage=31536000, stale-while-revalidate=86400"}]}, {"source": "/(.*).gif", "headers": [{"key": "Cache-Control", "value": "public, max-age=86400, s-maxage=31536000, stale-while-revalidate=86400"}]}, {"source": "/robots.txt", "headers": [{"key": "Cache-Control", "value": "public, max-age=3600, s-maxage=86400"}]}, {"source": "/sitemap.xml", "headers": [{"key": "Cache-Control", "value": "public, max-age=3600, s-maxage=86400"}]}, {"source": "/manifest.json", "headers": [{"key": "Cache-Control", "value": "public, max-age=3600, s-maxage=86400"}]}], "rewrites": [{"source": "/sitemap.xml", "destination": "/api/sitemap"}], "functions": {"src/app/api/**/*.ts": {"maxDuration": 30}}}