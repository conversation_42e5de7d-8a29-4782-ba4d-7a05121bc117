# Staging environment configuration example
# Copy this file to .env.staging and update the values

# Sanity configuration
NEXT_PUBLIC_SANITY_PROJECT_ID=your-project-id
NEXT_PUBLIC_SANITY_DATASET=production
SANITY_API_TOKEN=your-read-token
SANITY_WEBHOOK_SECRET=your-webhook-secret

# Enable staging dataset
NEXT_PUBLIC_USE_STAGING_DATASET=true
NEXT_PUBLIC_SANITY_DATASET_STAGING=development

# Sanity Studio configuration
SANITY_STUDIO_USE_STAGING=true
SANITY_STUDIO_DATASET_STAGING=development

# Next.js configuration
NEXT_PUBLIC_BASE_URL=https://staging.yourdomain.com