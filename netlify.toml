[build]
  command = "pnpm build"
  publish = ".next"

# Environment variables for build
[build.environment]
  NODE_VERSION = "18"
  NPM_FLAGS = "--version"

# Netlify Next.js plugin configuration
[[plugins]]
  package = "@netlify/plugin-nextjs"

# Function configuration for API routes
[functions]
  # Set longer timeout for API functions (especially for Sanity queries)
  node_bundler = "esbuild"

# ISR and caching configuration
[build.processing]
  skip_processing = false

[build.processing.css]
  bundle = true
  minify = true

[build.processing.js]
  bundle = true
  minify = true

[build.processing.html]
  pretty_urls = true

# Headers configuration for static assets (backup to _headers file)
[[headers]]
  for = "/_next/static/css/*"
  [headers.values]
    Content-Type = "text/css; charset=utf-8"
    Cache-Control = "public, immutable, max-age=31536000"
    X-Content-Type-Options = "nosniff"

[[headers]]
  for = "/_next/static/js/*"
  [headers.values]
    Content-Type = "application/javascript; charset=utf-8"
    Cache-Control = "public, immutable, max-age=31536000"
    X-Content-Type-Options = "nosniff"

[[headers]]
  for = "/_next/static/chunks/*"
  [headers.values]
    Content-Type = "application/javascript; charset=utf-8"
    Cache-Control = "public, immutable, max-age=31536000"
    X-Content-Type-Options = "nosniff"

[[headers]]
  for = "/_next/static/media/*"
  [headers.values]
    Cache-Control = "public, max-age=86400, s-maxage=31536000, stale-while-revalidate=86400"
    X-Content-Type-Options = "nosniff"

[[headers]]
  for = "/*.css"
  [headers.values]
    Content-Type = "text/css; charset=utf-8"
    Cache-Control = "public, max-age=31536000"
    X-Content-Type-Options = "nosniff"

[[headers]]
  for = "/*.js"
  [headers.values]
    Content-Type = "application/javascript; charset=utf-8"
    Cache-Control = "public, max-age=31536000"
    X-Content-Type-Options = "nosniff"

# Redirects for static assets - ensure they are NOT redirected to index.html
[[redirects]]
  from = "/_next/static/*"
  to = "/_next/static/:splat"
  status = 200
  force = true

[[redirects]]
  from = "/_next/*"
  to = "/_next/:splat"
  status = 200
  force = true

[[redirects]]
  from = "/*.css"
  to = "/:splat.css"
  status = 200
  force = true

[[redirects]]
  from = "/*.js"
  to = "/:splat.js"
  status = 200
  force = true

[[redirects]]
  from = "/images/*"
  to = "/images/:splat"
  status = 200
  force = true

# Static asset redirects for public directory files
[[redirects]]
  from = "/*.png"
  to = "/:splat.png"
  status = 200
  force = true

[[redirects]]
  from = "/*.svg"
  to = "/:splat.svg"
  status = 200
  force = true

[[redirects]]
  from = "/*.ico"
  to = "/:splat.ico"
  status = 200
  force = true

[[redirects]]
  from = "/fonts/*"
  to = "/fonts/:splat"
  status = 200
  force = true

# Redirects for internationalization and sitemap
[[redirects]]
  from = "/sitemap.xml"
  to = "/api/sitemap"
  status = 200

# Default locale redirect
[[redirects]]
  from = "/"
  to = "/zh"
  status = 302
  conditions = {Language = ["zh"]}

[[redirects]]
  from = "/"
  to = "/en"  
  status = 302
  conditions = {Language = ["en"]}

[[redirects]]
  from = "/"
  to = "/ar"
  status = 302
  conditions = {Language = ["ar"]}

# Remove fallback redirect as it's not needed with Next.js App Router
# The @netlify/plugin-nextjs handles routing automatically

