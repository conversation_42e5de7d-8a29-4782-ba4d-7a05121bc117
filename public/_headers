# Netlify Headers Configuration for MyNgaPop
# This ensures proper MIME types and caching for all static assets

# Apply to all paths
/*
  X-Content-Type-Options: nosniff
  X-Frame-Options: DENY
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin

# CSS Files - Fix MIME type issues
/_next/static/css/*
  Content-Type: text/css; charset=utf-8
  Cache-Control: public, immutable, max-age=31536000

# JavaScript Files
/_next/static/js/*
  Content-Type: application/javascript; charset=utf-8
  Cache-Control: public, immutable, max-age=31536000

# Static CSS/JS files
*.css
  Content-Type: text/css; charset=utf-8
  Cache-Control: public, max-age=31536000

*.js
  Content-Type: application/javascript; charset=utf-8
  Cache-Control: public, max-age=31536000

# Favicon and Icons
/favicon.ico
  Content-Type: image/x-icon
  Cache-Control: public, immutable, max-age=31536000

*.ico
  Content-Type: image/x-icon
  Cache-Control: public, immutable, max-age=31536000

# Images
*.jpg
  Content-Type: image/jpeg
  Cache-Control: public, max-age=86400, s-maxage=31536000, stale-while-revalidate=86400

*.jpeg
  Content-Type: image/jpeg
  Cache-Control: public, max-age=86400, s-maxage=31536000, stale-while-revalidate=86400

*.png
  Content-Type: image/png
  Cache-Control: public, max-age=86400, s-maxage=31536000, stale-while-revalidate=86400

*.webp
  Content-Type: image/webp
  Cache-Control: public, max-age=86400, s-maxage=31536000, stale-while-revalidate=86400

*.avif
  Content-Type: image/avif
  Cache-Control: public, max-age=86400, s-maxage=31536000, stale-while-revalidate=86400

*.svg
  Content-Type: image/svg+xml; charset=utf-8
  Cache-Control: public, max-age=86400, s-maxage=31536000, stale-while-revalidate=86400

*.gif
  Content-Type: image/gif
  Cache-Control: public, max-age=86400, s-maxage=31536000, stale-while-revalidate=86400

# Fonts
*.woff2
  Content-Type: font/woff2
  Cache-Control: public, immutable, max-age=31536000
  Access-Control-Allow-Origin: *

*.woff
  Content-Type: font/woff
  Cache-Control: public, immutable, max-age=31536000
  Access-Control-Allow-Origin: *

*.ttf
  Content-Type: font/ttf
  Cache-Control: public, immutable, max-age=31536000
  Access-Control-Allow-Origin: *

*.otf
  Content-Type: font/otf
  Cache-Control: public, immutable, max-age=31536000
  Access-Control-Allow-Origin: *

*.eot
  Content-Type: application/vnd.ms-fontobject
  Cache-Control: public, immutable, max-age=31536000
  Access-Control-Allow-Origin: *

# SEO and API files
/robots.txt
  Content-Type: text/plain; charset=utf-8
  Cache-Control: public, max-age=3600, s-maxage=86400

/sitemap.xml
  Content-Type: application/xml; charset=utf-8
  Cache-Control: public, max-age=3600, s-maxage=86400

/manifest.json
  Content-Type: application/json; charset=utf-8
  Cache-Control: public, max-age=3600, s-maxage=86400

# API Routes - No caching
/api/*
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0