/**
 * Network Detection Utility
 * Enhanced network condition detection for intelligent image optimization
 */

export interface NetworkInfo {
  effectiveType: '4g' | '3g' | '2g' | 'slow-2g' | 'unknown';
  downlink: number; // Mbps
  saveData: boolean;
  rtt: number; // Round trip time in ms
}

export interface QualitySettings {
  imageQuality: number;
  preferWebP: boolean;
  enableBlur: boolean;
  maxWidth?: number;
  maxHeight?: number;
}

/**
 * Network-aware quality adaptation utility
 */
export class NetworkQualityAdapter {
  private static instance: NetworkQualityAdapter;
  private cachedInfo: NetworkInfo | null = null;
  private lastCheck: number = 0;
  private readonly CACHE_DURATION = 30000; // 30 seconds

  static getInstance(): NetworkQualityAdapter {
    if (!NetworkQualityAdapter.instance) {
      NetworkQualityAdapter.instance = new NetworkQualityAdapter();
    }
    return NetworkQualityAdapter.instance;
  }

  /**
   * Get current network information
   */
  getNetworkInfo(): NetworkInfo {
    const now = Date.now();
    
    // Return cached info if still valid
    if (this.cachedInfo && (now - this.lastCheck) < this.CACHE_DURATION) {
      return this.cachedInfo;
    }

    // Get network information from browser API
    const connection = this.getConnection();
    
    const networkInfo: NetworkInfo = {
      effectiveType: connection?.effectiveType as NetworkInfo['effectiveType'] || 'unknown',
      downlink: connection?.downlink || 1,
      saveData: connection?.saveData || false,
      rtt: connection?.rtt || 1000,
    };

    // Cache the result
    this.cachedInfo = networkInfo;
    this.lastCheck = now;

    return networkInfo;
  }

  /**
   * Get optimal quality settings based on network conditions
   */
  getOptimalQuality(isAboveFold = false, isCritical = false): QualitySettings {
    const network = this.getNetworkInfo();
    
    // Base quality settings
    let imageQuality = 85;
    let preferWebP = true;
    let enableBlur = true;
    let maxWidth: number | undefined;
    let maxHeight: number | undefined;

    // Adjust based on network conditions
    switch (network.effectiveType) {
      case '4g':
        imageQuality = network.saveData ? 70 : 90;
        preferWebP = true;
        enableBlur = !isCritical;
        break;
        
      case '3g':
        imageQuality = network.saveData ? 50 : 70;
        preferWebP = true;
        enableBlur = true;
        maxWidth = 1200;
        maxHeight = 1200;
        break;
        
      case '2g':
      case 'slow-2g':
        imageQuality = 40;
        preferWebP = true;
        enableBlur = true;
        maxWidth = 800;
        maxHeight = 800;
        break;
        
      default:
        // Unknown or very slow connection
        imageQuality = network.saveData ? 30 : 60;
        preferWebP = true;
        enableBlur = true;
        maxWidth = 600;
        maxHeight = 600;
        break;
    }

    // Boost quality for critical above-the-fold images
    if (isAboveFold && isCritical && network.effectiveType === '4g') {
      imageQuality = Math.min(95, imageQuality + 10);
    }

    // Further reduce quality if save-data is enabled
    if (network.saveData) {
      imageQuality = Math.max(30, imageQuality - 15);
    }

    // Additional adjustments based on RTT
    if (network.rtt > 2000) {
      imageQuality = Math.max(30, imageQuality - 10);
    }

    return {
      imageQuality,
      preferWebP,
      enableBlur,
      maxWidth,
      maxHeight,
    };
  }

  /**
   * Check if network supports high-quality images
   */
  supportsHighQuality(): boolean {
    const network = this.getNetworkInfo();
    return network.effectiveType === '4g' && 
           !network.saveData && 
           network.downlink > 1.5 && 
           network.rtt < 1000;
  }

  /**
   * Check if we should preload images
   */
  shouldPreloadImages(): boolean {
    const network = this.getNetworkInfo();
    return network.effectiveType === '4g' && 
           !network.saveData && 
           network.downlink > 2;
  }

  /**
   * Get safe connection object
   */
  private getConnection(): any {
    if (typeof navigator === 'undefined') return null;
    
    return (navigator as any).connection || 
           (navigator as any).mozConnection || 
           (navigator as any).webkitConnection ||
           null;
  }

  /**
   * Add network change listener with debouncing
   */
  onNetworkChange(callback: (networkInfo: NetworkInfo) => void): () => void {
    const connection = this.getConnection();
    
    if (!connection) {
      return () => {}; // No-op cleanup function
    }

    let debounceTimer: NodeJS.Timeout | null = null;

    const handleChange = () => {
      // Clear any existing timer
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }
      
      // Debounce the callback by 1 second to avoid excessive updates
      debounceTimer = setTimeout(() => {
        // Invalidate cache on network change
        this.cachedInfo = null;
        this.lastCheck = 0;
        
        // Call callback with new network info
        callback(this.getNetworkInfo());
        debounceTimer = null;
      }, 1000);
    };

    connection.addEventListener('change', handleChange);
    
    // Return cleanup function
    return () => {
      if (debounceTimer) {
        clearTimeout(debounceTimer);
        debounceTimer = null;
      }
      connection.removeEventListener('change', handleChange);
    };
  }

  /**
   * Performance test to estimate actual network speed
   */
  async measureNetworkSpeed(): Promise<number> {
    try {
      const testImageUrl = 'data:image/gif;base64,R0lGODlhAQABAIAAAP///wAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==';
      const start = performance.now();
      
      await fetch(testImageUrl, { cache: 'no-cache' });
      
      const duration = performance.now() - start;
      
      // Estimate speed based on test duration (rough approximation)
      if (duration < 50) return 10; // Very fast (4G+)
      if (duration < 100) return 5;  // Fast (4G)
      if (duration < 200) return 2;  // Medium (3G)
      return 1; // Slow (2G or worse)
      
    } catch (error) {
      console.warn('Network speed test failed:', error);
      return 1; // Assume slow connection on error
    }
  }
}

/**
 * Priority hint determination based on image context
 */
export function getImagePriority(
  isAboveFold: boolean,
  isCritical: boolean,
  index: number = 0
): 'high' | 'low' | 'auto' {
  // Critical above-the-fold images get high priority
  if (isCritical && isAboveFold) {
    return 'high';
  }
  
  // First few images in viewport get high priority
  if (isAboveFold && index < 3) {
    return 'high';
  }
  
  // Images likely to be needed soon get auto priority
  if (index < 6) {
    return 'auto';
  }
  
  // Everything else gets low priority
  return 'low';
}

/**
 * Hook for using network-aware quality adaptation
 */
export function useNetworkQuality() {
  const adapter = NetworkQualityAdapter.getInstance();
  
  return {
    getOptimalQuality: adapter.getOptimalQuality.bind(adapter),
    getNetworkInfo: adapter.getNetworkInfo.bind(adapter),
    supportsHighQuality: adapter.supportsHighQuality.bind(adapter),
    shouldPreloadImages: adapter.shouldPreloadImages.bind(adapter),
    onNetworkChange: adapter.onNetworkChange.bind(adapter),
    measureNetworkSpeed: adapter.measureNetworkSpeed.bind(adapter),
  };
}