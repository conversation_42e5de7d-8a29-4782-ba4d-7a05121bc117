@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
  
  /* Dynamic theme colors - RGB values for Tailwind compatibility */
  /* Primary color shades (default blue) */
  --color-primary-50: 239 246 255;
  --color-primary-100: 219 234 254;
  --color-primary-200: 191 219 254;
  --color-primary-300: 147 197 253;
  --color-primary-400: 96 165 250;
  --color-primary-500: 59 130 246;
  --color-primary-600: 37 99 235;
  --color-primary-700: 29 78 216;
  --color-primary-800: 30 64 175;
  --color-primary-900: 30 58 138;
  
  /* Secondary color shades (default purple) */
  --color-secondary-50: 250 245 255;
  --color-secondary-100: 243 232 255;
  --color-secondary-200: 233 213 255;
  --color-secondary-300: 216 180 254;
  --color-secondary-400: 192 132 252;
  --color-secondary-500: 168 85 247;
  --color-secondary-600: 147 51 234;
  --color-secondary-700: 126 34 206;
  --color-secondary-800: 107 33 168;
  --color-secondary-900: 88 28 135;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  /* Custom scrollbar styles */
  .scrollbar-thin {
    scrollbar-width: thin;
  }
  
  .scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
    background-color: #d1d5db;
    border-radius: 9999px;
  }
  
  .scrollbar-track-gray-100::-webkit-scrollbar-track {
    background-color: #f3f4f6;
    border-radius: 9999px;
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }
}

/* Animation utilities */
@layer components {
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.4s ease-out;
  }
}