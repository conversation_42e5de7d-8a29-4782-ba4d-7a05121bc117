'use client'

import dynamic from 'next/dynamic'

// Dynamically import admin component to separate admin functionality
// from main application bundle - admin pages are rarely accessed
const PromotePageClient = dynamic(
  () => import('@/components/admin/PromotePageClient').then((mod) => ({ default: mod.PromotePageClient })),
  {
    ssr: false, // Admin functionality is client-side only
  }
)

export default function PromotePage() {
  return <PromotePageClient />
}