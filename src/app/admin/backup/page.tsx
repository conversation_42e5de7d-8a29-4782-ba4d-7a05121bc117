'use client'

import dynamic from 'next/dynamic'

// Dynamically import admin component to separate admin functionality
// from main application bundle - admin pages are rarely accessed
const BackupPageClient = dynamic(
  () => import('@/components/admin/BackupPageClient').then((mod) => ({ default: mod.BackupPageClient })),
  {
    ssr: false, // Admin functionality is client-side only
  }
)

export default function BackupPage() {
  return <BackupPageClient />
}