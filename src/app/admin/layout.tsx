import { Suspense } from 'react'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Admin Dashboard - MyNgaPop',
  description: 'MyNgaPop admin management interface',
  robots: 'noindex,nofollow', // Prevent search engine indexing
}

function AdminLoadingSkeleton() {
  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-6xl mx-auto animate-pulse">
        {/* Header skeleton */}
        <div className="mb-8">
          <div className="h-8 bg-gray-200 rounded w-64 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-96"></div>
        </div>

        {/* Content skeleton */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <div className="h-6 bg-gray-200 rounded w-48 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-100 rounded w-full"></div>
            <div className="h-4 bg-gray-100 rounded w-3/4"></div>
            <div className="h-4 bg-gray-100 rounded w-1/2"></div>
          </div>
        </div>

        {/* Action buttons skeleton */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex gap-4">
            <div className="h-10 bg-gray-200 rounded w-32"></div>
            <div className="h-10 bg-gray-200 rounded w-32"></div>
          </div>
        </div>
      </div>
    </div>
  )
}

interface AdminLayoutProps {
  children: React.ReactNode
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  return (
    <div className="admin-layout">
      {/* Admin navigation/header could go here */}
      <header className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="max-w-6xl mx-auto">
          <nav className="flex items-center justify-between">
            <div>
              <h1 className="text-lg font-semibold text-gray-900">MyNgaPop Admin</h1>
            </div>
            <div className="flex items-center gap-4">
              <a 
                href="/" 
                className="text-blue-600 hover:text-blue-800 text-sm"
              >
                返回前台
              </a>
            </div>
          </nav>
        </div>
      </header>

      {/* Main content with Suspense boundary */}
      <main>
        <Suspense fallback={<AdminLoadingSkeleton />}>
          {children}
        </Suspense>
      </main>
    </div>
  )
}