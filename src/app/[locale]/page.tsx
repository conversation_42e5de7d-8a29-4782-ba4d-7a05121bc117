import { getTranslations } from 'next-intl/server'
import { Suspense } from 'react'
import dynamic from 'next/dynamic'
import { Metadata } from 'next'
import { Header } from '@/components/layout/Header'

// ISR configuration - revalidate every hour for homepage
export const revalidate = 3600
import { Footer } from '@/components/layout/Footer'
import { client } from '@/lib/sanity/client'
import { homepageModularQuery } from '@/lib/sanity/queries'
import { HomepageModularData } from '@/types/sanity'

// Dynamic import for HomePageClient to reduce initial bundle size
const HomePageClient = dynamic(
  () => import('@/components/home/<USER>').then((mod) => ({ default: mod.HomePageClient })),
  {
    ssr: true,
    loading: () => (
      <div className="min-h-screen bg-gradient-to-br from-primary-50 via-primary-100 to-secondary-50 animate-pulse">
        <div className="container mx-auto px-4 py-16 min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="w-32 h-8 bg-gray-200 rounded mx-auto mb-6"></div>
            <div className="w-48 h-6 bg-gray-200 rounded mx-auto mb-4"></div>
            <div className="w-64 h-4 bg-gray-200 rounded mx-auto"></div>
          </div>
        </div>
      </div>
    )
  }
)

// Fallback component for Suspense
function HomePageFallback() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 animate-pulse">
      <div className="container mx-auto px-4 py-16 min-h-screen flex items-center justify-center">
        <div className="text-center max-w-4xl mx-auto">
          <div className="w-32 h-8 bg-gray-200 rounded mx-auto mb-6"></div>
          <div className="w-48 h-6 bg-gray-200 rounded mx-auto mb-4"></div>
          <div className="w-64 h-4 bg-gray-200 rounded mx-auto mb-12"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="bg-white/80 rounded-2xl shadow-lg p-8">
                <div className="w-12 h-12 bg-gray-200 rounded mx-auto mb-4"></div>
                <div className="w-24 h-4 bg-gray-200 rounded mx-auto mb-2"></div>
                <div className="w-32 h-3 bg-gray-200 rounded mx-auto"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

interface HomePageProps {
  params: Promise<{ locale: string }>
}

// Metadata generation for SEO optimization
export async function generateMetadata({ params }: HomePageProps): Promise<Metadata> {
  const { locale } = await params
  const t = await getTranslations('HomePage')
  const tMeta = await getTranslations('Metadata')
  
  return {
    title: tMeta('title'),
    description: tMeta('description'),
    keywords: tMeta('keywords'),
    openGraph: {
      title: 'MyNgaPop',
      description: t('description'),
      type: 'website',
      locale: locale,
    },
    robots: 'index,follow',
    alternates: {
      languages: {
        'zh': '/zh',
        'en': '/en', 
        'ar': '/ar'
      }
    }
  }
}

export default async function HomePage({ params }: HomePageProps) {
  const t = await getTranslations('HomePage')
  const tActions = await getTranslations('Actions')
  const { locale } = await params

  let homepageData: HomepageWithFeatured | null = null
  
  try {
    // Fetch homepage content from Sanity CMS with timeout
    const fetchPromise = client.fetch<HomepageModularData>(homepageModularQuery)
    const timeoutPromise = new Promise<never>((_, reject) => 
      setTimeout(() => reject(new Error('Timeout')), 5000)
    )
    
    homepageData = await Promise.race([fetchPromise, timeoutPromise])
  } catch (error) {
    console.error('Error fetching homepage data:', error)
    // Continue with fallback content
  }

  // If CMS data exists (either homepage or featured products), use it
  if (homepageData?.homepage || homepageData?.featuredProducts) {
    return (
      <>
        <Header />
        <Suspense fallback={<HomePageFallback />}>
          <HomePageClient
            data={homepageData}
            locale={locale}
          />
        </Suspense>
        <Footer />
      </>
    )
  }

  // Fallback to translation-based content if CMS data is not available
  const translations = {
    welcome: t('welcome'),
    learnMore: t('learnMore'),
    featuredProducts: t('featuredProducts'),
    achievements: t('achievements'),
    subtitle: t('subtitle'),
    description: t('description'),
    brandTagline: t('brandTagline'),
    qualityBadge: t('qualityBadge'),
    trustMessage: t('trustMessage'),
    viewAllProducts: tActions('viewAllProducts'),
    viewDetails: tActions('viewDetails'),
    features: {
      multilingual: {
        title: t('features.multilingual.title'),
        description: t('features.multilingual.description')
      },
      cms: {
        title: t('features.cms.title'),
        description: t('features.cms.description')
      },
      global: {
        title: t('features.global.title'),
        description: t('features.global.description')
      }
    },
    cta: {
      browse: t('cta.browse')
    },
    brandStory: {
      title: t('brandStory.title'),
      description: t('brandStory.description')
    },
    stats: {
      products: t('stats.products'),
      countries: t('stats.countries'),
      satisfaction: t('stats.satisfaction'),
      support: t('stats.support')
    }
  }

  return (
    <>
      <Header />
      <Suspense fallback={<HomePageFallback />}>
        <HomePageClient data={{}} translations={translations} />
      </Suspense>
      <Footer />
    </>
  )
}