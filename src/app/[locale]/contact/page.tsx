import { getTranslations } from 'next-intl/server'
import { <PERSON><PERSON> } from '@/components/layout/Header'
import { Footer } from '@/components/layout/Footer'
import { fetchWithErrorHandling } from '@/lib/sanity/client'

// ISR configuration - revalidate every 2 hours for static content
export const revalidate = 7200
import { contactPageQuery } from '@/lib/sanity/queries'
import { ContactPage } from '@/types/sanity'
import ContactPageContentDynamic from '@/components/contact/ContactPageContentDynamic'
import { ErrorBoundary } from '@/components/common/ErrorBoundary'

interface ContactPageProps {
  params: Promise<{
    locale: string
  }>
}

export async function generateMetadata({ params }: ContactPageProps) {
  const { locale } = await params
  const contactPageData: ContactPage | null = await fetchWithErrorHandling<ContactPage>(contactPageQuery)
  const t = await getTranslations('ContactPage')

  const title = contactPageData?.seo?.title?.[locale as keyof typeof contactPageData.seo.title] 
    || contactPageData?.title?.[locale as keyof typeof contactPageData.title]
    || t('title')

  const description = contactPageData?.seo?.description?.[locale as keyof typeof contactPageData.seo.description] 
    || t('description')

  return {
    title,
    description,
    keywords: contactPageData?.seo?.keywords?.join(', '),
    openGraph: {
      title,
      description,
      type: 'website',
    },
  }
}

export default async function ContactPageRoute({ params }: ContactPageProps) {
  const { locale } = await params
  const t = await getTranslations('ContactPage')
  
  // Fetch contact page data from Sanity with error handling
  const contactPageData: ContactPage | null = await fetchWithErrorHandling<ContactPage>(contactPageQuery)

  return (
    <>
      <Header />
      <main className="min-h-screen bg-gray-50">
        {contactPageData ? (
          <ErrorBoundary>
            <ContactPageContentDynamic data={contactPageData} locale={locale} />
          </ErrorBoundary>
        ) : (
          // Fallback content when Sanity is unavailable
          <div className="container mx-auto px-4 py-16">
            <div className="max-w-4xl mx-auto">
              <h1 className="text-4xl font-bold text-gray-900 mb-8 text-center">
                {t('title')}
              </h1>
              
              <div className="grid md:grid-cols-2 gap-12">
                {/* Contact Information */}
                <div>
                  <h2 className="text-2xl font-semibold mb-6">{t('info.title')}</h2>
                  <div className="space-y-4">
                    <div className="flex items-start space-x-3">
                      <div className="w-5 h-5 text-blue-600 mt-1">
                        <svg fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div>
                        <h3 className="font-medium">{t('info.address.label')}</h3>
                        <p className="text-gray-600">{t('info.address.value')}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start space-x-3">
                      <div className="w-5 h-5 text-blue-600 mt-1">
                        <svg fill="currentColor" viewBox="0 0 20 20">
                          <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                        </svg>
                      </div>
                      <div>
                        <h3 className="font-medium">{t('info.phone.label')}</h3>
                        <p className="text-gray-600">{t('info.phone.value')}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start space-x-3">
                      <div className="w-5 h-5 text-blue-600 mt-1">
                        <svg fill="currentColor" viewBox="0 0 20 20">
                          <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                          <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                        </svg>
                      </div>
                      <div>
                        <h3 className="font-medium">{t('info.email.label')}</h3>
                        <p className="text-gray-600">{t('info.email.value')}</p>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Contact Form */}
                <div>
                  <h2 className="text-2xl font-semibold mb-6">{t('form.title')}</h2>
                  <form className="space-y-4">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                        {t('form.name')}
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder={t('form.namePlaceholder')}
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                        {t('form.email')}
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder={t('form.emailPlaceholder')}
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-1">
                        {t('form.subject')}
                      </label>
                      <input
                        type="text"
                        id="subject"
                        name="subject"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder={t('form.subjectPlaceholder')}
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
                        {t('form.message')}
                      </label>
                      <textarea
                        id="message"
                        name="message"
                        rows={5}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder={t('form.messagePlaceholder')}
                      />
                    </div>
                    
                    <button
                      type="submit"
                      className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50"
                      disabled
                    >
                      {t('form.submit')} ({t('form.demo')})
                    </button>
                  </form>
                </div>
              </div>
              
              <div className="mt-12 bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center">
                <div className="text-yellow-600 mb-2">
                  <svg className="w-8 h-8 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <p className="text-sm text-yellow-700">
                  {t('cmsWarning')}
                </p>
              </div>
            </div>
          </div>
        )}
      </main>
      <Footer />
    </>
  )
}