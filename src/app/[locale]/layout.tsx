import { NextIntlClientProvider } from 'next-intl'
import { getMessages } from 'next-intl/server'
import { notFound } from 'next/navigation'
import { routing } from '@/i18n/routing'
import type { Metadata } from 'next'
import DatasetIndicator from '@/components/common/DatasetIndicator'
import { ThemeProvider } from '@/components/providers/ThemeProvider'
import { getSiteSettings } from '@/lib/sanity/getSiteSettings'
import '../globals.css'

type Locale = (typeof routing.locales)[number]

export const metadata: Metadata = {
  title: 'MyNgaPop - 跨境动漫周边品牌',
  description: '专业的跨境动漫周边品牌官网，提供优质的动漫周边产品',
}

export default async function LocaleLayout({
  children,
  params
}: {
  children: React.ReactNode
  params: Promise<{ locale: string }>
}) {
  const { locale } = await params
  
  // Type-safe locale validation
  function isValidLocale(locale: string): locale is Locale {
    return routing.locales.includes(locale as Locale)
  }
  
  if (!isValidLocale(locale)) {
    notFound()
  }

  // Providing all messages to the client
  // side is the easiest way to get started
  const messages = await getMessages()
  
  // Fetch site settings for theme colors
  const siteSettings = await getSiteSettings()
  const primaryColorValue = siteSettings?.appearance?.primaryColor
  const secondaryColorValue = siteSettings?.appearance?.secondaryColor
  
  // Handle both string and object color formats
  const primaryColor = typeof primaryColorValue === 'string' 
    ? primaryColorValue 
    : primaryColorValue?.hex
  const secondaryColor = typeof secondaryColorValue === 'string' 
    ? secondaryColorValue 
    : secondaryColorValue?.hex

  return (
    <html lang={locale}>
      <head>
        <link rel="icon" href="/favicon.ico" sizes="any" />
      </head>
      <body>
        <NextIntlClientProvider messages={messages}>
          <ThemeProvider primaryColor={primaryColor} secondaryColor={secondaryColor}>
            {children}
            <DatasetIndicator />
          </ThemeProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  )
}