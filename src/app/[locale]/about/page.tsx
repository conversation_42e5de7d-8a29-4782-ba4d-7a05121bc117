import { getTranslations } from 'next-intl/server'
import { Head<PERSON> } from '@/components/layout/Header'
import { Footer } from '@/components/layout/Footer'
import { fetchWithErrorHandling } from '@/lib/sanity/client'

// ISR configuration - revalidate every 2 hours for static content
export const revalidate = 7200
import { aboutPageQuery } from '@/lib/sanity/queries'
import { AboutPage } from '@/types/sanity'
import AboutPageContentDynamic from '@/components/about/AboutPageContentDynamic'
import { ErrorBoundary } from '@/components/common/ErrorBoundary'

interface AboutPageProps {
  params: Promise<{
    locale: string
  }>
}

export async function generateMetadata({ params }: AboutPageProps) {
  const { locale } = await params
  const aboutPageData: AboutPage | null = await fetchWithErrorHandling<AboutPage>(aboutPageQuery)
  const t = await getTranslations('AboutPage')

  const title = aboutPageData?.seo?.title?.[locale as keyof typeof aboutPageData.seo.title] 
    || aboutPageData?.title?.[locale as keyof typeof aboutPageData.title]
    || t('title')

  const description = aboutPageData?.seo?.description?.[locale as keyof typeof aboutPageData.seo.description] 
    || t('description')

  return {
    title,
    description,
    keywords: aboutPageData?.seo?.keywords?.join(', '),
    openGraph: {
      title,
      description,
      type: 'website',
    },
  }
}

export default async function AboutPageRoute({ params }: AboutPageProps) {
  const { locale } = await params
  const t = await getTranslations('AboutPage')
  
  // Fetch about page data from Sanity with error handling
  const aboutPageData: AboutPage | null = await fetchWithErrorHandling<AboutPage>(aboutPageQuery)

  return (
    <>
      <Header />
      <main className="min-h-screen bg-gray-50">
        {aboutPageData ? (
          <ErrorBoundary>
            <AboutPageContentDynamic data={aboutPageData} locale={locale} />
          </ErrorBoundary>
        ) : (
          // Fallback content when Sanity is unavailable
          <div className="container mx-auto px-4 py-16">
            <div className="max-w-4xl mx-auto">
              <h1 className="text-4xl font-bold text-gray-900 mb-8 text-center">
                {t('title')}
              </h1>
              
              <div className="prose prose-lg max-w-none">
                <p className="text-lg text-gray-600 mb-8 text-center">
                  {t('subtitle')}
                </p>
                
                <div className="grid md:grid-cols-2 gap-8 mb-12">
                  <div>
                    <h2 className="text-2xl font-semibold mb-4">{t('mission.title')}</h2>
                    <p className="text-gray-600">
                      {t('mission.description')}
                    </p>
                  </div>
                  <div>
                    <h2 className="text-2xl font-semibold mb-4">{t('vision.title')}</h2>
                    <p className="text-gray-600">
                      {t('vision.description')}
                    </p>
                  </div>
                </div>
                
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center">
                  <div className="text-yellow-600 mb-2">
                    <svg className="w-8 h-8 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <p className="text-sm text-yellow-700">
                    {t('cmsWarning')}
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}
      </main>
      <Footer />
    </>
  )
}