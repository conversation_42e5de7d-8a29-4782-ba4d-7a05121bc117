import { Suspense } from 'react'
import { <PERSON><PERSON> } from '@/components/layout/Header'
import { Footer } from '@/components/layout/Footer'
import { ProductSkeleton } from '@/components/product/ProductSkeleton'
import ProductsPageClient from './ProductsPageClient'

// ISR configuration - revalidate every 30 minutes for product listings
export const revalidate = 1800

interface ProductsPageProps {
  params: Promise<{
    locale: string
  }>
}

export default async function ProductsPage({ params }: ProductsPageProps) {
  const { locale } = await params

  return (
    <>
      <Header />
      <Suspense fallback={
        <main className="min-h-screen bg-gray-50">
          <div className="container mx-auto px-4 py-8">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded w-64 mb-4"></div>
              <div className="h-4 bg-gray-200 rounded w-96 mb-8"></div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 gap-6">
                {Array.from({ length: 24 }).map((_, i) => (
                  <ProductSkeleton key={i} />
                ))}
              </div>
            </div>
          </div>
        </main>
      }>
        <ProductsPageClient locale={locale} />
      </Suspense>
      <Footer />
    </>
  )
}