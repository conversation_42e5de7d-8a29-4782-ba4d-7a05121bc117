'use client'

import { useState, useEffect, useCallback } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { useTranslations } from 'next-intl'
import { SORT_OPTIONS } from '@/constants/filters'
import ProductCard from '@/components/product/ProductCard'
import { ProductFilterDynamic as ProductFilter, FilterState, FilterOptions } from '@/components/product/ProductFilterDynamic'
import { Product } from '@/types/sanity'
import { motion, AnimatePresence } from 'framer-motion'
import { ChevronRightIcon, AdjustmentsHorizontalIcon } from '@heroicons/react/24/outline'

interface FilteredProductsResponse {
  products: Product[]
  totalCount: number
  currentPage: number
  totalPages: number
  filters: {
    appliedFilters: FilterState
    availableFilters: FilterOptions
  }
}

interface ActiveFilterTag {
  type: 'category' | 'priceRange' | 'stockStatus' | 'tag' | 'ipSeries'
  label: string
  value: string
  onRemove: () => void
}

export default function FilterPageClient({ locale }: { locale: string }) {
  const t = useTranslations('FilterPage')
  const searchParams = useSearchParams()
  const router = useRouter()
  
  const [products, setProducts] = useState<Product[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [totalCount, setTotalCount] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [appliedFilters, setAppliedFilters] = useState<FilterState>({
    ipSeries: [],
    categories: [],
    priceRange: { min: 0, max: 10000 },
    stockStatus: [],
    tags: [],
    sortBy: 'newest'
  })
  const [filterOptions, setFilterOptions] = useState<FilterOptions>({
    ipSeries: [],
    categories: [],
    priceRanges: [
      { label: '0-100', min: 0, max: 100 },
      { label: '100-500', min: 100, max: 500 },
      { label: '500-1000', min: 500, max: 1000 },
      { label: '1000+', min: 1000, max: 10000 }
    ],
    stockStatuses: [
      { value: 'in-stock', label: { zh: '现货', en: 'In Stock', ar: 'متوفر' }, count: 0 },
      { value: 'pre-order', label: { zh: '预售', en: 'Pre-order', ar: 'طلب مسبق' }, count: 0 },
      { value: 'sold-out', label: { zh: '售罄', en: 'Sold Out', ar: 'نفد المخزون' }, count: 0 }
    ],
    availableTags: [],
    sortOptions: SORT_OPTIONS
  })

  // Fetch filtered products
  const fetchFilteredProducts = useCallback(async (filters: FilterState, page: number = 1) => {
    setIsLoading(true)
    setError(null)
    
    try {
      const searchTerm = searchParams.get('search') || searchParams.get('q')
      const queryParams = new URLSearchParams({
        ipSeries: filters.ipSeries.join(','),
        categories: filters.categories.join(','),
        minPrice: filters.priceRange.min > 0 ? filters.priceRange.min.toString() : '',
        maxPrice: filters.priceRange.max < 10000 ? filters.priceRange.max.toString() : '',
        stockStatus: filters.stockStatus.join(','),
        tags: filters.tags.join(','),
        sortBy: filters.sortBy,
        page: page.toString(),
        limit: '20'
      })
      
      if (searchTerm) {
        queryParams.set('q', searchTerm)
      }
      
      // 移除空值
      const params = new URLSearchParams()
      for (const [key, value] of queryParams.entries()) {
        if (value && value !== '') {
          params.set(key, value)
        }
      }
      
      const response = await fetch(`/api/products/filter?${params.toString()}`)
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const data: FilteredProductsResponse = await response.json()
      
      setProducts(data.products)
      setTotalCount(data.totalCount)
      setCurrentPage(data.currentPage)
      setTotalPages(data.totalPages)
      setFilterOptions(data.filters.availableFilters)
      setAppliedFilters(data.filters.appliedFilters)
      
    } catch (error) {
      console.error('Failed to fetch filtered products:', error)
      setError(error instanceof Error ? error.message : 'Error loading products')
      setProducts([])
      setTotalCount(0)
      setCurrentPage(1)
      setTotalPages(1)
    } finally {
      setIsLoading(false)
    }
  }, [searchParams])

  // 处理筛选器变化
  const handleFilterChange = (filters: FilterState) => {
    setCurrentPage(1)
    fetchFilteredProducts(filters, 1)
  }

  // 处理分页
  const handlePageChange = (page: number) => {
    fetchFilteredProducts(appliedFilters, page)
  }

  // 生成活跃筛选标签
  const generateActiveFilterTags = (): ActiveFilterTag[] => {
    const tags: ActiveFilterTag[] = []

    // IP 系列标签
    appliedFilters.ipSeries.forEach(ipSeriesId => {
      const ipSeriesOption = filterOptions.ipSeries.find(i => i._id === ipSeriesId)
      if (ipSeriesOption) {
        tags.push({
          type: 'ipSeries',
          label: ipSeriesOption.name[locale as keyof typeof ipSeriesOption.name] || ipSeriesOption.name.en,
          value: ipSeriesId,
          onRemove: () => {
            const newFilters = {
              ...appliedFilters,
              ipSeries: appliedFilters.ipSeries.filter(id => id !== ipSeriesId)
            }
            handleFilterChange(newFilters)
          }
        })
      }
    })

    // 分类标签
    appliedFilters.categories.forEach(category => {
      const categoryOption = filterOptions.categories.find(c => c.slug.current === category)
      if (categoryOption) {
        tags.push({
          type: 'category',
          label: categoryOption.name[locale as keyof typeof categoryOption.name] || categoryOption.name.en,
          value: category,
          onRemove: () => {
            const newFilters = {
              ...appliedFilters,
              categories: appliedFilters.categories.filter(c => c !== category)
            }
            handleFilterChange(newFilters)
          }
        })
      }
    })

    // 价格范围标签
    const { min, max } = appliedFilters.priceRange
    if (min > 0 || max < 10000) {
      tags.push({
        type: 'priceRange',
        label: `$${min}-$${max}`,
        value: `${min}-${max}`,
        onRemove: () => {
          const newFilters = {
            ...appliedFilters,
            priceRange: { min: 0, max: 10000 }
          }
          handleFilterChange(newFilters)
        }
      })
    }

    // 库存状态标签
    appliedFilters.stockStatus.forEach(status => {
      const statusOption = filterOptions.stockStatuses.find(s => s.value === status)
      if (statusOption) {
        tags.push({
          type: 'stockStatus',
          label: statusOption.label[locale as keyof typeof statusOption.label] || statusOption.label.en,
          value: status,
          onRemove: () => {
            const newFilters = {
              ...appliedFilters,
              stockStatus: appliedFilters.stockStatus.filter(s => s !== status)
            }
            handleFilterChange(newFilters)
          }
        })
      }
    })

    // 标签
    appliedFilters.tags.forEach(tag => {
      const tagOption = filterOptions.availableTags.find(t => t.value === tag)
      if (tagOption) {
        tags.push({
          type: 'tag',
          label: tagOption.label[locale as keyof typeof tagOption.label] || tagOption.label.en,
          value: tag,
          onRemove: () => {
            const newFilters = {
              ...appliedFilters,
              tags: appliedFilters.tags.filter(t => t !== tag)
            }
            handleFilterChange(newFilters)
          }
        })
      }
    })

    return tags
  }

  // 清除所有筛选器
  const clearAllFilters = () => {
    const defaultFilters: FilterState = {
      ipSeries: [],
      categories: [],
      priceRange: { min: 0, max: 10000 },
      stockStatus: [],
      tags: [],
      sortBy: 'newest'
    }
    handleFilterChange(defaultFilters)
  }

  // 初始加载
  useEffect(() => {
    const initialFilters: FilterState = {
      ipSeries: searchParams.get('ipSeries')?.split(',').filter(Boolean) || [],
      categories: searchParams.get('categories')?.split(',').filter(Boolean) || [],
      priceRange: {
        min: searchParams.get('minPrice') ? parseInt(searchParams.get('minPrice')!) : 0,
        max: searchParams.get('maxPrice') ? parseInt(searchParams.get('maxPrice')!) : 10000
      },
      stockStatus: searchParams.get('stockStatus')?.split(',').filter(Boolean) || [],
      tags: searchParams.get('tags')?.split(',').filter(Boolean) || [],
      sortBy: searchParams.get('sortBy') || 'newest'
    }
    
    fetchFilteredProducts(initialFilters)
  }, [searchParams, fetchFilteredProducts])

  const activeFilterTags = generateActiveFilterTags()

  return (
    <main className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* 面包屑导航 */}
        <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-6">
          <a href={`/${locale}`} className="hover:text-blue-600">
            {t('breadcrumb.home')}
          </a>
          <ChevronRightIcon className="h-4 w-4" />
          <a href={`/${locale}/products`} className="hover:text-blue-600">
            {t('breadcrumb.products')}
          </a>
          <ChevronRightIcon className="h-4 w-4" />
          <span className="text-gray-900">{t('breadcrumb.filter')}</span>
        </nav>

        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {t('title')}
          </h1>
          <p className="text-gray-600">
            {t('subtitle')}
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* 侧边栏筛选器 */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-lg p-6 sticky top-8">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                  <AdjustmentsHorizontalIcon className="h-5 w-5 mr-2" />
                  {t('filters.title')}
                </h2>
                {activeFilterTags.length > 0 && (
                  <button
                    onClick={clearAllFilters}
                    className="text-sm text-red-600 hover:text-red-700"
                  >
                    {t('filters.clearAll')}
                  </button>
                )}
              </div>

              {/* 活跃筛选标签 */}
              {activeFilterTags.length > 0 && (
                <div className="mb-6">
                  <h3 className="text-sm font-medium text-gray-700 mb-3">
                    {t('filters.active')}
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {activeFilterTags.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                      >
                        {tag.label}
                        <button
                          onClick={tag.onRemove}
                          className="ml-2 h-4 w-4 rounded-full bg-blue-200 text-blue-600 hover:bg-blue-300 flex items-center justify-center"
                        >
                          ×
                        </button>
                      </span>
                    ))}
                  </div>
                </div>
              )}

              <ProductFilter
                initialFilters={appliedFilters}
                filterOptions={filterOptions}
                onFilterChange={handleFilterChange}
                locale={locale}
              />
            </div>
          </div>

          {/* 主内容 */}
          <div className="lg:col-span-3">
            {/* 活动筛选器 */}
            <div className="mb-6 min-h-[40px]">
              {activeFilterTags.length > 0 && (
                <div className="flex items-center flex-wrap gap-2">
                  <span className="text-sm font-semibold">{t('activeFilters')}:</span>
                  {activeFilterTags.map(tag => (
                    <span
                      key={`${tag.type}-${tag.value}`}
                      className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                    >
                      {tag.label}
                      <button
                        onClick={tag.onRemove}
                        className="ml-1.5 -mr-1 flex-shrink-0 inline-flex items-center justify-center h-4 w-4 rounded-full text-blue-500 hover:bg-blue-200 hover:text-blue-600 focus:outline-none focus:bg-blue-500 focus:text-white"
                      >
                        <span className="sr-only">{t('removeFilter', { filterName: tag.label })}</span>
                        <svg className="h-2 w-2" stroke="currentColor" fill="none" viewBox="0 0 8 8">
                          <path strokeLinecap="round" strokeWidth="1.5" d="M1 1l6 6m0-6L1 7" />
                        </svg>
                      </button>
                    </span>
                  ))}
                  <button
                    onClick={clearAllFilters}
                    className="text-sm text-gray-500 hover:text-gray-700 underline"
                  >
                    {t('clearAll')}
                  </button>
                </div>
              )}
            </div>

            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold">
                {isLoading ? t('loading') : t('resultsCount', { count: totalCount })}
              </h2>
              <select
                value={appliedFilters.sortBy}
                onChange={(e) => handleFilterChange({ ...appliedFilters, sortBy: e.target.value })}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
              >
                {filterOptions.sortOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label[locale as keyof typeof option.label] || option.label.en}
                  </option>
                ))}
              </select>
            </div>

            <AnimatePresence>
              {isLoading ? (
                <motion.div
                  key="loading"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6"
                >
                  {Array.from({ length: 8 }).map((_, i) => (
                    <div key={i} className="bg-white rounded-lg shadow-md overflow-hidden animate-pulse">
                      <div className="w-full h-48 bg-gray-200"></div>
                      <div className="p-4">
                        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                        <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                      </div>
                    </div>
                  ))}
                </motion.div>
              ) : error ? (
                <motion.div
                  key="error"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className="text-center py-12"
                >
                  <div className="bg-red-50 border border-red-200 rounded-lg p-6">
                    <h3 className="text-lg font-medium text-red-800 mb-2">{t('error.title')}</h3>
                    <p className="text-red-600 mb-4">{error}</p>
                    <button
                      onClick={() => fetchFilteredProducts(appliedFilters, currentPage)}
                      className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
                    >
                      {t('error.retry')}
                    </button>
                  </div>
                </motion.div>
              ) : products.length > 0 ? (
                <motion.div
                  key="products"
                  initial="hidden"
                  animate="visible"
                  variants={{
                    hidden: { opacity: 0 },
                    visible: {
                      opacity: 1,
                      transition: {
                        staggerChildren: 0.05
                      }
                    }
                  }}
                  className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6"
                >
                  {products.map(product => (
                    <motion.div
                      key={product._id}
                      variants={{
                        hidden: { y: 20, opacity: 0 },
                        visible: { y: 0, opacity: 1 }
                      }}
                    >
                      <ProductCard product={product} locale={locale} />
                    </motion.div>
                  ))}
                </motion.div>
              ) : (
                <motion.div
                  key="no-results"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className="text-center py-12"
                >
                  <div className="bg-gray-100 rounded-lg p-8">
                    <h3 className="text-xl font-semibold text-gray-800 mb-2">{t('noResults.title')}</h3>
                    <p className="text-gray-600">{t('noResults.message')}</p>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            {totalPages > 1 && (
              <div className="mt-8 flex justify-center">
                <nav className="inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                    <button
                      key={page}
                      onClick={() => handlePageChange(page)}
                      className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                        currentPage === page
                          ? 'z-10 bg-indigo-50 border-indigo-500 text-indigo-600'
                          : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                      }`}
                    >
                      {page}
                    </button>
                  ))}
                </nav>
              </div>
            )}
          </div>
        </div>
      </div>
    </main>
  )
}