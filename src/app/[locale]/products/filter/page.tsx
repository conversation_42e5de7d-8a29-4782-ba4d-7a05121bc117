import { Suspense } from 'react'
import { <PERSON><PERSON> } from '@/components/layout/Header'
import { Footer } from '@/components/layout/Footer'
import FilterPageClient from './FilterPageClient'

// ISR configuration - revalidate every 15 minutes for dynamic filtering
export const revalidate = 900

interface FilterPageProps {
  params: Promise<{
    locale: string
  }>
}

export default async function FilterPage({ params }: FilterPageProps) {
  const { locale } = await params

  return (
    <>
      <Header />
      <Suspense fallback={
        <main className="min-h-screen bg-gray-50">
          <div className="container mx-auto px-4 py-8">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded w-64 mb-8"></div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 gap-6">
                {Array.from({ length: 20 }).map((_, i) => (
                  <div key={i} className="bg-white rounded-lg shadow-lg p-6">
                    <div className="bg-gray-200 h-48 rounded-lg mb-4"></div>
                    <div className="bg-gray-200 h-4 rounded mb-2"></div>
                    <div className="bg-gray-200 h-4 rounded mb-4 w-2/3"></div>
                    <div className="bg-gray-200 h-8 rounded"></div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </main>
      }>
        <FilterPageClient locale={locale} />
      </Suspense>
      <Footer />
    </>
  )
}