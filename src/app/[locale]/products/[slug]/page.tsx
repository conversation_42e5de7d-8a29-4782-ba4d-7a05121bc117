import { getTranslations } from 'next-intl/server'
import { notFound } from 'next/navigation'
import { Suspense } from 'react'
import Image from 'next/image'
import PortableText from '@/components/ui/PortableText'
import { ProductImageGallery } from '@/components/product/ProductImageGallery'

// ISR configuration - revalidate every hour for product details
export const revalidate = 3600
import { Header } from '@/components/layout/Header'
import { Footer } from '@/components/layout/Footer'
import ProductCard from '@/components/product/ProductCard'
import { ProductSkeleton } from '@/components/product/ProductSkeleton'
import { fetchWithErrorHandling } from '@/lib/sanity/client'
import { resolveImageUrl, resolveImageAlt } from '@/lib/sanity/image'
import { productBySlugQuery, productsQuery } from '@/lib/sanity/queries'
import { Product } from '@/types/sanity'
import OptimizedImage from '@/components/ui/OptimizedImage'
import { ErrorBoundary } from '@/components/common/ErrorBoundary'

interface ProductDetailPageProps {
  params: Promise<{
    slug: string
    locale: string
  }>
}



// Separate component for product content with its own loading state
async function ProductContent({ slug, locale }: { slug: string, locale: string }) {
  const t = await getTranslations('ProductDetailPage')
  
  const product = await fetchWithErrorHandling<Product>(productBySlugQuery, { slug })
  
  if (!product) {
    notFound()
  }

  const name = product.name?.[locale as keyof typeof product.name] || product.name?.zh || 'Product Name'
  const shortDesc = product.shortDescription?.[locale as keyof typeof product.shortDescription] || product.shortDescription?.zh || ''
  const description = product.description?.[locale as keyof typeof product.description] || product.description?.zh || []

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
      {/* Product Gallery */}
      <div className="space-y-4">
        <ProductImageGallery
          gallery={product.gallery || []}
          productName={name}
        />
      </div>

      {/* Product Details */}
      <div className="bg-white rounded-lg shadow-lg p-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          {name || 'Product Name'}
        </h1>
        
        {product.price && (
          <div className="text-2xl font-semibold text-blue-600 mb-6">
            {product.currency === 'CNY' && '¥'}
            {product.currency === 'USD' && '$'}
            {product.currency === 'AED' && 'د.إ'}
            {product.price}
          </div>
        )}

        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-2">{t('description.title')}</h3>
            {/* 描述: 使用PortableText组件渲染富文本 */}
            <div className="text-gray-600 prose prose-sm max-w-none">
              {description && Array.isArray(description) && description.length > 0 ? (
                <PortableText value={description} className="prose-sm" />
              ) : (
                <p className="text-gray-500 italic">{t('description.content')}</p>
              )}
            </div>
          </div>

          <div className="flex space-x-4">
            <a 
              href="mailto:<EMAIL>"
              className="flex-1 bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors text-center block"
            >
              {t('actions.contactUs')}
            </a>
            <button className="px-6 py-3 border border-gray-300 rounded-lg font-semibold hover:bg-gray-50 transition-colors">
              {t('actions.addToWishlist')}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

// Separate component for related products with its own loading state
async function RelatedProducts({ currentProductId, locale }: { currentProductId: string, locale: string }) {
  const t = await getTranslations('ProductDetailPage')
  
  const relatedProducts = await fetchWithErrorHandling<Product[]>(productsQuery) || []
  const filteredRelatedProducts = relatedProducts
    .filter(p => p._id !== currentProductId)
    .slice(0, 4)

  if (filteredRelatedProducts.length === 0) {
    return null
  }

  return (
    <div className="mt-16">
      <h2 className="text-2xl font-bold text-gray-900 mb-8">{t('related.title')}</h2>
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {filteredRelatedProducts.map((relatedProduct, index) => (
          <ProductCard
            key={relatedProduct._id}
            product={relatedProduct}
            locale={locale}
            index={index}
          />
        ))}
      </div>
    </div>
  )
}

export default async function ProductDetailPage({ params }: ProductDetailPageProps) {
  try {
    const { slug, locale } = await params

    // Validate parameters
    if (!slug || typeof slug !== 'string') {
      notFound()
    }

    // Pre-fetch product to get ID for related products
    const product = await fetchWithErrorHandling<Product>(productBySlugQuery, { slug })
    
    if (!product) {
      notFound()
    }

    return (
      <>
        <Header />
        <main className="min-h-screen bg-gray-50">
          <div className="container mx-auto px-4 py-16">
            {/* Breadcrumb with loading state */}
            <Suspense fallback={
              <div className="mb-8">
                <div className="h-4 bg-gray-200 rounded w-64 animate-pulse"></div>
              </div>
            }>
              <nav className="mb-8" aria-label="Breadcrumb">
                <div className="text-sm text-gray-600">
                  <span>
                    <a href="/" className="text-blue-600 hover:text-blue-800">Home</a>
                    {' / '}
                    <a href="/products" className="text-blue-600 hover:text-blue-800">Products</a>
                    {' / '}
                    <span className="text-gray-900">{product.name?.[locale as keyof typeof product.name] || product.name?.zh || 'Product'}</span>
                  </span>
                </div>
              </nav>
            </Suspense>

            {/* Main product content with loading skeleton */}
            <ErrorBoundary fallback={
              <div className="bg-red-50 border border-red-200 rounded-lg p-8 text-center">
                <h3 className="text-lg font-semibold text-red-800 mb-2">Product Not Available</h3>
                <p className="text-sm text-red-600">Unable to load product details. Please try again later.</p>
              </div>
            }>
              <Suspense fallback={<ProductSkeleton variant="detail" />}>
                <ProductContent slug={slug} locale={locale} />
              </Suspense>
            </ErrorBoundary>

            {/* Related products with loading skeleton */}
            <ErrorBoundary fallback={
              <div className="mt-16 bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center">
                <h3 className="text-lg font-semibold text-yellow-800 mb-2">Related Products Unavailable</h3>
                <p className="text-sm text-yellow-600">Unable to load related products at this time.</p>
              </div>
            }>
              <Suspense fallback={
                <div className="mt-16">
                  <div className="h-8 bg-gray-200 rounded w-48 mb-8 animate-pulse"></div>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <ProductSkeleton variant="card" count={4} />
                  </div>
                </div>
              }>
                <RelatedProducts currentProductId={product._id} locale={locale} />
              </Suspense>
            </ErrorBoundary>
          </div>
        </main>
        <Footer />
      </>
    )
  } catch (error) {
    console.error('Error in ProductDetailPage:', error)
    notFound()
  }
}