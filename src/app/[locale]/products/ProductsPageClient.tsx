'use client'

import { useState, useEffect, useCallback } from 'react'
import { useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { useTranslations } from 'next-intl'
import { SORT_OPTIONS } from '@/constants/filters'
import ProductCard from '@/components/product/ProductCard'
import { ProductFilterDynamic as ProductFilter, FilterState, FilterOptions } from '@/components/product/ProductFilterDynamic'
import { ProductSkeleton } from '@/components/product/ProductSkeleton'
import { Product } from '@/types/sanity'
import { motion, AnimatePresence } from 'framer-motion'

interface FilteredProductsResponse {
  products: Product[]
  totalCount: number
  currentPage: number
  totalPages: number
  filters: {
    appliedFilters: FilterState
    availableFilters: FilterOptions
  }
}

export default function ProductsPageClient({ locale }: { locale: string }) {
  const t = useTranslations('ProductsPage')
  const tCommon = useTranslations('Common')
  const searchParams = useSearchParams()
  
  const [products, setProducts] = useState<Product[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [totalCount, setTotalCount] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [filterOptions, setFilterOptions] = useState<FilterOptions>({
    ipSeries: [],
    categories: [],
    priceRanges: [
      { label: '0-100', min: 0, max: 100 },
      { label: '100-500', min: 100, max: 500 },
      { label: '500-1000', min: 500, max: 1000 },
      { label: '1000+', min: 1000, max: 10000 }
    ],
    stockStatuses: [
      { value: 'in-stock', label: { zh: '现货', en: 'In Stock', ar: 'متوفر' }, count: 0 },
      { value: 'pre-order', label: { zh: '预售', en: 'Pre-order', ar: 'طلب مسبق' }, count: 0 },
      { value: 'sold-out', label: { zh: '售罄', en: 'Sold Out', ar: 'نفد المخزون' }, count: 0 }
    ],
    availableTags: [],
    sortOptions: SORT_OPTIONS
  })

  // 获取筛选后的产品
  const fetchFilteredProducts = useCallback(async (filters: FilterState, page: number = 1) => {
    setIsLoading(true)
    setError(null)
    
    try {
      const searchTerm = searchParams.get('search') || searchParams.get('q')
      const queryParams = new URLSearchParams({
        ipSeries: filters.ipSeries.join(','),
        categories: filters.categories.join(','),
        minPrice: filters.priceRange.min > 0 ? filters.priceRange.min.toString() : '',
        maxPrice: filters.priceRange.max < 10000 ? filters.priceRange.max.toString() : '',
        stockStatus: filters.stockStatus.join(','),
        tags: filters.tags.join(','),
        sortBy: filters.sortBy,
        page: page.toString(),
        limit: '24'
      })
      
      if (searchTerm) {
        queryParams.set('q', searchTerm)
      }
      
      // 移除空值
      const params = new URLSearchParams()
      for (const [key, value] of queryParams.entries()) {
        if (value && value !== '') {
          params.set(key, value)
        }
      }
      
      const response = await fetch(`/api/products/filter?${params.toString()}`)
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const data: FilteredProductsResponse = await response.json()
      
      setProducts(data.products)
      setTotalCount(data.totalCount)
      setCurrentPage(data.currentPage)
      setTotalPages(data.totalPages)
      if (data.filters?.availableFilters) {
        setFilterOptions(prevOptions => ({
          ...prevOptions,
          ...data.filters.availableFilters
        }))
      }
      
    } catch (error) {
      console.error('获取筛选产品失败:', error)
      setError(error instanceof Error ? error.message : '加载产品时出错')
      // 回退到空状态
      setProducts([])
      setTotalCount(0)
      setCurrentPage(1)
      setTotalPages(1)
    } finally {
      setIsLoading(false)
    }
  }, [searchParams])

  // 处理筛选器变化
  const handleFilterChange = useCallback((filters: FilterState) => {
    setCurrentPage(1) // 重置到第一页
    fetchFilteredProducts(filters, 1)
  }, [fetchFilteredProducts])

  // 处理分页
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    // 从URL重建当前筛选状态
    const currentFilters: FilterState = {
      ipSeries: searchParams.get('ipSeries')?.split(',').filter(Boolean) || [],
      categories: searchParams.get('categories')?.split(',').filter(Boolean) || [],
      priceRange: {
        min: searchParams.get('minPrice') ? parseInt(searchParams.get('minPrice')!) : 0,
        max: searchParams.get('maxPrice') ? parseInt(searchParams.get('maxPrice')!) : 10000
      },
      stockStatus: searchParams.get('stockStatus')?.split(',').filter(Boolean) || [],
      tags: searchParams.get('tags')?.split(',').filter(Boolean) || [],
      sortBy: searchParams.get('sortBy') || 'newest'
    }
    fetchFilteredProducts(currentFilters, page)
  }

  // 初始化筛选器和产品
  useEffect(() => {
    // 从URL参数构建初始筛选状态
    const initialFilters: FilterState = {
      ipSeries: searchParams.get('ipSeries')?.split(',').filter(Boolean) || [],
      categories: searchParams.get('categories')?.split(',').filter(Boolean) || [],
      priceRange: {
        min: searchParams.get('minPrice') ? parseInt(searchParams.get('minPrice')!) : 0,
        max: searchParams.get('maxPrice') ? parseInt(searchParams.get('maxPrice')!) : 10000
      },
      stockStatus: searchParams.get('stockStatus')?.split(',').filter(Boolean) || [],
      tags: searchParams.get('tags')?.split(',').filter(Boolean) || [],
      sortBy: searchParams.get('sortBy') || 'newest'
    };

    fetchFilteredProducts(initialFilters, 1);
  }, [searchParams, fetchFilteredProducts]);

  const getLocalizedLabel = (label: { zh: string; en: string; ar: string }) => {
    return label[locale as keyof typeof label] || label.en
  }

  return (
    <main className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            {t('title')}
          </h1>
          <p className="text-gray-600 text-lg">
            {t('subtitle')}
          </p>
        </div>
        
        {/* Mobile filter button */}
        <div className="lg:hidden mb-4">
          <Link href={`/${locale}/products/filter`}>
            <button 
              className="w-full bg-white rounded-lg shadow-md p-4 flex items-center justify-between text-gray-900 hover:bg-gray-50 transition-colors"
              data-testid="filter-button"
            >
              <span className="font-semibold">{t('filters')}</span>
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
              </svg>
            </button>
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* 侧边栏筛选器 - 桌面版 */}
          <div className="hidden lg:block lg:col-span-1">
            <div className="bg-white rounded-lg shadow-lg p-6 sticky top-8">
              <h2 className="text-lg font-semibold text-gray-900 mb-6">
                {t('filters')}
              </h2>
              <ProductFilter
                initialFilters={{
                  ipSeries: searchParams.get('ipSeries')?.split(',').filter(Boolean) || [],
                  categories: searchParams.get('categories')?.split(',').filter(Boolean) || [],
                  priceRange: {
                    min: searchParams.get('minPrice') ? parseInt(searchParams.get('minPrice')!) : 0,
                    max: searchParams.get('maxPrice') ? parseInt(searchParams.get('maxPrice')!) : 10000
                  },
                  stockStatus: searchParams.get('stockStatus')?.split(',').filter(Boolean) || [],
                  tags: searchParams.get('tags')?.split(',').filter(Boolean) || [],
                  sortBy: searchParams.get('sortBy') || 'newest'
                }}
                filterOptions={filterOptions}
                onFilterChange={handleFilterChange}
                locale={locale}
              />
            </div>
          </div>

          {/* 主内容区域 */}
          <div className="lg:col-span-3">
            {/* 搜索结果统计和排序 */}
            <div className="flex items-center justify-between mb-6">
              <div>
                <p className="text-gray-600">
                  {isLoading ? (
                    t('loading')
                  ) : error ? (
                    <span className="text-red-600">{error}</span>
                  ) : (
                    t('resultsCount', { count: totalCount })
                  )}
                </p>
              </div>
              <div className="flex items-center space-x-4">
                <span className="text-sm text-gray-500 dark:text-gray-400">{t('sortBy')}:</span>
                <select
                  value={searchParams.get('sortBy') || 'newest'}
                  onChange={(e) => {
                    const currentFilters: FilterState = {
                      ipSeries: searchParams.get('ipSeries')?.split(',').filter(Boolean) || [],
                      categories: searchParams.get('categories')?.split(',').filter(Boolean) || [],
                      priceRange: {
                        min: searchParams.get('minPrice') ? parseInt(searchParams.get('minPrice')!) : 0,
                        max: searchParams.get('maxPrice') ? parseInt(searchParams.get('maxPrice')!) : 10000
                      },
                      stockStatus: searchParams.get('stockStatus')?.split(',').filter(Boolean) || [],
                      tags: searchParams.get('tags')?.split(',').filter(Boolean) || [],
                      sortBy: e.target.value
                    };
                    handleFilterChange(currentFilters);
                  }}
                  className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-1.5 text-sm focus:ring-2 focus:ring-blue-500 focus:outline-none"
                >
                  {SORT_OPTIONS.map(option => (
                    <option key={option.value} value={option.value}>
                      {getLocalizedLabel(option.label)}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* 产品网格 */}
            <AnimatePresence mode="wait">
              {isLoading ? (
                <motion.div
                  key="loading"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 gap-6"
                >
                  {Array.from({ length: 8 }).map((_, i) => (
                    <ProductSkeleton key={i} />
                  ))}
                </motion.div>
              ) : error ? (
                <motion.div
                  key="error"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className="text-center py-12"
                >
                  <div className="bg-red-50 dark:bg-red-900/10 border border-red-200 dark:border-red-500/30 rounded-lg p-6">
                    <h3 className="text-lg font-medium text-red-800 dark:text-red-300 mb-2">
                      {t('error.title')}
                    </h3>
                    <p className="text-red-600 dark:text-red-400 mb-4">{error}</p>
                    <button
                      onClick={() => {
                        const currentFilters: FilterState = {
                          ipSeries: searchParams.get('ipSeries')?.split(',').filter(Boolean) || [],
                          categories: searchParams.get('categories')?.split(',').filter(Boolean) || [],
                          priceRange: {
                            min: searchParams.get('minPrice') ? parseInt(searchParams.get('minPrice')!) : 0,
                            max: searchParams.get('maxPrice') ? parseInt(searchParams.get('maxPrice')!) : 10000
                          },
                          stockStatus: searchParams.get('stockStatus')?.split(',').filter(Boolean) || [],
                          tags: searchParams.get('tags')?.split(',').filter(Boolean) || [],
                          sortBy: searchParams.get('sortBy') || 'newest'
                        }
                        fetchFilteredProducts(currentFilters, currentPage)
                      }}
                      className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
                    >
                      {t('error.retry')}
                    </button>
                  </div>
                </motion.div>
              ) : products.length === 0 ? (
                <motion.div
                  key="empty"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className="text-center py-12"
                  data-testid="no-products"
                >
                  <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-8">
                    <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-2">
                      {t('noResults.title')}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400">
                      {t('noResults.message')}
                    </p>
                  </div>
                </motion.div>
              ) : (
                <motion.div
                  key="products"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 gap-6"
                >
                  {products.map((product, index) => (
                    <motion.div
                      key={product._id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ 
                        opacity: 1, 
                        y: 0,
                        transition: { delay: index * 0.05 }
                      }}
                    >
                      <ProductCard product={product} locale={locale} />
                    </motion.div>
                  ))}
                </motion.div>
              )}
            </AnimatePresence>

            {/* 分页 */}
            {!isLoading && !error && totalPages > 1 && (
              <div className="flex justify-center mt-12">
                <nav className="flex items-center space-x-2">
                  <button
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage <= 1}
                    className="px-3 py-2 text-sm font-medium text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {t('pagination.previous')}
                  </button>
                  
                  {Array.from({ length: totalPages > 5 ? 5 : totalPages }, (_, i) => {
                    const pageNumber = i + 1;
                    return (
                      <button
                        key={pageNumber}
                        onClick={() => handlePageChange(pageNumber)}
                        className={`px-4 py-2 text-sm font-medium border rounded-md ${
                          currentPage === pageNumber
                            ? 'bg-blue-50 dark:bg-blue-900/50 text-blue-600 dark:text-blue-300 border-blue-500 dark:border-blue-400'
                            : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700'
                        }`}
                      >
                        {pageNumber}
                      </button>
                    )
                  })}
                  
                  {totalPages > 5 && <span className="text-gray-500 dark:text-gray-400">...</span>}
                  
                  <button
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage >= totalPages}
                    className="px-3 py-2 text-sm font-medium text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {t('pagination.next')}
                  </button>
                </nav>
              </div>
            )}
          </div>
        </div>
      </div>
    </main>
  );
}