import { notFound } from 'next/navigation'
import { getRequestConfig } from 'next-intl/server'
import { routing } from '@/i18n/routing'

export default getRequestConfig(async ({ requestLocale }) => {
  // Get the locale from the request
  let locale = await requestLocale
  
  // Validate that the incoming `locale` parameter is valid and provide fallback
  if (!locale || !routing.locales.includes(locale as any)) {
    locale = routing.defaultLocale
  }

  return {
    locale,
    messages: (await import(`@/messages/${locale}.json`)).default
  }
})