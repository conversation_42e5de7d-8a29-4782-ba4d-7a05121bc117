import { NextRequest, NextResponse } from 'next/server'
import { client } from '@/lib/sanity/client'
import { Product } from '@/types/sanity'

export interface FilterParams {
  ipSeries?: string[]
  categories?: string[]
  minPrice?: number
  maxPrice?: number
  stockStatus?: string[]
  tags?: string[]
  sortBy?: string
  searchTerm?: string
  page?: number
  limit?: number
}

export interface FilteredProductsResponse {
  products: Product[]
  totalCount: number
  currentPage: number
  totalPages: number
  filters: {
    appliedFilters: FilterParams
    availableFilters: {
      ipSeries: Array<{
        _id: string
        name: { zh: string; en: string; ar: string }
        productCount: number
      }>
      categories: Array<{
        _id: string
        name: { zh: string; en: string; ar: string }
        slug: { current: string }
        productCount: number
      }>
      priceRange: { min: number; max: number }
      stockStatuses: Array<{
        value: string
        count: number
        label: { zh: string; en: string; ar: string }
      }>
      availableTags: Array<{
        value: string
        count: number
        label: { zh: string; en: string; ar: string }
      }>
    }
  }
}

function buildFilterQuery(params: FilterParams): string {
  const conditions: string[] = [
    '_type == "product"',
    'isPublished == true',
    '!(_id in path("drafts.**"))'
  ]

  // IP 系列筛选
  if (params.ipSeries && params.ipSeries.length > 0) {
    const ipSeriesConditions = params.ipSeries
      .map(id => `ipSeries._ref == "${id}"`)
      .join(' || ')
    conditions.push(`(${ipSeriesConditions})`)
  }

  // 分类筛选
  if (params.categories && params.categories.length > 0) {
    const categoryConditions = params.categories
      .map(cat => `category->slug.current == "${cat}"`)
      .join(' || ')
    conditions.push(`(${categoryConditions})`)
  }

  // 价格筛选
  if (params.minPrice !== undefined && params.minPrice > 0) {
    conditions.push(`price >= ${params.minPrice}`)
  }
  if (params.maxPrice !== undefined && params.maxPrice < 10000) {
    conditions.push(`price <= ${params.maxPrice}`)
  }

  // 库存状态筛选
  if (params.stockStatus && params.stockStatus.length > 0) {
    const stockConditions = params.stockStatus
      .map(status => `stockStatus == "${status}"`)
      .join(' || ')
    conditions.push(`(${stockConditions})`)
  }

  // 标签筛选
  if (params.tags && params.tags.length > 0) {
    const tagConditions = params.tags
      .map(tag => `"${tag}" in tags`)
      .join(' || ')
    conditions.push(`(${tagConditions})`)
  }

  // 搜索词筛选
  if (params.searchTerm) {
    const searchConditions = [
      `name.zh match "${params.searchTerm}*"`,
      `name.en match "${params.searchTerm}*"`,
      `name.ar match "${params.searchTerm}*"`,
      `shortDescription.zh match "${params.searchTerm}*"`,
      `shortDescription.en match "${params.searchTerm}*"`,
      `shortDescription.ar match "${params.searchTerm}*"`
    ].join(' || ')
    conditions.push(`(${searchConditions})`)
  }

  return conditions.join(' && ')
}

function buildSortExpression(sortBy?: string): string {
  switch (sortBy) {
    case 'oldest':
      return 'order(publishedAt asc)'
    case 'price-asc':
      return 'order(price asc)'
    case 'price-desc':
      return 'order(price desc)'
    case 'name-asc':
      return 'order(name.zh asc)'
    case 'name-desc':
      return 'order(name.zh desc)'
    case 'newest':
    default:
      return 'order(publishedAt desc)'
  }
}

async function getFilterOptions(): Promise<FilteredProductsResponse['filters']['availableFilters']> {
  // 获取所有 IP 系列及其产品数量
  const ipSeriesQuery = `
    *[_type == "ipSeries"] {
      _id,
      name,
      "productCount": count(*[_type == "product" && ipSeries._ref == ^._id && isPublished == true && !(_id in path("drafts.**"))])
    } | order(productCount desc)
  `

  // 获取所有分类及其产品数量
  const categoriesQuery = `
    *[_type == "category"] {
      _id,
      name,
      slug,
      "productCount": count(*[_type == "product" && category._ref == ^._id && isPublished == true && !(_id in path("drafts.**"))])
    } | order(productCount desc)
  `

  // 获取价格范围
  const priceRangeQuery = `
    *[_type == "product" && isPublished == true && !(_id in path("drafts.**")) && defined(price)] {
      price
    } | order(price asc)
  `

  // 获取库存状态统计
  const stockStatusQuery = `
    {
      "in-stock": count(*[_type == "product" && stockStatus == "in-stock" && isPublished == true && !(_id in path("drafts.**"))]),
      "pre-order": count(*[_type == "product" && stockStatus == "pre-order" && isPublished == true && !(_id in path("drafts.**"))]),
      "sold-out": count(*[_type == "product" && stockStatus == "sold-out" && isPublished == true && !(_id in path("drafts.**"))])
    }
  `

  // 获取所有标签及其使用次数  
  const tagsQuery = `
    array::unique(*[_type == "product" && isPublished == true && !(_id in path("drafts.**")) && defined(tags)].tags[])
  `

  try {
    const [ipSeries, categories, priceData, stockStats, tagsData] = await Promise.all([
      client.fetch(ipSeriesQuery),
      client.fetch(categoriesQuery),
      client.fetch(priceRangeQuery),
      client.fetch(stockStatusQuery),
      client.fetch(tagsQuery)
    ])

    // 计算价格范围
    const prices = priceData.map((p: any) => p.price).filter(Boolean)
    const priceRange = {
      min: prices.length > 0 ? Math.min(...prices) : 0,
      max: prices.length > 0 ? Math.max(...prices) : 10000
    }

    // 格式化库存状态
    const stockStatuses = [
      { 
        value: 'in-stock', 
        count: stockStats['in-stock'] || 0,
        label: { zh: '现货', en: 'In Stock', ar: 'متوفر' }
      },
      { 
        value: 'pre-order', 
        count: stockStats['pre-order'] || 0,
        label: { zh: '预售', en: 'Pre-order', ar: 'طلب مسبق' }
      },
      { 
        value: 'sold-out', 
        count: stockStats['sold-out'] || 0,
        label: { zh: '售罄', en: 'Sold Out', ar: 'نفد المخزون' }
      }
    ]

    // 格式化标签
    const availableTags = (tagsData || [])
      .filter((tag: string) => tag)
      .map((tag: string) => ({
        value: tag,
        count: 1, // For now, we'll just show each tag exists
        label: { zh: tag, en: tag, ar: tag }
      }))


    return {
      ipSeries: ipSeries.filter((ip: any) => ip.productCount > 0),
      categories: categories.filter((cat: any) => cat.productCount > 0),
      priceRange,
      stockStatuses,
      availableTags
    }
  } catch (error) {
    console.error('获取筛选选项失败:', error)
    console.error('Tags query was:', tagsQuery)
    if (error instanceof Error) {
      console.error('Error details:', error.message)
    }
    return {
      ipSeries: [],
      categories: [],
      priceRange: { min: 0, max: 10000 },
      stockStatuses: [
        { value: 'in-stock', count: 0, label: { zh: '现货', en: 'In Stock', ar: 'متوفر' } },
        { value: 'pre-order', count: 0, label: { zh: '预售', en: 'Pre-order', ar: 'طلب مسبق' } },
        { value: 'sold-out', count: 0, label: { zh: '售罄', en: 'Sold Out', ar: 'نفد المخزون' } }
      ],
      availableTags: []
    }
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // 解析筛选参数
    const filterParams: FilterParams = {
      ipSeries: searchParams.get('ipSeries')?.split(',').filter(Boolean) || [],
      categories: searchParams.get('categories')?.split(',').filter(Boolean) || [],
      minPrice: searchParams.get('minPrice') ? parseInt(searchParams.get('minPrice')!) : undefined,
      maxPrice: searchParams.get('maxPrice') ? parseInt(searchParams.get('maxPrice')!) : undefined,
      stockStatus: searchParams.get('stockStatus')?.split(',').filter(Boolean) || [],
      tags: searchParams.get('tags')?.split(',').filter(Boolean) || [],
      sortBy: searchParams.get('sortBy') || 'newest',
      searchTerm: searchParams.get('q') || undefined,
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '24')
    }

    // 构建查询
    const filterConditions = buildFilterQuery(filterParams)
    const sortExpression = buildSortExpression(filterParams.sortBy)
    
    // 计算分页
    const offset = ((filterParams.page || 1) - 1) * (filterParams.limit || 24)
    const limit = filterParams.limit || 24

    // 获取产品总数
    const countQuery = `count(*[${filterConditions}])`
    const totalCount = await client.fetch(countQuery)

    // 获取产品列表
    const productsQuery = `
      *[${filterConditions}] | ${sortExpression} [${offset}...${offset + limit}] {
        _id,
        name,
        slug,
        shortDescription,
        gallery[] {
          imageType,
          imageType == "upload" => {
            "uploadedImage": uploadedImage {
              asset,
              alt,
              caption
            }
          },
          imageType == "external" => {
            externalUrl,
            alt,
            caption,
            fallbackImage
          }
        },
        category->{
          name,
          slug
        },
        ipSeries->{
          name,
          slug
        },
        price,
        currency,
        stockStatus,
        tags,
        publishedAt,
        _createdAt
      }
    `

    const [products, availableFilters] = await Promise.all([
      client.fetch(productsQuery),
      getFilterOptions()
    ])

    const totalPages = Math.ceil(totalCount / limit)

    const response: FilteredProductsResponse = {
      products,
      totalCount,
      currentPage: filterParams.page || 1,
      totalPages,
      filters: {
        appliedFilters: filterParams,
        availableFilters
      }
    }

    const apiResponse = NextResponse.json(response)
    
    // Set cache headers - cache for 10 minutes on CDN, 2 minutes in browser for GET filter results
    apiResponse.headers.set('Cache-Control', 'public, s-maxage=600, max-age=120, stale-while-revalidate=86400')
    apiResponse.headers.set('Vary', 'Accept-Encoding')
    
    return apiResponse

  } catch (error) {
    console.error('产品筛选 API 错误:', error)
    
    const errorResponse = NextResponse.json(
      { 
        error: '筛选产品时出错',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
    
    // No cache for error responses
    errorResponse.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate')
    
    return errorResponse
  }
}

export async function POST(request: NextRequest) {
  try {
    const filterParams: FilterParams = await request.json()

    // 使用 POST 方法支持更复杂的筛选参数
    const filterConditions = buildFilterQuery(filterParams)
    const sortExpression = buildSortExpression(filterParams.sortBy)
    
    const offset = ((filterParams.page || 1) - 1) * (filterParams.limit || 24)
    const limit = filterParams.limit || 24

    const [totalCount, products, availableFilters] = await Promise.all([
      client.fetch(`count(*[${filterConditions}])`),
      client.fetch(`
        *[${filterConditions}] | ${sortExpression} [${offset}...${offset + limit}] {
          _id,
          name,
          slug,
          shortDescription,
          "mainImage": gallery[0] {
          imageType,
          imageType == "upload" => {
            "uploadedImage": uploadedImage {
              asset,
              alt,
              caption
            }
          },
          imageType == "external" => {
            externalUrl,
            alt,
            caption,
            fallbackImage
          }
        },
          category->{
            name,
            slug
          },
          price,
          currency,
          stockStatus,
          tags,
          publishedAt,
          _createdAt
        }
      `),
      getFilterOptions()
    ])

    const totalPages = Math.ceil(totalCount / limit)

    const response: FilteredProductsResponse = {
      products,
      totalCount,
      currentPage: filterParams.page || 1,
      totalPages,
      filters: {
        appliedFilters: filterParams,
        availableFilters
      }
    }

    const apiResponse = NextResponse.json(response)
    
    // Set cache headers for POST requests - shorter cache time (5 minutes CDN, 1 minute browser)
    apiResponse.headers.set('Cache-Control', 'public, s-maxage=300, max-age=60, stale-while-revalidate=86400')
    apiResponse.headers.set('Vary', 'Accept-Encoding')
    
    return apiResponse

  } catch (error) {
    console.error('产品筛选 API 错误:', error)
    
    const errorResponse = NextResponse.json(
      { 
        error: '筛选产品时出错',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
    
    // No cache for error responses
    errorResponse.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate')
    
    return errorResponse
  }
}