import { NextResponse } from 'next/server'
import { client } from '@/lib/sanity/client'
import { homepageWithFeaturedQuery } from '@/lib/sanity/queries'

export async function GET() {
  try {
    // 获取当前查询的数据
    const data = await client.fetch(homepageWithFeaturedQuery)
    
    // 检查所有首页相关的文档类型
    const allHomepageTypes = await client.fetch(`
      {
        "homepage": *[_type == "homepage"] {
          _id,
          _type,
          _createdAt,
          _updatedAt,
          title,
          "heroSection": heroSection {
            titleDisplayMode,
            "hasMainTitle": defined(mainTitle),
            "hasTitleImage": defined(titleImage)
          }
        },
        "homepageV2": *[_type == "homepageV2"] {
          _id,
          _type,
          _createdAt,
          _updatedAt,
          title,
          "heroSection": heroSection {
            titleDisplayMode,
            "hasMainTitle": defined(mainTitle),
            "hasTitleImage": defined(titleImage)
          }
        },
        "homepageBasic": *[_type == "homepageBasic"] {
          _id,
          _type,
          _createdAt,
          _updatedAt,
          title,
          "heroSection": heroSection {
            titleDisplayMode,
            "hasMainTitle": defined(mainTitle),
            "hasTitleImage": defined(titleImage)
          }
        },
        "allHomepageRelated": *[_type match "homepage*"] {
          _id,
          _type,
          _createdAt,
          _updatedAt
        }
      }
    `)
    
    // 检查特色产品
    const featuredProducts = data?.featuredProducts
    const featuredProductsInfo = {
      hasData: !!featuredProducts,
      displaySettings: featuredProducts?.displaySettings,
      productsCount: featuredProducts?.products?.length || 0,
      products: featuredProducts?.products?.map((p: any) => ({
        name: p.name?.zh || p.name?.en,
        price: p.price,
        currency: p.currency,
        tags: p.tags,
        hasCategory: !!p.category,
        categoryName: p.category?.name?.zh || p.category?.name?.en
      }))
    }
    
    // 分析首页配置
    const homepageAnalysis = {
      currentQueryResult: {
        hasHomepageData: !!data?.homepage,
        titleDisplayMode: data?.homepage?.heroSection?.titleDisplayMode,
        hasMainTitle: !!data?.homepage?.heroSection?.mainTitle,
        hasTitleImage: !!data?.homepage?.heroSection?.titleImage,
        lastUpdated: data?.homepage?._updatedAt
      },
      documentCounts: {
        homepage: allHomepageTypes.homepage?.length || 0,
        homepageV2: allHomepageTypes.homepageV2?.length || 0,
        homepageBasic: allHomepageTypes.homepageBasic?.length || 0,
        totalHomepageDocuments: allHomepageTypes.allHomepageRelated?.length || 0
      },
      issues: {
        multipleHomepageTypes: (allHomepageTypes.homepage?.length || 0) + (allHomepageTypes.homepageV2?.length || 0) + (allHomepageTypes.homepageBasic?.length || 0) > 1,
        noHomepageDocument: (allHomepageTypes.homepage?.length || 0) === 0,
        queryTargetMismatch: !data?.homepage && (allHomepageTypes.homepageV2?.length || 0) > 0
      },
      recommendations: []
    }
    
    // 生成建议
    if (homepageAnalysis.issues.multipleHomepageTypes) {
      homepageAnalysis.recommendations.push('发现多个首页文档类型，建议合并或删除重复配置')
    }
    if (homepageAnalysis.issues.noHomepageDocument) {
      homepageAnalysis.recommendations.push('查询目标"homepage"类型的文档不存在，检查是否应该使用"homepageV2"')
    }
    if (homepageAnalysis.issues.queryTargetMismatch) {
      homepageAnalysis.recommendations.push('当前查询目标为"homepage"但可能需要改为"homepageV2"')
    }
    
    return NextResponse.json({
      message: 'Homepage and Featured Products Debug Info',
      featuredProducts: featuredProductsInfo,
      homepage: homepageAnalysis,
      allDocuments: allHomepageTypes,
      timestamp: new Date().toISOString(),
      gitAnalysis: {
        latestHomepageCommit: '2eb78da - feat(homepage): restructure homepage schema into modular subpages',
        recommendedVersion: 'homepageV2',
        migrationStatus: '根据git记录，最新版本应该使用homepageV2，包含模块化子页面结构'
      }
    }, { status: 200 })
  } catch (error) {
    console.error('Debug API error:', error)
    return NextResponse.json({ 
      error: 'Failed to fetch debug data',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}