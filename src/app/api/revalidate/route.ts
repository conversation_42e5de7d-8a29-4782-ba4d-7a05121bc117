import { revalidatePath, revalidateTag } from 'next/cache'
import { headers } from 'next/headers'
import { NextResponse } from 'next/server'
import { createHmac, timingSafeEqual } from 'crypto'

// Validate Webhook signature using HMAC-SHA256 with timing-safe comparison
function isValidSignature(body: string, signature: string | null): boolean {
  if (!signature) return false
  
  const secret = process.env.SANITY_WEBHOOK_SECRET
  if (!secret) return false
  
  try {
    // Remove 'sha256=' prefix if present
    const cleanSignature = signature.replace('sha256=', '')
    
    // Generate HMAC
    const expectedSignature = createHmac('sha256', secret)
      .update(body, 'utf8')
      .digest('hex')
    
    // Use timing-safe comparison to prevent timing attacks
    if (cleanSignature.length !== expectedSignature.length) {
      return false
    }
    
    return timingSafeEqual(
      Buffer.from(cleanSignature, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    )
  } catch (error) {
    console.error('Signature validation error:', error)
    return false
  }
}

// Validate and safely parse JSON
function safeJsonParse(jsonString: string): any {
  try {
    return JSON.parse(jsonString)
  } catch (error) {
    throw new Error('Invalid JSON format')
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.text()
    const headersList = await headers()
    const signature = headersList.get('sanity-webhook-signature')
    
    if (!isValidSignature(body, signature)) {
      return NextResponse.json(
        { error: 'Invalid signature' }, 
        { status: 401 }
      )
    }

    let data: any
    try {
      data = safeJsonParse(body)
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid JSON format' },
        { status: 400 }
      )
    }
    
    // Validate required fields
    if (!data || typeof data !== 'object') {
      return NextResponse.json(
        { error: 'Invalid request data' },
        { status: 400 }
      )
    }
    
    // Validate and extract required fields with proper type checking
    const _type = data._type
    const slug = data.slug
    
    // Validate _type is a string and is one of the expected types
    if (typeof _type !== 'string' || !_type.trim()) {
      return NextResponse.json(
        { error: 'Invalid or missing _type field' },
        { status: 400 }
      )
    }
    
    // Validate _type is one of the allowed content types
    const allowedTypes = ['product', 'category', 'ipSeries']
    if (!allowedTypes.includes(_type)) {
      return NextResponse.json(
        { error: `Invalid _type: ${_type}. Allowed types: ${allowedTypes.join(', ')}` },
        { status: 400 }
      )
    }
    
    // Validate slug structure if provided
    if (slug !== undefined && slug !== null) {
      if (typeof slug !== 'object' || typeof slug.current !== 'string' || !slug.current.trim()) {
        return NextResponse.json(
          { error: 'Invalid slug format - expected object with current property' },
          { status: 400 }
        )
      }
    }

    // Handle different content types
    switch (_type) {
      case 'product':
        if (slug?.current) {
          // Revalidate product detail pages for all locales
          const locales = ['zh', 'en', 'ar']
          
          for (const locale of locales) {
            const paths = [
              `/${locale}/products/${slug.current}`,
              `/${locale}/products`,
            ]
            
            // For Chinese (default locale), also revalidate without prefix
            if (locale === 'zh') {
              paths.push(`/products/${slug.current}`)
              paths.push('/products')
            }
            
            for (const path of paths) {
              await revalidatePath(path)
            }
          }
          
          // Revalidate homepage
          await revalidatePath('/')
          
          // Tag-based revalidation
          await revalidateTag('products')
        }
        break
        
      case 'category':
        await revalidateTag('categories')
        await revalidatePath('/products')
        break
        
      case 'ipSeries':
        await revalidateTag('ip-series')
        break
        
      default:
        // This should not happen due to validation above, but handle gracefully
        await revalidatePath('/')
    }

    return NextResponse.json({
      revalidated: true,
      type: _type,
      slug: slug?.current,
      timestamp: new Date().toISOString()
    })
    
  } catch (error) {
    console.error('Revalidation error:', error)
    return NextResponse.json(
      { 
        error: 'Revalidation failed',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}