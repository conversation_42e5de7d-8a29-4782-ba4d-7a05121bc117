import { NextResponse } from 'next/server'
import { createClient } from '@sanity/client'

// 创建 Sanity 客户端
const stagingClient = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID!,
  dataset: 'staging',
  apiVersion: '2024-01-01',
  useCdn: false,
  token: process.env.SANITY_API_TOKEN,
})

const productionClient = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID!,
  dataset: 'production',
  apiVersion: '2024-01-01',
  useCdn: false,
  token: process.env.SANITY_API_TOKEN,
})

// 文档类型
const documentTypes = [
  'homepage',
  'aboutPage',
  'contactPage',
  'siteSettings',
  'navigation',
  'product',
  'category',
  'ipSeries'
]

export async function POST(request: Request) {
  try {
    // 验证权限
    const authHeader = request.headers.get('authorization')
    // TODO: 实现真实的权限验证
    
    // 验证 token
    if (!process.env.SANITY_API_TOKEN) {
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      )
    }
    
    let successCount = 0
    let errorCount = 0
    const errors: string[] = []
    
    // 处理每种文档类型
    for (const docType of documentTypes) {
      const query = `*[_type == "${docType}"]`
      
      try {
        const [stagingDocs, productionDocs] = await Promise.all([
          stagingClient.fetch(query),
          productionClient.fetch(query)
        ])
        
        // 创建生产环境文档映射
        const prodDocsMap = new Map(
          productionDocs.map((doc: any) => [doc._id, doc])
        )
        
        // 处理每个文档
        for (const stagingDoc of stagingDocs) {
          try {
            const prodDoc = prodDocsMap.get(stagingDoc._id)
            
            if (!prodDoc) {
              // 创建新文档
              await productionClient.create(stagingDoc)
              successCount++
            } else {
              // 更新现有文档
              const { _id, _rev, _createdAt, _updatedAt, ...updateData } = stagingDoc
              
              // 检查是否真的有更新
              const prodCopy = { ...prodDoc } as any
              delete prodCopy._rev
              delete prodCopy._createdAt
              delete prodCopy._updatedAt
              
              if (JSON.stringify(updateData) !== JSON.stringify(prodCopy)) {
                await productionClient
                  .patch(_id)
                  .set(updateData)
                  .commit()
                successCount++
              }
            }
          } catch (docError: any) {
            errorCount++
            errors.push(`Failed to process ${docType} - ${stagingDoc._id}: ${docError.message}`)
            console.error('Document error:', docError)
          }
        }
      } catch (typeError: any) {
        console.error(`Error processing ${docType}:`, typeError)
        errors.push(`Failed to process ${docType}: ${typeError.message}`)
      }
    }
    
    // 触发生产环境重新验证
    if (successCount > 0) {
      try {
        const revalidateUrl = new URL('/api/revalidate', request.url)
        await fetch(revalidateUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-sanity-webhook-signature': 'internal-promote'
          },
          body: JSON.stringify({ _type: 'all' })
        })
      } catch (revalidateError) {
        console.error('Revalidation error:', revalidateError)
      }
    }
    
    return NextResponse.json({
      successCount,
      errorCount,
      errors: errors.slice(0, 10), // 只返回前10个错误
      message: errorCount > 0 
        ? `推送完成，但有 ${errorCount} 个错误`
        : '所有内容已成功推送到生产环境'
    })
  } catch (error) {
    console.error('Promotion error:', error)
    return NextResponse.json(
      { error: 'Failed to promote changes' },
      { status: 500 }
    )
  }
}