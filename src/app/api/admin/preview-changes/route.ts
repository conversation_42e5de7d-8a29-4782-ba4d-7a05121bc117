import { NextResponse } from 'next/server'
import { createClient } from '@sanity/client'

// 创建 Sanity 客户端
const stagingClient = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID!,
  dataset: 'staging',
  apiVersion: '2024-01-01',
  useCdn: false,
  token: process.env.SANITY_API_TOKEN,
})

const productionClient = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID!,
  dataset: 'production',
  apiVersion: '2024-01-01',
  useCdn: false,
  token: process.env.SANITY_API_TOKEN,
})

// 文档类型
const documentTypes = [
  'homepage',
  'aboutPage',
  'contactPage',
  'siteSettings',
  'navigation',
  'product',
  'category',
  'ipSeries'
]

export async function POST(request: Request) {
  try {
    // 验证权限（简单示例，实际应该使用更完善的认证）
    const authHeader = request.headers.get('authorization')
    // TODO: 实现真实的权限验证
    
    const changes = []
    
    // 对比每种文档类型
    for (const docType of documentTypes) {
      const query = `*[_type == "${docType}"]`
      
      const [stagingDocs, productionDocs] = await Promise.all([
        stagingClient.fetch(query),
        productionClient.fetch(query)
      ])
      
      // 创建生产环境文档映射
      const prodDocsMap = new Map(
        productionDocs.map((doc: any) => [doc._id, doc])
      )
      
      // 比较文档
      for (const stagingDoc of stagingDocs) {
        const prodDoc = prodDocsMap.get(stagingDoc._id)
        
        if (!prodDoc) {
          // 新文档
          changes.push({
            type: docType,
            id: stagingDoc._id,
            action: 'new'
          })
        } else {
          // 检查是否有更新（忽略系统字段）
          const stagingCopy = { ...stagingDoc } as any
          const prodCopy = { ...prodDoc } as any
          
          // 删除系统字段
          const systemFields = ['_rev', '_updatedAt', '_createdAt']
          systemFields.forEach(field => {
            delete stagingCopy[field]
            delete prodCopy[field]
          })
          
          if (JSON.stringify(stagingCopy) !== JSON.stringify(prodCopy)) {
            changes.push({
              type: docType,
              id: stagingDoc._id,
              action: 'updated'
            })
          }
        }
      }
    }
    
    return NextResponse.json({ changes })
  } catch (error) {
    console.error('Preview changes error:', error)
    return NextResponse.json(
      { error: 'Failed to preview changes' },
      { status: 500 }
    )
  }
}