import { NextRequest, NextResponse } from 'next/server'
import { client } from '@/lib/sanity/client'
import { resolveImageUrl } from '@/lib/sanity/image'

interface SearchResult {
  id: string
  title: string
  type: 'product' | 'category' | 'page'
  url: string
  image?: string
  description?: string
  category?: string
  price?: string
}

// 增强的搜索查询，支持产品、分类和多语言
const searchProductsQuery = `
  *[_type == "product" && (
    name.zh match $searchTerm + "*" ||
    name.en match $searchTerm + "*" ||
    name.ar match $searchTerm + "*" ||
    shortDescription.zh match $searchTerm + "*" ||
    shortDescription.en match $searchTerm + "*" ||
    shortDescription.ar match $searchTerm + "*" ||
    tags[]->name.zh match $searchTerm + "*" ||
    tags[]->name.en match $searchTerm + "*" ||
    category->name.zh match $searchTerm + "*" ||
    category->name.en match $searchTerm + "*"
  ) && isPublished == true] | order(_score desc, publishedAt desc)[0...10] {
    _id,
    name,
    slug,
    shortDescription,
    gallery[] {
      imageType,
      imageType == "upload" => {
        "uploadedImage": uploadedImage {
          asset,
          alt,
          caption
        }
      },
      imageType == "external" => {
        externalUrl,
        alt,
        caption,
        fallbackImage
      }
    },
    price,
    currency,
    stockStatus,
    category->{
      name,
      slug
    },
    publishedAt
  }
`

const searchCategoriesQuery = `
  *[_type == "category" && (
    name.zh match $searchTerm + "*" ||
    name.en match $searchTerm + "*" ||
    name.ar match $searchTerm + "*" ||
    description.zh match $searchTerm + "*" ||
    description.en match $searchTerm + "*"
  )] | order(name.zh asc)[0...5] {
    _id,
    name,
    slug,
    description,
    image
  }
`

function formatSearchResults(
  products: any[],
  categories: any[],
  locale: string = 'zh'
): SearchResult[] {
  const results: SearchResult[] = []

  // 格式化产品结果
  products.forEach(product => {
    const title = product.name?.[locale] || product.name?.zh || product.name?.en
    const description = product.shortDescription?.[locale] || product.shortDescription?.zh || product.shortDescription?.en
    const categoryName = product.category?.name?.[locale] || product.category?.name?.zh

    if (title) {
      results.push({
        id: product._id,
        title,
        type: 'product',
        url: `/${locale}/products/${product.slug?.current}`,
        image: resolveImageUrl(product.gallery?.[0]),
        description: description,
        category: categoryName,
        price: product.price ? `${product.currency || '¥'}${product.price}` : undefined
      })
    }
  })

  // 格式化分类结果
  categories.forEach(category => {
    const title = category.name?.[locale] || category.name?.zh || category.name?.en
    const description = category.description?.[locale] || category.description?.zh || category.description?.en

    if (title) {
      results.push({
        id: category._id,
        title,
        type: 'category',
        url: `/${locale}/products?category=${category.slug?.current}`,
        description: description,
        image: resolveImageUrl(category.image)
      })
    }
  })

  return results
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q')
    const locale = searchParams.get('locale') || 'zh'

    if (!query || query.trim().length < 2) {
      const response = NextResponse.json(
        { 
          error: 'Search query must be at least 2 characters',
          results: [] 
        },
        { status: 400 }
      )
      
      // No cache for error responses
      response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate')
      
      return response
    }

    const searchTerm = query.trim()

    // 并行执行搜索查询
    const [products, categories] = await Promise.all([
      client.fetch(searchProductsQuery, { searchTerm }),
      client.fetch(searchCategoriesQuery, { searchTerm })
    ])

    // 格式化搜索结果
    const results = formatSearchResults(products, categories, locale)

    // 添加静态页面搜索结果（如需要）
    const pageResults = getStaticPageResults(searchTerm, locale)
    results.push(...pageResults)

    const response = NextResponse.json({
      query: searchTerm,
      results,
      total: results.length,
      locale
    })

    // Set cache headers - cache for 5 minutes on CDN, 1 minute in browser
    response.headers.set('Cache-Control', 'public, s-maxage=300, max-age=60, stale-while-revalidate=86400')
    response.headers.set('Vary', 'Accept-Encoding')
    
    return response

  } catch (error) {
    console.error('Search API error:', error)
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        results: []
      },
      { status: 500 }
    )
  }
}

// 静态页面搜索结果
function getStaticPageResults(searchTerm: string, locale: string): SearchResult[] {
  const pages = [
    {
      id: 'about',
      titles: {
        zh: '关于我们',
        en: 'About Us',
        ar: 'من نحن'
      },
      descriptions: {
        zh: '了解我们的品牌故事',
        en: 'Learn about our brand story',
        ar: 'تعرف على قصة علامتنا التجارية'
      },
      url: 'about'
    },
    {
      id: 'contact',
      titles: {
        zh: '联系我们',
        en: 'Contact Us',
        ar: 'اتصل بنا'
      },
      descriptions: {
        zh: '联系我们获取更多信息',
        en: 'Contact us for more information',
        ar: 'اتصل بنا للحصول على مزيد من المعلومات'
      },
      url: 'contact'
    }
  ]

  return pages
    .filter(page => {
      const title = page.titles[locale as keyof typeof page.titles]
      const description = page.descriptions[locale as keyof typeof page.descriptions]
      return title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
             description?.toLowerCase().includes(searchTerm.toLowerCase())
    })
    .map(page => ({
      id: page.id,
      title: page.titles[locale as keyof typeof page.titles],
      type: 'page' as const,
      url: `/${locale}/${page.url}`,
      description: page.descriptions[locale as keyof typeof page.descriptions]
    }))
}