import { GET } from '../route'
import { NextRequest } from 'next/server'
import { client } from '@/lib/sanity/client'

// Mock Sanity client
jest.mock('@/lib/sanity/client', () => ({
  client: {
    fetch: jest.fn(),
  },
}))

describe('/api/search', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('成功返回搜索结果', async () => {
    const mockProducts = [
      {
        _id: '1',
        name: { zh: '测试产品', en: 'Test Product' },
        slug: { current: 'test-product' },
        shortDescription: { zh: '测试描述', en: 'Test description' },
        price: 100,
        currency: '¥',
        category: {
          name: { zh: '测试分类', en: 'Test Category' },
          slug: { current: 'test-category' }
        }
      }
    ]

    const mockCategories = [
      {
        _id: '2',
        name: { zh: '分类测试', en: 'Category Test' },
        slug: { current: 'category-test' },
        description: { zh: '分类描述', en: 'Category description' }
      }
    ]

    ;(client.fetch as jest.Mock)
      .mockResolvedValueOnce(mockProducts)
      .mockResolvedValueOnce(mockCategories)

    const request = new NextRequest('http://localhost:3000/api/search?q=测试&locale=zh')
    const response = await GET(request)
    const data = await response.json()

    expect(response.status).toBe(200)
    expect(data.query).toBe('测试')
    expect(data.results).toHaveLength(2)
    expect(data.results[0].title).toBe('测试产品')
    expect(data.results[0].type).toBe('product')
    expect(data.results[1].title).toBe('分类测试')
    expect(data.results[1].type).toBe('category')
  })

  it('处理搜索查询太短的情况', async () => {
    const request = new NextRequest('http://localhost:3000/api/search?q=a&locale=zh')
    const response = await GET(request)
    const data = await response.json()

    expect(response.status).toBe(400)
    expect(data.error).toBe('Search query must be at least 2 characters')
  })

  it('处理缺少查询参数的情况', async () => {
    const request = new NextRequest('http://localhost:3000/api/search?locale=zh')
    const response = await GET(request)
    const data = await response.json()

    expect(response.status).toBe(400)
    expect(data.error).toBe('Search query must be at least 2 characters')
  })

  it('处理 Sanity 客户端错误', async () => {
    ;(client.fetch as jest.Mock).mockRejectedValue(new Error('Sanity error'))

    const request = new NextRequest('http://localhost:3000/api/search?q=测试&locale=zh')
    const response = await GET(request)
    const data = await response.json()

    expect(response.status).toBe(500)
    expect(data.error).toBe('Internal server error')
  })

  it('支持不同语言环境', async () => {
    const mockProducts = [
      {
        _id: '1',
        name: { zh: '测试产品', en: 'Test Product', ar: 'منتج اختبار' },
        slug: { current: 'test-product' },
        shortDescription: { zh: '测试描述', en: 'Test description', ar: 'وصف اختبار' },
        price: 100,
        currency: '$'
      }
    ]

    ;(client.fetch as jest.Mock)
      .mockResolvedValueOnce(mockProducts)
      .mockResolvedValueOnce([])

    // 测试英文
    const enRequest = new NextRequest('http://localhost:3000/api/search?q=test&locale=en')
    const enResponse = await GET(enRequest)
    const enData = await enResponse.json()

    expect(enData.results[0].title).toBe('Test Product')
    expect(enData.results[0].url).toBe('/en/products/test-product')

    // 测试阿拉伯语
    ;(client.fetch as jest.Mock)
      .mockResolvedValueOnce(mockProducts)
      .mockResolvedValueOnce([])

    const arRequest = new NextRequest('http://localhost:3000/api/search?q=اختبار&locale=ar')
    const arResponse = await GET(arRequest)
    const arData = await arResponse.json()

    expect(arData.results[0].title).toBe('منتج اختبار')
    expect(arData.results[0].url).toBe('/ar/products/test-product')
  })

  it('包含静态页面搜索结果', async () => {
    ;(client.fetch as jest.Mock)
      .mockResolvedValueOnce([])
      .mockResolvedValueOnce([])

    const request = new NextRequest('http://localhost:3000/api/search?q=关于&locale=zh')
    const response = await GET(request)
    const data = await response.json()

    expect(data.results).toContainEqual(
      expect.objectContaining({
        id: 'about',
        title: '关于我们',
        type: 'page',
        url: '/zh/about'
      })
    )
  })
})