// Sanity schema types

export interface SanityImage {
  _type: 'image'
  asset: {
    _ref: string
    _type: 'reference'
  }
  crop?: {
    top: number
    bottom: number
    left: number
    right: number
  }
  hotspot?: {
    x: number
    y: number
    height: number
    width: number
  }
}

// FlexibleImage type definitions to match the library
interface FlexibleImageExternal {
  _type?: 'flexibleImage'
  _key?: string
  imageType: 'external'
  externalUrl: string
  alt?: string
  caption?: string
  fallbackImage?: SanityImage
}

interface FlexibleImageUpload {
  _type?: 'flexibleImage'
  _key?: string
  imageType: 'upload'
  uploadedImage: SanityImage
  alt?: string
  caption?: string
  fallbackImage?: SanityImage
}

export type FlexibleImage = FlexibleImageExternal | FlexibleImageUpload

export interface LocaleString {
  zh?: string
  en?: string
  ar?: string
}

export interface LocaleText {
  zh?: string
  en?: string
  ar?: string
}

export interface LocaleBlockContent {
  zh?: any[] // Rich text blocks
  en?: any[] // Rich text blocks
  ar?: any[] // Rich text blocks
}

export interface Category {
  _id: string
  name: LocaleString
  slug: {
    current: string
  }
  description?: LocaleText
  image?: FlexibleImage
}

export interface IPSeries {
  _id: string
  name: LocaleString
  slug: {
    current: string
  }
  description?: LocaleText
  logo?: FlexibleImage
  bannerImage?: FlexibleImage
}

export interface Product {
  _id: string
  name: LocaleString
  slug: {
    current: string
  }
  shortDescription?: LocaleText
  description?: LocaleBlockContent
  gallery: FlexibleImage[]  // Now required, first image is the main image
  category: Category
  ipSeries?: IPSeries
  tags?: string[]
  price?: number
  currency?: 'CNY' | 'USD' | 'AED'
  stockStatus: 'in-stock' | 'pre-order' | 'sold-out'
  isPublished: boolean
  publishedAt?: string
  seo?: {
    title?: LocaleString
    description?: LocaleText
    keywords?: string[]
  }
  // mainImage is now computed from gallery[0] in queries
}

export interface LocaleBlockContent {
  zh?: any[]
  en?: any[]
  ar?: any[]
}

export interface SEO {
  title?: LocaleString
  description?: LocaleText
  keywords?: string[]
  ogImage?: FlexibleImage
}

export interface TeamMember {
  name: LocaleString
  position?: LocaleString
  bio?: LocaleText
  photo?: FlexibleImage
}

export interface TextSection {
  _type: 'textSection'
  sectionTitle?: LocaleString
  content?: LocaleBlockContent
}

export interface ImageTextSection {
  _type: 'imageTextSection'
  sectionTitle?: LocaleString
  content?: LocaleBlockContent
  image?: FlexibleImage
  imagePosition?: 'left' | 'right'
}

export interface TeamSection {
  _type: 'teamSection'
  sectionTitle?: LocaleString
  teamMembers?: TeamMember[]
}

export type AboutPageSection = TextSection | ImageTextSection | TeamSection

export interface AboutPage {
  _id: string
  title: LocaleString
  heroSection?: {
    headline?: LocaleBlockContent | LocaleString // Support both formats during migration
    subtitle?: LocaleBlockContent | LocaleText // Support both formats during migration
    heroImage?: FlexibleImage
  }
  sections?: AboutPageSection[]
  seo?: SEO
  _updatedAt?: string
}

export interface SocialLink {
  platform: 'wechat' | 'weibo' | 'qq' | 'facebook' | 'twitter' | 'instagram' | 'linkedin' | 'youtube' | 'other'
  label?: LocaleString
  url: string
  icon?: FlexibleImage
}

export interface ContactPage {
  _id: string
  title: LocaleString
  heroSection?: {
    headline?: LocaleString
    subtitle?: LocaleText
  }
  contactInfo?: {
    sectionTitle?: LocaleString
    address?: {
      label?: LocaleString
      value?: LocaleText
    }
    phone?: {
      label?: LocaleString
      value?: string
    }
    email?: {
      label?: LocaleString
      value?: string
    }
    workingHours?: {
      label?: LocaleString
      value?: LocaleText
    }
  }
  contactForm?: {
    formTitle?: LocaleString
    formDescription?: LocaleText
    nameLabel?: LocaleString
    emailLabel?: LocaleString
    subjectLabel?: LocaleString
    messageLabel?: LocaleString
    submitButtonText?: LocaleString
    successMessage?: LocaleString
    errorMessage?: LocaleString
  }
  mapSection?: {
    showMap?: boolean
    mapTitle?: LocaleString
    latitude?: number
    longitude?: number
    mapDescription?: LocaleText
  }
  socialLinks?: SocialLink[]
  seo?: SEO
  _updatedAt?: string
}

export interface FeatureItem {
  icon: string
  title: LocaleString
  description: LocaleText
  linkUrl?: string
  isExternal?: boolean
  order?: number
}

export interface StatItem {
  number: string
  label: LocaleString
  color?: 'blue' | 'green' | 'red' | 'purple' | 'orange'
}

export interface Homepage {
  _id: string
  title: LocaleString
  heroSection?: {
    titleDisplayMode?: 'text' | 'image'
    mainTitle?: LocaleString
    titleImage?: FlexibleImage
    titleGradient?: {
      usePreset?: boolean
      preset?: 'blue-purple' | 'orange-red' | 'green-cyan' | 'pink-purple' | 'rainbow'
      fromColor?: { hex?: string }
      viaColor?: { hex?: string }
      toColor?: { hex?: string }
      direction?: 'to right' | 'to left' | 'to bottom' | 'to top' | 'to bottom right' | 'to bottom left'
    }
    subtitle?: LocaleString
    description?: LocaleText
    ctaButton?: {
      text?: LocaleString
      url?: string
    }
  }
  featuresSection?: {
    showSection?: boolean
    sectionTitle?: LocaleString
    sectionDescription?: LocaleText
    layout?: 'grid-3' | 'grid-4' | 'horizontal' | 'vertical'
    features?: FeatureItem[]
  }
  brandStorySection?: {
    title?: LocaleString
    description?: LocaleBlockContent
    backgroundImage?: FlexibleImage
    showProducts?: boolean
    products?: FeaturedProduct[]
    productSettings?: {
      showPrices?: boolean
      showCategories?: boolean
      showBadges?: boolean
      layout?: 'grid' | 'carousel'
    }
  }
  statsSection?: {
    showStats?: boolean
    stats?: StatItem[]
  }
  seo?: SEO
  _updatedAt?: string
}

export interface SiteSettings {
  _id: string
  siteTitle: LocaleString
  siteDescription: LocaleText
  logo?: FlexibleImage
  favicon?: FlexibleImage
  contactInfo?: {
    email?: string
    phone?: string
    address?: LocaleText
  }
  socialMedia?: {
    platform: string
    url: string
    label?: LocaleString
  }[]
  analytics?: {
    googleAnalyticsId?: string
    baiduAnalyticsId?: string
    otherAnalytics?: {
      name?: string
      trackingId?: string
      scriptCode?: string
    }[]
  }
  appearance?: {
    primaryColor?: string | { hex: string }
    secondaryColor?: string | { hex: string }
    fontFamily?: string
  }
  features?: {
    enableSearch?: boolean
    enableNewsletter?: boolean
    enableComments?: boolean
    enableLiveChat?: boolean
    maintenanceMode?: boolean
    maintenanceMessage?: LocaleText
  }
  seo?: SEO
  _updatedAt?: string
}

export interface MenuItem {
  title: LocaleString
  slug: string
  external?: boolean
  openInNewTab?: boolean
  icon?: string
  order?: number
  isActive?: boolean
  submenu?: {
    title: LocaleString
    slug: string
    external?: boolean
    openInNewTab?: boolean
    description?: LocaleText
    icon?: string
    isActive?: boolean
  }[]
}

export interface Navigation {
  _id: string
  title: LocaleString
  identifier: string
  menuItems?: MenuItem[]
  settings?: {
    sticky?: boolean
    showSearch?: boolean
    showLanguageSwitch?: boolean
    showCart?: boolean
    backgroundColor?: any
    textColor?: any
    hoverColor?: any
  }
  _updatedAt?: string
}

export interface FeaturedProduct extends Product {
  rating: number
}

export interface FeaturedProductsConfig {
  _id: string
  title: LocaleString
  subtitle?: LocaleText
  products: FeaturedProduct[]
  displaySettings?: {
    showPrices?: boolean
    showRatings?: boolean
    showCategories?: boolean
    showBadges?: boolean
  }
  layout?: {
    columnsDesktop?: number
    columnsTablet?: number
    columnsMobile?: number
  }
  isActive: boolean
  _updatedAt?: string
}

// 新的模块化数据接口
export interface HomepageModularData {
  homepage?: Homepage
  featuredProducts?: HomepageFeaturedProducts
  features?: HomepageFeatures
  brandStory?: HomepageBrandStory
  stats?: HomepageStats
  seo?: HomepageSeo
}

// 模块化组件接口
export interface HomepageFeaturedProducts {
  _id?: string
  showSection?: boolean
  sectionTitle?: LocaleString
  sectionSubtitle?: LocaleString
  products?: Product[]
  displaySettings?: any
  layoutSettings?: any
}

export interface HomepageFeatures {
  _id?: string
  showSection?: boolean
  sectionTitle?: LocaleString
  features?: FeatureItem[]
}

export interface HomepageBrandStory {
  _id?: string
  showSection?: boolean
  title?: LocaleString
  subtitle?: LocaleString
  description?: LocaleBlockContent
  layout?: string
  media?: any
  backgroundImage?: FlexibleImage
  backgroundStyle?: any
  timeline?: any
  showProducts?: boolean
  products?: Product[]
  productSettings?: any
  ctaButton?: any
}

export interface HomepageStats {
  _id?: string
  showStats?: boolean
  sectionTitle?: LocaleString
  stats?: Array<{
    icon?: string
    number?: number
    label?: LocaleString
    suffix?: string
    color?: string
  }>
}

export interface HomepageSeo {
  _id?: string
  title?: LocaleString
  description?: LocaleText
  keywords?: string[]
  ogImage?: FlexibleImage
}

// 保持向后兼容
export interface HomepageWithFeatured extends HomepageModularData {
  // 为了向后兼容，保留原有接口但扩展为模块化数据
}