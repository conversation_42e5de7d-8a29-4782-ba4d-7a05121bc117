/**
 * Application Constants
 * Centralized configuration values to replace magic numbers throughout the codebase
 */

// Timeout values (in milliseconds)
export const TIMEOUTS = {
  CMS_FETCH: 5000,
  IMAGE_LOAD: 10000,
  API_REQUEST: 8000,
} as const

// Image quality settings
export const IMAGE_QUALITY = {
  DEFAULT: 85,
  HIGH: 95,
  LOW: 60,
} as const

// Performance thresholds
export const PERFORMANCE = {
  SEARCH_RESPONSE_MAX: 250, // milliseconds
  EMBEDDING_MAX_PER_1K_CHARS: 45, // milliseconds
  PAGE_INDEX_MAX: 30000, // milliseconds
  MEMORY_PEAK_MAX: 120, // MB
} as const

// UI dimensions
export const UI_DIMENSIONS = {
  POPUP_WIDTH: 400,
  POPUP_HEIGHT: 600,
  MOBILE_BREAKPOINT: 768,
} as const

// Error retry settings
export const RETRY_CONFIG = {
  MAX_ATTEMPTS: 3,
  BACKOFF_BASE: 1000, // milliseconds
  BACKOFF_MULTIPLIER: 2,
} as const