import { useEffect, useRef, useState, useCallback } from 'react';

interface UseIntersectionObserverOptions {
  threshold?: number | number[];
  root?: Element | null;
  rootMargin?: string;
  triggerOnce?: boolean;
  enabled?: boolean;
}

interface UseIntersectionObserverReturn<T extends HTMLElement = HTMLElement> {
  ref: React.RefObject<T>;
  inView: boolean;
  entry?: IntersectionObserverEntry;
}

/**
 * Custom hook for detecting when an element is in the viewport
 * Supports preloading by adjusting rootMargin
 * 
 * @param options - Intersection Observer options
 * @returns Object containing ref, inView state, and entry
 */
export function useIntersectionObserver<T extends HTMLElement = HTMLElement>({
  threshold = 0,
  root = null,
  rootMargin = '50px', // Preload 50px before entering viewport
  triggerOnce = true,
  enabled = true,
}: UseIntersectionObserverOptions = {}): UseIntersectionObserverReturn<T> {
  const [entry, setEntry] = useState<IntersectionObserverEntry>();
  const [inView, setInView] = useState(false);
  const elementRef = useRef<T>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const unobserveRef = useRef<(() => void) | null>(null);

  const handleIntersection = useCallback(
    (entries: IntersectionObserverEntry[]) => {
      const [entry] = entries;
      setEntry(entry);
      const isInView = entry?.isIntersecting ?? false;
      
      setInView(isInView);

      // If triggerOnce is true and element is in view, disconnect observer
      if (isInView && triggerOnce && unobserveRef.current) {
        unobserveRef.current();
        unobserveRef.current = null;
      }
    },
    [triggerOnce]
  );

  useEffect(() => {
    // Skip if not enabled or no element ref
    if (!enabled || !elementRef.current) {
      return;
    }

    // Check if IntersectionObserver is supported
    if (!window.IntersectionObserver) {
      console.warn('IntersectionObserver is not supported in this browser');
      setInView(true); // Fallback to always visible
      return;
    }

    const element = elementRef.current;

    // Create observer
    observerRef.current = new IntersectionObserver(handleIntersection, {
      threshold,
      root,
      rootMargin,
    });

    // Start observing
    observerRef.current.observe(element);

    // Create unobserve function
    unobserveRef.current = () => {
      if (observerRef.current && element) {
        observerRef.current.unobserve(element);
        observerRef.current.disconnect();
        observerRef.current = null;
      }
    };

    // Cleanup
    return () => {
      if (unobserveRef.current) {
        unobserveRef.current();
      }
    };
  }, [enabled, handleIntersection, root, rootMargin, threshold]);

  return {
    ref: elementRef,
    inView,
    entry,
  };
}

/**
 * Hook for observing multiple elements with a single observer
 * More efficient for lists with many items
 */
export function useIntersectionObserverBatch<T extends HTMLElement = HTMLElement>({
  threshold = 0,
  root = null,
  rootMargin = '50px',
  enabled = true,
}: UseIntersectionObserverOptions = {}) {
  const [visibleElements, setVisibleElements] = useState<Set<T>>(new Set());
  const observerRef = useRef<IntersectionObserver | null>(null);
  const elementsRef = useRef<Map<T, boolean>>(new Map());

  const observe = useCallback((element: T) => {
    if (!enabled || !element || !observerRef.current) return;
    
    elementsRef.current.set(element, false);
    observerRef.current.observe(element);
  }, [enabled]);

  const unobserve = useCallback((element: T) => {
    if (!element || !observerRef.current) return;
    
    elementsRef.current.delete(element);
    observerRef.current.unobserve(element);
    setVisibleElements(prev => {
      const next = new Set(prev);
      next.delete(element);
      return next;
    });
  }, []);

  useEffect(() => {
    if (!enabled) return;

    const handleIntersection = (entries: IntersectionObserverEntry[]) => {
      const updates = new Map<T, boolean>();
      
      entries.forEach(entry => {
        const element = entry.target as T;
        const wasVisible = elementsRef.current.get(element) ?? false;
        const isVisible = entry.isIntersecting;
        
        if (wasVisible !== isVisible) {
          elementsRef.current.set(element, isVisible);
          updates.set(element, isVisible);
        }
      });

      if (updates.size > 0) {
        setVisibleElements(prev => {
          const next = new Set(prev);
          updates.forEach((isVisible, element) => {
            if (isVisible) {
              next.add(element);
            } else {
              next.delete(element);
            }
          });
          return next;
        });
      }
    };

    observerRef.current = new IntersectionObserver(handleIntersection, {
      threshold,
      root,
      rootMargin,
    });

    // Re-observe all existing elements
    elementsRef.current.forEach((_, element) => {
      observerRef.current?.observe(element);
    });

    return () => {
      observerRef.current?.disconnect();
      observerRef.current = null;
    };
  }, [enabled, root, rootMargin, threshold]);

  return {
    observe,
    unobserve,
    visibleElements,
    isVisible: (element: T) => visibleElements.has(element),
  };
}