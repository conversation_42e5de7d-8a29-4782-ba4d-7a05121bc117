'use client'

import { useTranslations as useNextIntlTranslations } from 'next-intl'
import React, { useEffect, useState } from 'react'

// 默认翻译文本
const fallbackTranslations: Record<string, Record<string, string>> = {
  Error: {
    title: 'Something went wrong!',
    description: 'An unexpected error occurred. Please try again.',
    retry: 'Try again',
    goHome: 'Go back home'
  },
  // 可以添加其他命名空间的默认翻译
}

/**
 * 安全的翻译 hook，在 context 不可用时提供降级方案
 */
export function useSafeTranslations(namespace: string) {
  let t: (key: string) => string
  let isReady = false
  
  try {
    // 尝试使用 next-intl 的 hook - 始终调用，不在条件语句中
    const intlT = useNextIntlTranslations(namespace)
    t = intlT
    isReady = true
  } catch (error) {
    // 如果 context 不可用，使用降级方案
    t = (key: string) => {
      const namespaceTranslations = fallbackTranslations[namespace] || {}
      return namespaceTranslations[key] || key
    }
    isReady = false
  }
  
  return { t, isReady }
}

/**
 * HOC: 为组件提供安全的国际化支持
 */
export function withSafeTranslations<P extends object>(
  Component: React.ComponentType<P>,
  namespace: string
) {
  return function SafeTranslatedComponent(props: P) {
    const { t, isReady } = useSafeTranslations(namespace)
    
    return <Component {...props} t={t} translationsReady={isReady} />
  }
}