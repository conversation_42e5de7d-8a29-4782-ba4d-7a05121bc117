'use client'

import React, { useState, useEffect } from 'react'
import dynamic from 'next/dynamic'

// Load Framer Motion conditionally
const loadFramerMotion = async () => {
  try {
    const framerMotion = await import('framer-motion')
    return framerMotion
  } catch (error) {
    console.warn('Failed to load framer-motion:', error)
    return null
  }
}

/**
 * Hook to conditionally load and use Framer Motion based on user preferences
 * and device capabilities
 */
export function useConditionalMotion() {
  const [motionLibrary, setMotionLibrary] = useState<any>(null)
  const [shouldUseMotion, setShouldUseMotion] = useState(false)
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    // Check if animations should be used
    const checkMotionPreference = () => {
      // Check prefers-reduced-motion
      const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches
      
      // Check device capabilities
      const deviceMemory = (navigator as any).deviceMemory || 4
      const hardwareConcurrency = navigator.hardwareConcurrency || 4
      const isLowPowerDevice = deviceMemory < 4 || hardwareConcurrency < 4
      
      return !prefersReducedMotion && !isLowPowerDevice
    }

    const shouldLoad = checkMotionPreference()
    setShouldUseMotion(shouldLoad)

    if (shouldLoad) {
      // Load Framer Motion dynamically
      loadFramerMotion().then((framer) => {
        if (framer) {
          setMotionLibrary(framer)
        }
        setIsLoaded(true)
      })
    } else {
      // Set loaded to true without loading the library
      setIsLoaded(true)
    }
  }, [])

  // Create wrapper components that filter out motion props when Framer Motion is not loaded
  const createFallbackComponent = (tag: string) => {
    const Component = React.forwardRef<any, any>((props, ref) => {
      // Filter out motion-specific props
      const {
        initial,
        animate,
        exit,
        transition,
        variants,
        whileHover,
        whileTap,
        whileDrag,
        whileFocus,
        whileInView,
        drag,
        dragConstraints,
        dragElastic,
        dragMomentum,
        dragTransition,
        layout,
        layoutId,
        style,
        ...domProps
      } = props

      // Create the element with only DOM-safe props
      return React.createElement(tag, { ...domProps, ref, style })
    })
    
    Component.displayName = `MotionFallback${tag.charAt(0).toUpperCase() + tag.slice(1)}`
    return Component
  }

  // Return motion components or fallbacks
  const motion = motionLibrary ? motionLibrary.motion : {
    div: createFallbackComponent('div'),
    section: createFallbackComponent('section'),
    h1: createFallbackComponent('h1'),
    h2: createFallbackComponent('h2'),
    p: createFallbackComponent('p'),
    button: createFallbackComponent('button'),
  }

  const AnimatePresence = motionLibrary?.AnimatePresence || (({ children }: { children: React.ReactNode }) => children)
  
  // Manage reduced motion state directly in this hook to avoid conditional hook calls
  const [reducedMotionState, setReducedMotionState] = useState(!shouldUseMotion)
  
  useEffect(() => {
    // Update reduced motion state based on our own preference detection
    setReducedMotionState(!shouldUseMotion)
  }, [shouldUseMotion])

  return {
    motion,
    AnimatePresence,
    reducedMotion: reducedMotionState,
    shouldUseMotion,
    isLoaded,
  }
}