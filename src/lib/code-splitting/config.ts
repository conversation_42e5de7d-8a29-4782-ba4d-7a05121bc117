/**
 * 代码分割策略配置
 * 定义哪些组件应该被动态加载，以及加载策略
 */

export interface SplitConfig {
  // 是否应该分割
  shouldSplit: boolean
  // 加载优先级
  priority: 'high' | 'medium' | 'low'
  // 预加载策略
  preload?: 'hover' | 'visible' | 'none'
  // 是否需要 SSR
  ssr?: boolean
}

// 组件分割策略映射
export const CODE_SPLIT_CONFIG: Record<string, SplitConfig> = {
  // ❌ 永远不分割的组件
  'error-boundary': { shouldSplit: false, priority: 'high' },
  'layout': { shouldSplit: false, priority: 'high' },
  'header': { shouldSplit: false, priority: 'high' },
  'footer': { shouldSplit: false, priority: 'high' },
  
  // ✅ 页面级别分割
  'products-page': { shouldSplit: true, priority: 'high', ssr: true },
  'about-page': { shouldSplit: true, priority: 'medium', ssr: true },
  'contact-page': { shouldSplit: true, priority: 'low', ssr: true },
  
  // ✅ 大型功能组件分割
  'product-filter': { shouldSplit: true, priority: 'medium', preload: 'hover' },
  'image-gallery': { shouldSplit: true, priority: 'medium', preload: 'visible' },
  'search-modal': { shouldSplit: true, priority: 'low', preload: 'none' },
  
  // ✅ 第三方库分割
  'framer-motion': { shouldSplit: true, priority: 'low', preload: 'none' },
  'react-intersection-observer': { shouldSplit: true, priority: 'low', preload: 'visible' },
}

// 获取组件是否应该被分割
export function shouldSplitComponent(componentName: string): boolean {
  const config = CODE_SPLIT_CONFIG[componentName]
  return config?.shouldSplit ?? false
}

// 获取组件加载优先级
export function getComponentPriority(componentName: string): SplitConfig['priority'] {
  const config = CODE_SPLIT_CONFIG[componentName]
  return config?.priority ?? 'low'
}