'use client'

import React, { Suspense, lazy, useEffect, useState } from 'react'
import { ErrorBoundary } from 'react-error-boundary'

interface DynamicComponentProps<T = any> {
  // 导入函数
  loader: () => Promise<{ default: React.ComponentType<T> }>
  // 加载时显示的组件
  loading?: React.ReactNode
  // 错误时显示的组件
  error?: React.ComponentType<{ error: Error; retry: () => void }>
  // 组件的 props
  props?: T
  // 是否在服务端渲染
  ssr?: boolean
  // 预加载策略
  preload?: 'hover' | 'visible' | 'none'
}

/**
 * 通用的动态组件加载器
 * 处理代码分割、加载状态、错误边界
 */
export function DynamicComponent<T extends Record<string, any> = Record<string, any>>({
  loader,
  loading = <div>Loading...</div>,
  error: ErrorComponent,
  props = {} as T,
  ssr = false,
  preload = 'none'
}: DynamicComponentProps<T>) {
  const [Component, setComponent] = useState<React.ComponentType<T> | null>(null)
  const [isPreloading, setIsPreloading] = useState(false)

  // 动态导入组件
  useEffect(() => {
    if (!ssr || typeof window !== 'undefined') {
      const LazyComponent = lazy(loader)
      setComponent(LazyComponent as any)
    }
  }, [loader, ssr])

  // 预加载逻辑
  useEffect(() => {
    if (preload === 'hover' && !isPreloading) {
      const handleMouseEnter = () => {
        setIsPreloading(true)
        loader() // 预加载但不渲染
      }
      
      // 需要在实际使用时绑定到具体元素
      // 这里只是示例
      return () => {
        // cleanup
      }
    }
  }, [preload, loader, isPreloading])

  // SSR 场景下同步加载
  if (ssr && typeof window === 'undefined') {
    const SyncComponent = require(loader.toString()).default
    return <SyncComponent {...props} />
  }

  // 客户端渲染
  if (!Component) {
    return <>{loading}</>
  }

  return (
    <ErrorBoundary
      fallbackRender={({ error, resetErrorBoundary }) => {
        if (ErrorComponent) {
          return <ErrorComponent error={error} retry={resetErrorBoundary} />
        }
        return (
          <div className="p-4 border border-red-300 rounded bg-red-50">
            <p>Failed to load component</p>
            <button onClick={resetErrorBoundary} className="mt-2 px-4 py-2 bg-red-500 text-white rounded">
              Retry
            </button>
          </div>
        )
      }}
    >
      <Suspense fallback={loading}>
        <Component {...props} />
      </Suspense>
    </ErrorBoundary>
  )
}

/**
 * 创建动态导入的 HOC
 */
export function createDynamicComponent<T extends Record<string, any> = Record<string, any>>(
  loader: () => Promise<{ default: React.ComponentType<T> }>,
  options: Omit<DynamicComponentProps<T>, 'loader' | 'props'> = {}
) {
  const DynamicWrapper = (props: T) => (
    <DynamicComponent<T>
      loader={loader}
      props={props}
      {...options}
    />
  )
  
  DynamicWrapper.displayName = 'DynamicComponent'
  
  return DynamicWrapper
}