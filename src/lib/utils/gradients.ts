// Gradient utility functions for homepage title

export interface GradientConfig {
  usePreset?: boolean
  preset?: 'blue-purple' | 'orange-red' | 'green-cyan' | 'pink-purple' | 'rainbow'
  fromColor?: { hex?: string }
  viaColor?: { hex?: string }
  toColor?: { hex?: string }
  direction?: 'to right' | 'to left' | 'to bottom' | 'to top' | 'to bottom right' | 'to bottom left'
}

// Preset gradient definitions
const presetGradients = {
  'blue-purple': {
    from: '#3B82F6', // blue-500
    via: '#8B5CF6',  // violet-500
    to: '#A855F7',   // purple-500
  },
  'orange-red': {
    from: '#FB923C', // orange-400
    via: '#F97316',  // orange-500
    to: '#EF4444',   // red-500
  },
  'green-cyan': {
    from: '#10B981', // emerald-500
    via: '#14B8A6',  // teal-500
    to: '#06B6D4',   // cyan-500
  },
  'pink-purple': {
    from: '#EC4899', // pink-500
    via: '#DB2777',  // pink-600
    to: '#9333EA',   // purple-600
  },
  'rainbow': {
    from: '#EF4444', // red-500
    via: '#F59E0B',  // amber-500
    to: '#10B981',   // emerald-500
  },
}

export function getGradientStyle(config?: GradientConfig) {
  if (!config) {
    // Default gradient
    return {
      background: `linear-gradient(to right, #3B82F6, #8B5CF6, #A855F7)`,
      WebkitBackgroundClip: 'text',
      WebkitTextFillColor: 'transparent',
      backgroundClip: 'text',
      color: 'transparent', // Fallback for browsers that don't support background-clip
    }
  }

  const direction = config.direction || 'to right'
  
  let colors: string[]
  
  if (config.usePreset && config.preset && presetGradients[config.preset]) {
    const preset = presetGradients[config.preset]
    colors = [preset.from, preset.via, preset.to].filter(Boolean)
  } else {
    // Use custom colors
    const fromColor = config.fromColor?.hex || '#3B82F6'
    const toColor = config.toColor?.hex || '#A855F7'
    const viaColor = config.viaColor?.hex
    
    colors = viaColor 
      ? [fromColor, viaColor, toColor]
      : [fromColor, toColor]
  }
  
  return {
    background: `linear-gradient(${direction}, ${colors.join(', ')})`,
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text',
    color: 'transparent', // Fallback
  }
}

// Utility to create CSS variables for gradients (for potential future use)
export function getGradientCSSVariables(config?: GradientConfig) {
  if (!config) return {}
  
  let fromColor, viaColor, toColor
  
  if (config.usePreset && config.preset && presetGradients[config.preset]) {
    const preset = presetGradients[config.preset]
    fromColor = preset.from
    viaColor = preset.via
    toColor = preset.to
  } else {
    fromColor = config.fromColor?.hex || '#3B82F6'
    viaColor = config.viaColor?.hex
    toColor = config.toColor?.hex || '#A855F7'
  }
  
  return {
    '--gradient-from': fromColor,
    '--gradient-via': viaColor || 'transparent',
    '--gradient-to': toColor,
    '--gradient-direction': config.direction || 'to right',
  }
}