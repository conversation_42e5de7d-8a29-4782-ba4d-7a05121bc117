// Color utility functions for dynamic theming

/**
 * Convert hex color to RGB values
 */
export function hexToRgb(hex: string): { r: number; g: number; b: number } | null {
  // Remove # if present
  const cleanHex = hex.replace('#', '')
  
  if (cleanHex.length !== 6) {
    return null
  }
  
  const result = /^([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(cleanHex)
  return result
    ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16),
      }
    : null
}

/**
 * Convert hex color to CSS RGB string
 */
export function hexToRgbString(hex: string): string {
  const rgb = hexToRgb(hex)
  return rgb ? `${rgb.r} ${rgb.g} ${rgb.b}` : '59 130 246' // Default to blue-600
}

/**
 * Generate color variations (lighter/darker shades)
 */
export function generateColorShades(hex: string): {
  50: string
  100: string
  200: string
  300: string
  400: string
  500: string
  600: string
  700: string
  800: string
  900: string
} {
  const rgb = hexToRgb(hex)
  if (!rgb) {
    // Return default blue shades if invalid hex
    return {
      50: '239 246 255',
      100: '219 234 254',
      200: '191 219 254',
      300: '147 197 253',
      400: '96 165 250',
      500: '59 130 246',
      600: '37 99 235',
      700: '29 78 216',
      800: '30 64 175',
      900: '30 58 138',
    }
  }

  // Generate shades by adjusting lightness
  const shades: Record<number, string> = {}
  const shadesConfig = [
    { key: 50, lightness: 0.95 },
    { key: 100, lightness: 0.9 },
    { key: 200, lightness: 0.8 },
    { key: 300, lightness: 0.7 },
    { key: 400, lightness: 0.6 },
    { key: 500, lightness: 0.5 }, // Original color
    { key: 600, lightness: 0.4 },
    { key: 700, lightness: 0.3 },
    { key: 800, lightness: 0.2 },
    { key: 900, lightness: 0.1 },
  ]

  shadesConfig.forEach(({ key, lightness }) => {
    const factor = key === 500 ? 1 : lightness
    
    if (key < 500) {
      // Lighter shades - mix with white
      const r = Math.round(rgb.r + (255 - rgb.r) * (1 - factor))
      const g = Math.round(rgb.g + (255 - rgb.g) * (1 - factor))
      const b = Math.round(rgb.b + (255 - rgb.b) * (1 - factor))
      shades[key] = `${r} ${g} ${b}`
    } else if (key > 500) {
      // Darker shades - reduce values
      const r = Math.round(rgb.r * factor)
      const g = Math.round(rgb.g * factor)
      const b = Math.round(rgb.b * factor)
      shades[key] = `${r} ${g} ${b}`
    } else {
      // Original color
      shades[key] = `${rgb.r} ${rgb.g} ${rgb.b}`
    }
  })

  return shades as any
}

/**
 * Apply theme colors to CSS variables
 */
export function applyThemeColors(primaryColor?: string, secondaryColor?: string) {
  if (typeof window === 'undefined') return

  const root = document.documentElement
  
  if (primaryColor) {
    const primaryShades = generateColorShades(primaryColor)
    Object.entries(primaryShades).forEach(([shade, value]) => {
      root.style.setProperty(`--color-primary-${shade}`, value)
    })
  }
  
  if (secondaryColor) {
    const secondaryShades = generateColorShades(secondaryColor)
    Object.entries(secondaryShades).forEach(([shade, value]) => {
      root.style.setProperty(`--color-secondary-${shade}`, value)
    })
  }
}