// GROQ queries for Sanity CMS

// Reusable flexible image query fragment
const flexibleImageQuery = `{
  imageType,
  imageType == "upload" => {
    "uploadedImage": uploadedImage {
      asset,
      alt,
      caption
    }
  },
  imageType == "external" => {
    externalUrl,
    alt,
    caption,
    fallbackImage
  }
}`

export const productsQuery = `
  *[_type == "product" && isPublished == true && !(_id in path("drafts.**"))] | order(_createdAt desc) {
    _id,
    name,
    slug,
    shortDescription,
    gallery[] {
      imageType,
      imageType == "upload" => {
        "uploadedImage": uploadedImage {
          asset,
          alt,
          caption
        }
      },
      imageType == "external" => {
        externalUrl,
        alt,
        caption,
        fallbackImage
      }
    },
    category->{
      name,
      slug
    },
    price,
    currency,
    stockStatus,
    tags,
    _createdAt
  }
`

export const productBySlugQuery = `
  *[_type == "product" && slug.current == $slug && isPublished == true && !(_id in path("drafts.**"))][0] {
    _id,
    name,
    slug,
    shortDescription,
    description,
    gallery[] {
      imageType,
      imageType == "upload" => {
        "uploadedImage": uploadedImage {
          asset,
          alt,
          caption
        }
      },
      imageType == "external" => {
        externalUrl,
        alt,
        caption,
        fallbackImage
      }
    },
    category->{
      name,
      slug
    },
    ipSeries->{
      name,
      slug
    },
    price,
    currency,
    stockStatus,
    tags,
    publishedAt,
    seo
  }
`

export const categoriesQuery = `
  *[_type == "category"] | order(name.zh asc) {
    _id,
    name,
    slug,
    description,
    image
  }
`

export const productsByCategoryQuery = `
  *[_type == "product" && category->slug.current == $category && isPublished == true && !(_id in path("drafts.**"))] | order(publishedAt desc) {
    _id,
    name,
    slug,
    shortDescription,
    gallery[] {
      imageType,
      imageType == "upload" => {
        "uploadedImage": uploadedImage {
          asset,
          alt,
          caption
        }
      },
      imageType == "external" => {
        externalUrl,
        alt,
        caption,
        fallbackImage
      }
    },
    price,
    currency,
    stockStatus,
    tags,
    publishedAt
  }
`

// === Vision URL 查询示例 ===

// 获取所有产品（包括未发布的，用于调试）
export const allProductsQuery = `
  *[_type == "product"] | order(_createdAt desc) {
    _id,
    name,
    slug,
    isPublished,
    stockStatus,
    publishedAt,
    _createdAt
  }
`

// 获取产品的完整信息（包括所有关联）
export const productFullInfoQuery = `
  *[_type == "product" && slug.current == $slug][0] {
    ...,
    category->,
    ipSeries->,
    gallery
  }
`

// 多语言内容查询
export const productMultilingualQuery = `
  *[_type == "product" && isPublished == true && !(_id in path("drafts.**"))] {
    _id,
    "name_zh": name.zh,
    "name_en": name.en,
    "name_ar": name.ar,
    "shortDescription_zh": shortDescription.zh,
    "shortDescription_en": shortDescription.en,
    "shortDescription_ar": shortDescription.ar,
    slug,
    gallery[] {
      imageType,
      imageType == "upload" => {
        "uploadedImage": uploadedImage {
          asset,
          alt,
          caption
        }
      },
      imageType == "external" => {
        externalUrl,
        alt,
        caption,
        fallbackImage
      }
    },
    price,
    currency
  }
`

// 按价格范围筛选产品
export const productsByPriceRangeQuery = `
  *[_type == "product" && price >= $minPrice && price <= $maxPrice && isPublished == true && !(_id in path("drafts.**"))] | order(price asc) {
    _id,
    name,
    slug,
    shortDescription,
    gallery[] {
      imageType,
      imageType == "upload" => {
        "uploadedImage": uploadedImage {
          asset,
          alt,
          caption
        }
      },
      imageType == "external" => {
        externalUrl,
        alt,
        caption,
        fallbackImage
      }
    },
    price,
    currency,
    stockStatus,
    category->{
      name,
      slug
    }
  }
`

// 按库存状态筛选产品
export const productsByStockStatusQuery = `
  *[_type == "product" && stockStatus == $stockStatus && isPublished == true && !(_id in path("drafts.**"))] | order(publishedAt desc) {
    _id,
    name,
    slug,
    shortDescription,
    gallery[] {
      imageType,
      imageType == "upload" => {
        "uploadedImage": uploadedImage {
          asset,
          alt,
          caption
        }
      },
      imageType == "external" => {
        externalUrl,
        alt,
        caption,
        fallbackImage
      }
    },
    price,
    currency,
    stockStatus,
    publishedAt
  }
`

// 按标签筛选产品
export const productsByTagQuery = `
  *[_type == "product" && $tag in tags && isPublished == true && !(_id in path("drafts.**"))] | order(publishedAt desc) {
    _id,
    name,
    slug,
    shortDescription,
    gallery[] {
      imageType,
      imageType == "upload" => {
        "uploadedImage": uploadedImage {
          asset,
          alt,
          caption
        }
      },
      imageType == "external" => {
        externalUrl,
        alt,
        caption,
        fallbackImage
      }
    },
    price,
    currency,
    stockStatus,
    tags,
    publishedAt
  }
`

// 增强的搜索产品查询 - 支持全语言和智能评分
export const searchProductsQuery = `
  *[_type == "product" && (
    name.zh match $searchTerm + "*" ||
    name.en match $searchTerm + "*" ||
    name.ar match $searchTerm + "*" ||
    shortDescription.zh match $searchTerm + "*" ||
    shortDescription.en match $searchTerm + "*" ||
    shortDescription.ar match $searchTerm + "*" ||
    tags[]->name.zh match $searchTerm + "*" ||
    tags[]->name.en match $searchTerm + "*" ||
    category->name.zh match $searchTerm + "*" ||
    category->name.en match $searchTerm + "*" ||
    category->name.ar match $searchTerm + "*"
  ) && isPublished == true && !(_id in path("drafts.**"))] | order(_score desc, publishedAt desc)[0...20] {
    _id,
    name,
    slug,
    shortDescription,
    gallery[] {
      imageType,
      imageType == "upload" => {
        "uploadedImage": uploadedImage {
          asset,
          alt,
          caption
        }
      },
      imageType == "external" => {
        externalUrl,
        alt,
        caption,
        fallbackImage
      }
    },
    price,
    currency,
    stockStatus,
    publishedAt,
    category->{
      name,
      slug
    },
    tags[]->{
      name,
      slug
    },
    "relevanceScore": 100
  }
`

// 搜索分类查询
export const searchCategoriesQuery = `
  *[_type == "category" && (
    name.zh match $searchTerm + "*" ||
    name.en match $searchTerm + "*" ||
    name.ar match $searchTerm + "*" ||
    description.zh match $searchTerm + "*" ||
    description.en match $searchTerm + "*" ||
    description.ar match $searchTerm + "*"
  )] | order(name.zh asc)[0...10] {
    _id,
    name,
    slug,
    description,
    image,
    "productCount": count(*[_type == "product" && category._ref == ^._id && isPublished == true])
  }
`

// 带搜索参数的产品查询（用于搜索结果页面）
export const productsWithSearchQuery = `
  *[_type == "product" && (
    name.zh match $searchTerm + "*" ||
    name.en match $searchTerm + "*" ||
    name.ar match $searchTerm + "*" ||
    shortDescription.zh match $searchTerm + "*" ||
    shortDescription.en match $searchTerm + "*" ||
    shortDescription.ar match $searchTerm + "*"
  ) && isPublished == true && !(_id in path("drafts.**"))] | order(_score desc, publishedAt desc) {
    _id,
    name,
    slug,
    shortDescription,
    gallery[] {
      imageType,
      imageType == "upload" => {
        "uploadedImage": uploadedImage {
          asset,
          alt,
          caption
        }
      },
      imageType == "external" => {
        externalUrl,
        alt,
        caption,
        fallbackImage
      }
    },
    price,
    currency,
    stockStatus,
    publishedAt,
    category->{
      name,
      slug
    },
    tags[]->{
      name,
      slug
    }
  }
`

// 获取最新发布的产品
export const latestProductsQuery = `
  *[_type == "product" && isPublished == true && !(_id in path("drafts.**"))] | order(publishedAt desc)[0...$limit] {
    _id,
    name,
    slug,
    shortDescription,
    gallery[] {
      imageType,
      imageType == "upload" => {
        "uploadedImage": uploadedImage {
          asset,
          alt,
          caption
        }
      },
      imageType == "external" => {
        externalUrl,
        alt,
        caption,
        fallbackImage
      }
    },
    price,
    currency,
    stockStatus,
    publishedAt,
    category->{
      name,
      slug
    }
  }
`

// 获取特定IP系列的产品
export const productsByIPSeriesQuery = `
  *[_type == "product" && ipSeries->slug.current == $ipSeries && isPublished == true && !(_id in path("drafts.**"))] | order(publishedAt desc) {
    _id,
    name,
    slug,
    shortDescription,
    gallery[] {
      imageType,
      imageType == "upload" => {
        "uploadedImage": uploadedImage {
          asset,
          alt,
          caption
        }
      },
      imageType == "external" => {
        externalUrl,
        alt,
        caption,
        fallbackImage
      }
    },
    price,
    currency,
    stockStatus,
    ipSeries->{
      name,
      slug
    },
    publishedAt
  }
`

// 获取产品统计信息
export const productStatsQuery = `
  {
    "total": count(*[_type == "product" && !(_id in path("drafts.**"))]),
    "published": count(*[_type == "product" && isPublished == true && !(_id in path("drafts.**"))]),
    "draft": count(*[_type == "product" && (_id in path("drafts.**") || isPublished == false)]),
    "inStock": count(*[_type == "product" && stockStatus == "in-stock" && isPublished == true && !(_id in path("drafts.**"))]),
    "preOrder": count(*[_type == "product" && stockStatus == "pre-order" && isPublished == true && !(_id in path("drafts.**"))]),
    "soldOut": count(*[_type == "product" && stockStatus == "sold-out" && isPublished == true && !(_id in path("drafts.**"))]),
    "categories": count(*[_type == "category"]),
    "ipSeries": count(*[_type == "ipSeries"])
  }
`

// 获取产品的详细图片信息
export const productImagesQuery = `
  *[_type == "product" && slug.current == $slug][0] {
    _id,
    name,
    gallery[]{
      imageType,
      imageType == "upload" => {
        "url": uploadedImage.asset->url,
        "alt": uploadedImage.alt,
        "hotspot": uploadedImage.hotspot,
        "crop": uploadedImage.crop
      },
      imageType == "external" => {
        "url": externalUrl,
        "alt": alt,
        "fallbackImage": fallbackImage.asset->url
      }
    }
  }
`

// === 页面内容查询 ===

// 获取首页内容
export const homepageQuery = `
  *[_type == "homepage"][0] {
    _id,
    title,
    heroSection {
      mainTitle,
      subtitle,
      description,
      ctaButton
    },
    featuresSection {
      sectionTitle,
      features[]{
        icon,
        title,
        description
      }
    },
    brandStorySection {
      title,
      description,
      backgroundImage
    },
    statsSection {
      showStats,
      stats[]{
        number,
        label,
        color
      }
    },
    seo,
    _updatedAt
  }
`

// 获取关于我们页面内容
export const aboutPageQuery = `
  *[_type == "aboutPage"][0] {
    _id,
    title,
    heroSection {
      ...,
      "heroImage": heroImage {
        imageType,
        imageType == "upload" => {
          "uploadedImage": {
            "asset": uploadedImage.asset,
            "alt": uploadedImage.alt,
            "hotspot": uploadedImage.hotspot,
            "crop": uploadedImage.crop
          }
        },
        imageType == "external" => {
          "externalUrl": externalUrl,
          "alt": alt,
          "fallbackImage": fallbackImage.asset->url
        }
      }
    },
    sections[]{
      ...,
      _type == "imageTextSection" => {
        ...,
        "image": image {
          imageType,
          imageType == "upload" => {
            "uploadedImage": {
              "asset": uploadedImage.asset,
              "alt": uploadedImage.alt,
              "hotspot": uploadedImage.hotspot,
              "crop": uploadedImage.crop
            }
          },
          imageType == "external" => {
            "externalUrl": externalUrl,
            "alt": alt,
            "fallbackImage": fallbackImage.asset->url
          }
        }
      },
      _type == "teamSection" => {
        ...,
        teamMembers[]{
          ...,
          "photo": photo {
            imageType,
            imageType == "upload" => {
              "uploadedImage": {
                "asset": uploadedImage.asset,
                "alt": uploadedImage.alt,
                "hotspot": uploadedImage.hotspot,
                "crop": uploadedImage.crop
              }
            },
            imageType == "external" => {
              "externalUrl": externalUrl,
              "alt": alt,
              "fallbackImage": fallbackImage.asset->url
            }
          }
        }
      }
    },
    seo,
    _updatedAt
  }
`

// 获取联系我们页面内容
export const contactPageQuery = `
  *[_type == "contactPage"][0] {
    _id,
    title,
    heroSection,
    contactInfo,
    contactForm,
    mapSection,
    socialLinks[]{
      ...,
      "icon": icon {
        imageType,
        imageType == "upload" => {
          "url": uploadedImage.asset->url,
          "alt": uploadedImage.alt
        },
        imageType == "external" => {
          "url": externalUrl,
          "alt": alt,
          "fallbackImage": fallbackImage.asset->url
        }
      }
    },
    seo,
    _updatedAt
  }
`

// 获取站点设置
export const siteSettingsQuery = `
  *[_type == "siteSettings"][0] {
    _id,
    siteTitle,
    siteDescription,
    logo,
    favicon,
    contactInfo,
    socialMedia[]{
      platform,
      url,
      label
    },
    analytics,
    appearance,
    features,
    seo,
    _updatedAt
  }
`

// 获取导航配置
export const navigationQuery = `
  *[_type == "navigation"] | order(identifier asc) {
    _id,
    title,
    identifier,
    menuItems[]{
      title,
      slug,
      external,
      openInNewTab,
      icon,
      order,
      isActive,
      submenu[]{
        title,
        slug,
        external,
        openInNewTab,
        description,
        icon,
        isActive
      }
    },
    settings,
    _updatedAt
  }
`

// 根据标识符获取特定导航
export const navigationByIdentifierQuery = `
  *[_type == "navigation" && identifier == $identifier][0] {
    _id,
    title,
    identifier,
    menuItems[]{
      title,
      slug,
      external,
      openInNewTab,
      icon,
      order,
      isActive,
      submenu[]{
        title,
        slug,
        external,
        openInNewTab,
        description,
        icon,
        isActive
      }
    },
    settings,
    _updatedAt
  }
`

// 获取特色产品配置
export const featuredProductsQuery = `
  *[_type == "featuredProducts" && isActive == true][0] {
    _id,
    title,
    subtitle,
    "products": products[]->{
      _id,
      name,
      slug,
      shortDescription,
      gallery[] {
        imageType,
        imageType == "upload" => {
          "uploadedImage": uploadedImage {
            asset,
            alt,
            caption
          }
        },
        imageType == "external" => {
          externalUrl,
          alt,
          caption,
          fallbackImage
        }
      },
      price,
      currency,
      stockStatus,
      tags,
      category->{
        name,
        slug
      },
      publishedAt,
      tags,
      "rating": 4.5
    },
    displaySettings,
    layout,
    _updatedAt
  }
`

// 获取首页完整内容（包含特色产品）
export const homepageWithFeaturedQuery = `
  {
    "homepage": *[_type == "homepageBasic"][0] {
      _id,
      title,
      heroSection {
        titleDisplayMode,
        mainTitle,
        "titleImage": titleImage {
          imageType,
          imageType == "upload" => {
            "uploadedImage": uploadedImage {
              asset,
              alt,
              caption
            }
          },
          imageType == "external" => {
            externalUrl,
            alt,
            caption,
            fallbackImage
          }
        },
        titleGradient {
          usePreset,
          preset,
          fromColor,
          viaColor,
          toColor,
          direction
        },
        subtitle,
        description,
        ctaButton
      },
      featuresSection {
        sectionTitle,
        features[]{
          icon,
          title,
          description
        }
      },
      brandStorySection {
        title,
        description,
        "backgroundImage": backgroundImage {
          imageType,
          imageType == "upload" => {
            "uploadedImage": uploadedImage {
              asset,
              alt,
              caption,
              hotspot,
              crop
            }
          },
          imageType == "external" => {
            externalUrl,
            alt,
            caption,
            fallbackImage
          }
        },
        showProducts,
        "products": products[]->{
          _id,
          name,
          slug,
          shortDescription,
          gallery[] {
            imageType,
            imageType == "upload" => {
              "uploadedImage": uploadedImage {
                asset,
                alt,
                caption
              }
            },
            imageType == "external" => {
              externalUrl,
              alt,
              caption,
              fallbackImage
            }
          },
          price,
          currency,
          stockStatus,
          tags,
          category->{
            name,
            slug
          },
          publishedAt,
          tags,
          "rating": 4.5
        },
        productSettings
      },
      statsSection {
        showStats,
        stats[]{
          number,
          label,
          color
        }
      },
      seo,
      _updatedAt
    },
    "featuredProducts": *[_type == "featuredProducts" && isActive == true][0] {
      _id,
      title,
      subtitle,
      "products": products[]->{
        _id,
        name,
        slug,
        shortDescription,
        gallery[] {
          imageType,
          imageType == "upload" => {
            "uploadedImage": uploadedImage {
              asset,
              alt,
              caption
            }
          },
          imageType == "external" => {
            externalUrl,
            alt,
            caption,
            fallbackImage
          }
        },
        price,
        currency,
        stockStatus,
        tags,
        category->{
          name,
          slug
        },
        publishedAt,
        tags,
        "rating": 4.5 + ((_id % 5) * 0.1)
      },
      displaySettings,
      layout,
      _updatedAt
    }
  }
`