import { client } from './client'
import { siteSettingsQuery } from './queries'
import type { SiteSettings } from '@/types/sanity'

export async function getSiteSettings(): Promise<SiteSettings | null> {
  try {
    const settings = await client.fetch<SiteSettings>(siteSettingsQuery)
    return settings
  } catch (error) {
    console.error('Error fetching site settings:', error)
    return null
  }
}