// src/lib/sanity/image.ts

// 中文注释：通用图片 URL 解析工具，兼容 flexibleImage 与普通 Sanity image
// Logs in English by convention
import { urlFor } from './client'

// Type definitions for better type checking
interface FlexibleImageExternal {
  imageType: 'external'
  externalUrl: string
  alt?: string
  caption?: string
}

interface FlexibleImageUpload {
  imageType: 'upload'
  uploadedImage: {
    asset: {
      _ref: string
      _type: 'reference'
    }
    alt?: string
    caption?: string
  }
}

type FlexibleImage = FlexibleImageExternal | FlexibleImageUpload

/**
 * 根据 flexibleImage 对象生成最终可用的图片 URL。
 * 1. flexibleImage.external → 直接返回 externalUrl
 * 2. flexibleImage.upload  → 使用 uploadedImage 生成 Sanity URL
 * 若解析失败，返回占位图以避免运行时错误。
 *
 * @param flexibleImage  flexibleImage 对象
 * @param width          目标宽度，可选
 * @param height         目标高度，可选
 * @param fit            缩放模式
 */
export function resolveImageUrl(flexibleImage: FlexibleImage | null | undefined, width?: number, height?: number, fit: 'crop' | 'max' | 'min' = 'max'): string {
  try {
    if (!flexibleImage) {
      console.warn('resolveImageUrl: No image data provided', { flexibleImage, width, height, fit });
      throw new Error('No image data');
    }

    // 验证数据结构
    if (!flexibleImage.imageType) {
      console.error('resolveImageUrl: Missing imageType', { flexibleImage });
      throw new Error('Invalid image data: missing imageType');
    }

    let url = '';
    
    if (flexibleImage.imageType === 'external') {
      if (!flexibleImage.externalUrl) {
        console.error('resolveImageUrl: External image missing externalUrl', { flexibleImage });
        throw new Error('External image missing URL');
      }
      url = flexibleImage.externalUrl;
      if (width) url += `?w=${width}`;
      if (height) url += `&h=${height}`;
      url += `&fit=${fit}`;
    } else if (flexibleImage.imageType === 'upload') {
      if (!flexibleImage.uploadedImage?.asset?._ref) {
        console.error('resolveImageUrl: Upload image missing asset reference', { 
          flexibleImage,
          hasUploadedImage: !!flexibleImage.uploadedImage,
          hasAsset: !!flexibleImage.uploadedImage?.asset,
          hasRef: !!flexibleImage.uploadedImage?.asset?._ref
        });
        throw new Error('Upload image missing asset reference');
      }
      const builder = urlFor(flexibleImage.uploadedImage.asset._ref)
        .width(width ?? 400);
      if (height) builder.height(height);
      builder.fit(fit).quality(80);
      url = builder.url();
    } else {
      console.error('resolveImageUrl: Unknown imageType', { flexibleImage });
      throw new Error(`Unknown imageType: ${(flexibleImage as any).imageType || 'undefined'}`);
    }

    if (!url) {
      console.error('resolveImageUrl: Failed to generate URL', { flexibleImage, width, height, fit });
      throw new Error('Failed to generate URL');
    }
    return url;
  } catch (error) {
    console.error('resolveImageUrl error:', error);
    return '/images/placeholder.svg';
  }
}

/**
 * 从 flexibleImage 对象中提取 alt text
 */
export function resolveImageAlt(flexibleImage: FlexibleImage | null | undefined, fallback: string = 'Image'): string {
  try {
    if (!flexibleImage) return fallback

    // flexibleImage - external 类型
    if (flexibleImage.imageType === 'external' && flexibleImage.alt) {
      return flexibleImage.alt
    }

    // flexibleImage - upload 类型
    if (flexibleImage.imageType === 'upload' && flexibleImage.uploadedImage?.alt) {
      return flexibleImage.uploadedImage.alt
    }

    return fallback
  } catch (error) {
    console.error('resolveImageAlt error:', error)
    return fallback
  }
}

// 中文注释：自定义Next.js Image loader，支持Sanity和外部图像
export function sanityImageLoader({ src, width, quality }: { src: string; width: number; quality?: number }) {
  try {
    // 本地静态文件直接返回
    if (src.startsWith('/')) {
      return src
    }
    // 解析 URL 以便读取 hostname 与现有查询参数
    const url = new URL(src, src.startsWith('http') ? undefined : 'https://dummy.domain')

    // 先删除可能已有的 w / q / fm，避免重复
    url.searchParams.delete('w')
    url.searchParams.delete('q')
    url.searchParams.delete('fm')

    // 必须添加的宽度参数
    url.searchParams.set('w', width.toString())

    // 仅对 Sanity CDN 追加质量与格式，其他域名保持原样，避免 400
    if (url.hostname === 'cdn.sanity.io') {
      url.searchParams.set('q', (quality || 75).toString())
      url.searchParams.set('fm', 'webp')
    }

    return url.toString()
  } catch (error) {
    console.error('sanityImageLoader error:', error)
    return '/images/placeholder.svg'
  }
}

// 中文注释：类型守卫函数，用于检查FlexibleImage
export function isFlexibleImage(data: unknown): data is FlexibleImage {
  return typeof data === 'object' && data !== null && 'imageType' in data && (data.imageType === 'external' || data.imageType === 'upload');
} 