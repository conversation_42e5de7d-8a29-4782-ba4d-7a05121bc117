import {createClient, ClientConfig} from '@sanity/client'
import imageUrlBuilder from '@sanity/image-url'

// TypeScript interfaces for better type safety
interface SanityClientConfig extends ClientConfig {
  projectId: string
  dataset: string
  apiVersion: string
  useCdn: boolean
  token?: string
}

interface SanityPreviewClientConfig extends SanityClientConfig {
  perspective: 'previewDrafts'
}

// Environment variable validation
function validateEnvironmentVariables() {
  const errors: string[] = []
  
  if (!process.env.NEXT_PUBLIC_SANITY_PROJECT_ID) {
    errors.push('NEXT_PUBLIC_SANITY_PROJECT_ID is required')
  }
  
  if (!process.env.NEXT_PUBLIC_SANITY_DATASET) {
    errors.push('NEXT_PUBLIC_SANITY_DATASET is required')
  }
  
  if (errors.length > 0) {
    console.error(`Sanity configuration errors: ${errors.join(', ')}`)
    throw new Error(`Missing required Sanity environment variables: ${errors.join(', ')}`)
  }
  
  return true
}

// Validate environment variables (throws if invalid)
validateEnvironmentVariables()

// Server-side detection
const isServer = typeof window === 'undefined'

// Helper function to determine the dataset based on environment
function getDataset(): string {
  // Check for staging-specific environment variable first
  if (process.env.NEXT_PUBLIC_USE_STAGING_DATASET === 'true') {
    return process.env.NEXT_PUBLIC_SANITY_DATASET_STAGING || 'staging'
  }
  
  // Default to the main dataset
  return process.env.NEXT_PUBLIC_SANITY_DATASET!
}

// Shared client configuration - validated environment variables are guaranteed to exist
const baseClientConfig: SanityClientConfig = {
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID!,
  dataset: getDataset(),
  apiVersion: '2024-01-01',
  useCdn: false,
  token: process.env.SANITY_API_TOKEN,
}

// Create client with properly typed configuration
export const client = createClient(baseClientConfig)

// Export the current dataset for debugging and UI indicators
export const currentDataset = getDataset()

const builder = imageUrlBuilder(client)

export function urlFor(source: Parameters<typeof builder.image>[0]) {
  return builder.image(source)
}

// Enhanced fetch function with error handling
export async function fetchWithErrorHandling<T>(query: string, params?: object): Promise<T | null> {
  // Configuration validation happens at module initialization
  // If we reach here, configuration is valid

  // Only log sensitive information in development
  if (process.env.NODE_ENV !== 'production') {
    console.log('🔍 Sanity fetch request:', {
      query: query.substring(0, 100) + (query.length > 100 ? '...' : ''),
      params,
      projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
      dataset: currentDataset
    })
  } else {
    console.log('🔍 Sanity fetch request:', {
      query: query.substring(0, 100) + (query.length > 100 ? '...' : ''),
      paramsProvided: !!params,
      dataset: currentDataset,
      configValid: true
    })
  }

  try {
    const result = params ? await client.fetch<T>(query, params) : await client.fetch<T>(query)
    
    console.log('✅ Sanity fetch successful:', {
      resultType: Array.isArray(result) ? 'array' : typeof result,
      resultLength: Array.isArray(result) ? result.length : 'N/A',
      hasData: !!result
    })
    
    return result
  } catch (error) {
    console.error('❌ Sanity fetch error:', error)
    
    // Check if it's a network error
    if (error instanceof Error) {
      if (error.message.includes('fetch failed') || error.message.includes('Network')) {
        console.warn('Network error detected, Sanity may be unavailable')
        
        // Provide more specific error information
        if (error.message.includes('ConnectTimeoutError')) {
          console.warn('Connection timeout - this might be a proxy configuration issue')
          if (isServer) {
            const proxy = process.env.HTTPS_PROXY || process.env.https_proxy || process.env.HTTP_PROXY || process.env.http_proxy
            // Only log proxy details in development
            if (process.env.NODE_ENV !== 'production') {
              console.warn('Server-side proxy configuration:', proxy ? `Using proxy: ${proxy}` : 'No proxy configured')
            } else {
              console.warn('Server-side proxy configuration:', proxy ? 'Proxy configured' : 'No proxy configured')
            }
          } else {
            console.warn('Client-side request - proxy not available in browser')
          }
        }
      }
    }
    
    return null
  }
}

// Preview client for draft content
const previewClientConfig: SanityPreviewClientConfig = {
  ...baseClientConfig,
  perspective: 'previewDrafts',
}

export const previewClient = createClient(previewClientConfig)
// Validate preview client configuration
if (previewClient.config().token && typeof previewClient.config().token !== 'string') {
  throw new Error('Invalid SANITY_API_TOKEN configuration')
}