'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import LazyImage from '@/components/ui/LazyImage'
import { useConditionalMotion } from '@/hooks/useConditionalMotion'
import { Homepage, FeaturedProductsConfig, FeaturedProduct, FeatureItem } from '@/types/sanity'
import { resolveImageUrl, resolveImageAlt } from '@/lib/sanity/image'
import { ImagePerformanceMonitor } from '@/components/ui/OptimizedImage'
import { SearchBoxDynamic as SearchBox } from '../ui/SearchBoxDynamic'
import { imageSizes } from '@/components/ui/OptimizedImage'
import PortableText from '@/components/ui/PortableText'
import { getGradientStyle } from '@/lib/utils/gradients'

interface HomePageClientProps {
  homepageData?: Homepage
  featuredProductsData?: FeaturedProductsConfig
  locale?: string
  translations?: {
    welcome: string
    learnMore: string
    featuredProducts: string
    achievements: string
    subtitle: string
    description: string
    brandTagline: string
    qualityBadge: string
    trustMessage: string
    viewAllProducts: string
    viewDetails: string
    features: {
      multilingual: { title: string; description: string }
      cms: { title: string; description: string }
      global: { title: string; description: string }
    }
    cta: {
      browse: string
    }
    brandStory: {
      title: string
      description: string
    }
    stats: {
      products: string
      countries: string
      satisfaction: string
      support: string
    }
  }
}

// Helper function to get localized content
function getLocalizedContent(content: any, locale: string = 'zh'): string {
  if (!content) return ''
  if (typeof content === 'string') return content
  return content[locale] || content.zh || content.en || content.ar || ''
}


export function HomePageClient({ 
  homepageData, 
  featuredProductsData,
  locale = 'zh', 
  translations: t 
}: HomePageClientProps) {
  const { motion, AnimatePresence, reducedMotion, shouldUseMotion, isLoaded } = useConditionalMotion()
  const shouldReduceMotion = reducedMotion
  const [titleImageError, setTitleImageError] = useState(false)
  
  // 当图片URL发生变化时重置错误状态，允许重新尝试加载新图片
  useEffect(() => {
    if (homepageData?.heroSection?.titleImage) {
      setTitleImageError(false)
    }
  }, [homepageData?.heroSection?.titleImage])

  // Show static content if motion library is not loaded yet and should use motion
  if (shouldUseMotion && !isLoaded) {
    return (
      <main className="min-h-screen bg-gradient-to-br from-primary-50 via-primary-100 to-secondary-50">
        <div className="container mx-auto px-4 py-16 min-h-screen flex items-center justify-center">
          <div className="text-center max-w-4xl mx-auto">
            <div className="mb-6 flex justify-center">
              {(() => {
                const titleMode = homepageData?.heroSection?.titleDisplayMode || 'text'
                const titleText = getLocalizedContent(homepageData?.heroSection?.mainTitle, locale)
                
                // 图片模式：优先尝试显示图片
                if (titleMode === 'image' && homepageData?.heroSection?.titleImage) {
                  const imageUrl = resolveImageUrl(homepageData.heroSection.titleImage, 400, 240)
                  const imageAlt = resolveImageAlt(homepageData.heroSection.titleImage, 'MyNgaPop')
                  
                  // 检查图片URL是否有效且不是占位符
                  if (imageUrl && imageUrl !== '/images/placeholder.svg') {
                    return (
                      <Image 
                        src={imageUrl}
                        alt={imageAlt}
                        width={400}
                        height={240}
                        className="h-24 md:h-32 lg:h-40 w-auto object-contain"
                        priority
                      />
                    )
                  }
                  
                  // 图片模式但图片不可用时的提示（仅开发环境）
                  if (process.env.NODE_ENV === 'development') {
                    console.warn('首页配置为图片模式但图片不可用，已降级到文本模式（静态渲染）')
                  }
                }
                
                // 文本模式或图片模式降级处理
                if (titleText) {
                  const gradientStyle = getGradientStyle(homepageData?.heroSection?.titleGradient)
                  return (
                    <h1 className="text-5xl md:text-7xl lg:text-8xl font-bold leading-tight">
                      <span style={gradientStyle}>
                        {titleText}
                      </span>
                    </h1>
                  )
                }
                
                // 最终降级：显示默认Logo
                return (
                  <Image 
                    src="/images/logo.png" 
                    alt="MyNgaPop"
                    width={160}
                    height={96}
                    className="h-24 md:h-32 lg:h-40 w-auto"
                    unoptimized={true}
                  />
                )
              })()}
            </div>
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-64 mx-auto mb-4"></div>
              <div className="h-3 bg-gray-200 rounded w-48 mx-auto"></div>
            </div>
          </div>
        </div>
      </main>
    )
  }

  const fadeInUp = {
    initial: shouldReduceMotion ? { opacity: 0 } : { opacity: 0, y: 60 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: shouldReduceMotion ? 0.1 : 0.6 }
  }

  const staggerChildren = {
    animate: {
      transition: {
        staggerChildren: shouldReduceMotion ? 0 : 0.1
      }
    }
  }

  const cardVariants = {
    initial: shouldReduceMotion ? { opacity: 0 } : { opacity: 0, y: 40 },
    animate: { opacity: 1, y: 0 }
  }

  // Use CMS data if available, otherwise fallback to translations
  const mainTitle = homepageData?.heroSection?.mainTitle 
    ? getLocalizedContent(homepageData.heroSection.mainTitle, locale)
    : 'MyNgaPop'
  
  const subtitle = homepageData?.heroSection?.subtitle
    ? getLocalizedContent(homepageData.heroSection.subtitle, locale)
    : t?.subtitle || ''
  
  const description = homepageData?.heroSection?.description
    ? getLocalizedContent(homepageData.heroSection.description, locale)
    : t?.description || ''

  const ctaText = homepageData?.heroSection?.ctaButton?.text
    ? getLocalizedContent(homepageData.heroSection.ctaButton.text, locale)
    : t?.cta?.browse || (locale === 'zh' ? '浏览产品' : 'Browse Products')

  const ctaUrl = homepageData?.heroSection?.ctaButton?.url || '/products'

  const brandStoryTitle = homepageData?.brandStorySection?.title
    ? getLocalizedContent(homepageData.brandStorySection.title, locale)
    : t?.brandStory?.title || ''

  const brandStoryDescription = homepageData?.brandStorySection?.description
    ? homepageData.brandStorySection.description[locale as 'zh' | 'en' | 'ar'] || 
      homepageData.brandStorySection.description.zh ||
      homepageData.brandStorySection.description.en
    : null

  // Check if features section should be shown
  const shouldShowFeatures = homepageData?.featuresSection?.showSection !== false
  
  // Get features from CMS or use default
  let features: FeatureItem[] = []
  
  // Only get features if section should be shown
  if (shouldShowFeatures) {
    if (homepageData?.featuresSection?.features) {
      features = homepageData.featuresSection.features
    } else if (!homepageData) {
      // Use default e-commerce features when no CMS data
      features = [
        { 
          icon: '🛡️', 
          title: { 
            zh: t?.features?.multilingual?.title || '正版保证', 
            en: t?.features?.multilingual?.title || 'Authentic Products',
            ar: 'منتجات أصلية'
          },
          description: { 
            zh: t?.features?.multilingual?.description || '100%官方授权，品质保证', 
            en: t?.features?.multilingual?.description || '100% officially licensed, quality guaranteed',
            ar: '100% منتجات مرخصة رسمياً، جودة مضمونة'
          }
        },
        { 
          icon: '🚚', 
          title: { 
            zh: t?.features?.cms?.title || '全球配送', 
            en: t?.features?.cms?.title || 'Global Shipping',
            ar: 'شحن عالمي'
          },
          description: { 
            zh: t?.features?.cms?.description || '支持50+国家，安全快递', 
            en: t?.features?.cms?.description || 'Supporting 50+ countries with secure delivery',
            ar: 'دعم أكثر من 50 دولة مع توصيل آمن وسريع'
          }
        },
        { 
          icon: '💎', 
          title: { 
            zh: t?.features?.global?.title || '会员特权', 
            en: t?.features?.global?.title || 'VIP Benefits',
            ar: 'مزايا VIP'
          },
          description: { 
            zh: t?.features?.global?.description || 'VIP专享折扣和限定商品', 
            en: t?.features?.global?.description || 'Exclusive discounts and limited products for VIP members',
            ar: 'خصومات حصرية ومنتجات محدودة لأعضاء VIP'
          }
        }
      ]
    }
  }
  
  // Sort features by order if specified
  if (features.length > 0) {
    features = [...features].sort((a, b) => {
      const orderA = a.order || 999
      const orderB = b.order || 999
      return orderA - orderB
    })
  }
  
  // Get features section configuration (only if should show features)
  const featuresSection = shouldShowFeatures ? homepageData?.featuresSection : null
  const featuresLayout = featuresSection?.layout || 'grid-3'
  const featuresSectionTitle = featuresSection?.sectionTitle ? getLocalizedContent(featuresSection.sectionTitle, locale) : ''
  const featuresSectionDescription = featuresSection?.sectionDescription ? getLocalizedContent(featuresSection.sectionDescription, locale) : ''

  // Get stats from CMS or use default
  const stats = homepageData?.statsSection?.stats || [
    { number: '1000+', label: { zh: t?.stats?.products || '产品', en: t?.stats?.products || 'Products' } },
    { number: '50+', label: { zh: t?.stats?.countries || '国家', en: t?.stats?.countries || 'Countries' } },
    { number: '99%', label: { zh: t?.stats?.satisfaction || '满意度', en: t?.stats?.satisfaction || 'Satisfaction' } },
    { number: '24/7', label: { zh: t?.stats?.support || '支持', en: t?.stats?.support || 'Support' } }
  ]

  // Get featured products data
  const featuredProducts = featuredProductsData?.products || []
  const featuredTitle = featuredProductsData?.title 
    ? getLocalizedContent(featuredProductsData.title, locale)
    : (t?.featuredProducts || (locale === 'zh' ? '精选产品' : locale === 'en' ? 'Featured Products' : 'المنتجات المميزة'))
  const featuredSubtitle = featuredProductsData?.subtitle
    ? getLocalizedContent(featuredProductsData.subtitle, locale)
    : (locale === 'zh' ? '探索我们精心挑选的热门动漫周边产品，每一件都承载着无数动漫爱好者的梦想' : 
       locale === 'en' ? 'Explore our carefully selected popular anime merchandise, each carrying the dreams of countless anime fans' : 
       'استكشف منتجاتنا المختارة بعناية من البضائع الشعبية للأنمي، كل منها يحمل أحلام عشاق الأنمي')

  const displaySettings = featuredProductsData?.displaySettings || {
    showPrices: true,
    showRatings: true,
    showCategories: true,
    showBadges: true
  }

  // Get brand story data
  const brandStoryBackgroundImage = homepageData?.brandStorySection?.backgroundImage
  const brandStoryProducts = homepageData?.brandStorySection?.products || []
  const brandStoryProductSettings = homepageData?.brandStorySection?.productSettings || {
    showPrices: true,
    showCategories: true,
    showBadges: true,
    layout: 'grid'
  }

  return (
    <main className="min-h-screen">
      {/* Enhanced Hero Section */}
      <section 
        className="relative min-h-screen bg-gradient-to-br from-primary-50 via-primary-100 to-secondary-50 overflow-hidden"
        aria-label="Hero section"
      >
        {/* Animated Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <motion.div
            className="absolute top-20 left-10 w-72 h-72 bg-primary-200 rounded-full mix-blend-multiply filter blur-xl opacity-70"
            animate={shouldReduceMotion ? {} : {
              x: [0, 100, 0],
              y: [0, -100, 0],
            }}
            transition={shouldReduceMotion ? {} : {
              duration: 20,
              repeat: Infinity,
              ease: "linear"
            }}
          />
          <motion.div
            className="absolute top-40 right-10 w-72 h-72 bg-secondary-200 rounded-full mix-blend-multiply filter blur-xl opacity-70"
            animate={shouldReduceMotion ? {} : {
              x: [0, -100, 0],
              y: [0, 100, 0],
            }}
            transition={shouldReduceMotion ? {} : {
              duration: 25,
              repeat: Infinity,
              ease: "linear"
            }}
          />
          <motion.div
            className="absolute -bottom-8 left-20 w-72 h-72 bg-pink-200 rounded-full mix-blend-multiply filter blur-xl opacity-70"
            animate={shouldReduceMotion ? {} : {
              x: [0, 50, 0],
              y: [0, -50, 0],
            }}
            transition={shouldReduceMotion ? {} : {
              duration: 15,
              repeat: Infinity,
              ease: "linear"
            }}
          />
        </div>

        <div className="relative container mx-auto px-4 py-16 min-h-screen flex items-center">
          <div className="w-full">
            {/* Hero Content */}
            <div className="text-center max-w-6xl mx-auto">
              <motion.div
                className="mb-8"
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
              >
                <span className="inline-block px-4 py-2 bg-white/80 backdrop-blur-sm rounded-full text-sm font-medium text-blue-600 border border-blue-200 shadow-sm">
                  ✨ {t?.welcome || (locale === 'zh' ? '欢迎来到动漫世界' : locale === 'en' ? 'Welcome to Anime World' : 'مرحبا بكم في عالم الأنمي')}
                </span>
              </motion.div>

              <motion.div 
                className="mb-6 flex justify-center"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, ease: "easeOut" }}
              >
                {(() => {
                  const titleMode = homepageData?.heroSection?.titleDisplayMode || 'text'
                  
                  // 图片模式：优先尝试显示图片
                  if (titleMode === 'image' && homepageData?.heroSection?.titleImage) {
                    const imageUrl = resolveImageUrl(homepageData.heroSection.titleImage, 400, 240)
                    const imageAlt = resolveImageAlt(homepageData.heroSection.titleImage, 'MyNgaPop')
                    
                    // 如果图片URL有效且不是占位符，尝试显示图片
                    if (imageUrl && imageUrl !== '/images/placeholder.svg' && !titleImageError) {
                      return (
                        <div className="relative">
                          <Image 
                            src={imageUrl}
                            alt={imageAlt}
                            width={400}
                            height={240}
                            className="h-24 md:h-32 lg:h-40 w-auto object-contain"
                            priority
                            onError={() => {
                              console.warn('首页标题图片加载失败，将降级到文本模式')
                              setTitleImageError(true)
                            }}
                          />
                        </div>
                      )
                    }
                    
                    // 图片模式但图片不可用时的提示（仅开发环境）
                    if (process.env.NODE_ENV === 'development') {
                      console.warn('首页配置为图片模式但图片不可用，已降级到文本模式', {
                        hasImageData: !!homepageData?.heroSection?.titleImage,
                        imageUrl,
                        hasError: titleImageError
                      })
                    }
                  }
                  
                  // 文本模式或图片模式降级处理
                  if (mainTitle) {
                    const titleGradient = homepageData?.heroSection ? homepageData.heroSection.titleGradient : undefined
                    const gradientStyle = getGradientStyle(titleGradient)
                    
                    return (
                      <h1 className="text-5xl md:text-7xl lg:text-8xl font-bold leading-tight">
                        <span style={gradientStyle}>
                          {mainTitle}
                        </span>
                      </h1>
                    )
                  }
                  
                  // 最终降级：显示默认Logo
                  return (
                    <Image 
                      src="/images/logo.png" 
                      alt="MyNgaPop"
                      width={160}
                      height={96}
                      className="h-24 md:h-32 lg:h-40 w-auto"
                      unoptimized={true}
                    />
                  )
                })()}
              </motion.div>
              
              <motion.p 
                className="text-xl md:text-3xl text-gray-600 mb-8 font-light"
                {...fadeInUp}
                transition={{ delay: 0.2, duration: 0.6 }}
              >
                {subtitle}
              </motion.p>
              
              <motion.div 
                className="mb-12 max-w-3xl mx-auto"
                {...fadeInUp}
                transition={{ delay: 0.4, duration: 0.6 }}
              >
                {Array.isArray(description) ? (
                  <PortableText 
                    value={description} 
                    className="text-lg md:text-xl text-gray-500 leading-relaxed" 
                  />
                ) : (
                  <p className="text-lg md:text-xl text-gray-500 leading-relaxed">
                    {description}
                  </p>
                )}
              </motion.div>

              {/* Enhanced CTA Buttons */}
              <motion.div 
                className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16"
                initial={{ opacity: 0, y: 40 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6, duration: 0.6 }}
              >
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Link 
                    href={ctaUrl} 
                    className="group relative inline-flex items-center px-8 py-4 bg-gradient-to-r from-primary-600 to-secondary-600 text-white font-semibold rounded-full shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden"
                  >
                    <span className="relative z-10 flex items-center">
                      {ctaText}
                      <svg className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                      </svg>
                    </span>
                    <div className="absolute inset-0 bg-gradient-to-r from-secondary-600 to-primary-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  </Link>
                </motion.div>
                
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Link 
                    href="/about" 
                    className="inline-flex items-center px-8 py-4 bg-white/80 backdrop-blur-sm text-gray-700 font-semibold rounded-full border border-gray-200 hover:bg-white hover:shadow-lg transition-all duration-300"
                  >
                    {t?.learnMore || (locale === 'zh' ? '了解更多' : locale === 'en' ? 'Learn More' : 'اعرف المزيد')}
                    <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </Link>
                </motion.div>
              </motion.div>

              {/* Enhanced Features Section */}
              {shouldShowFeatures && features.length > 0 && (
                <motion.div 
                  className="max-w-6xl mx-auto"
                  variants={staggerChildren}
                  initial="initial"
                  animate="animate"
                >
                  {/* Features Section Header */}
                  {(featuresSectionTitle || featuresSectionDescription) && (
                    <div className="text-center mb-12">
                      {featuresSectionTitle && (
                        <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
                          {featuresSectionTitle}
                        </h3>
                      )}
                      {featuresSectionDescription && (
                        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                          {featuresSectionDescription}
                        </p>
                      )}
                    </div>
                  )}
                  
                  {/* Features Grid with Dynamic Layout */}
                  <motion.div 
                    className={`gap-8 ${
                      featuresLayout === 'grid-4' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4' :
                      featuresLayout === 'horizontal' ? 'flex flex-wrap justify-center' :
                      featuresLayout === 'vertical' ? 'flex flex-col max-w-2xl mx-auto' :
                      'grid grid-cols-1 md:grid-cols-3' // default grid-3
                    }`}
                    variants={staggerChildren}
                    initial="initial"
                    animate="animate"
                  >
                    {features.map((feature, index) => {
                      const cardContent = (
                        <div className={`${
                          featuresLayout === 'vertical' ? 'flex items-center gap-6' : 'text-center'
                        }`}>
                          <div className={`text-4xl mb-6 group-hover:scale-110 transition-transform duration-300 ${
                            featuresLayout === 'vertical' ? 'mb-0 flex-shrink-0' : ''
                          }`}>
                            {feature.icon}
                          </div>
                          <div className={featuresLayout === 'vertical' ? 'flex-1' : ''}>
                            <h3 className="text-xl font-bold mb-4 text-gray-800">
                              {getLocalizedContent(feature.title, locale)}
                            </h3>
                            {(() => {
                              const desc = getLocalizedContent(feature.description, locale)
                              return Array.isArray(desc) ? (
                                <PortableText 
                                  value={desc} 
                                  className="text-gray-600 leading-relaxed" 
                                />
                              ) : (
                                <p className="text-gray-600 leading-relaxed">
                                  {desc}
                                </p>
                              )
                            })()}
                            {feature.linkUrl && (
                              <div className="mt-4 text-blue-600 font-medium group-hover:text-blue-700 transition-colors">
                                {locale === 'zh' ? '了解更多 →' : locale === 'en' ? 'Learn More →' : 'اعرف المزيد ←'}
                              </div>
                            )}
                          </div>
                        </div>
                      )
                      
                      return (
                        <motion.div 
                          key={index}
                          className={`group bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg p-8 hover:shadow-2xl transition-all duration-300 border border-white/50 ${
                            featuresLayout === 'horizontal' ? 'flex-1 min-w-[300px] max-w-[350px]' :
                            featuresLayout === 'vertical' ? 'mb-6 last:mb-0' : ''
                          }`}
                          variants={cardVariants}
                          transition={{ duration: 0.5 }}
                          whileHover={{ 
                            scale: 1.05,
                            rotateY: feature.linkUrl ? 0 : 5,
                          }}
                          whileTap={{ scale: 0.95 }}
                        >
                          {feature.linkUrl ? (
                            feature.isExternal ? (
                              <a 
                                href={feature.linkUrl}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="block cursor-pointer"
                              >
                                {cardContent}
                              </a>
                            ) : (
                              <Link href={feature.linkUrl} className="block cursor-pointer">
                                {cardContent}
                              </Link>
                            )
                          ) : (
                            cardContent
                          )}
                        </motion.div>
                      )
                    })}
                  </motion.div>
                </motion.div>
              )}
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <motion.div 
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: shouldReduceMotion ? 0 : 1.2, duration: shouldReduceMotion ? 0.1 : 0.6 }}
          role="button"
          aria-label="Scroll down to explore more content"
        >
          <motion.div
            className="w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center cursor-pointer hover:border-gray-600 transition-colors"
            animate={shouldReduceMotion ? {} : { y: [0, 10, 0] }}
            transition={shouldReduceMotion ? {} : { duration: 2, repeat: Infinity }}
            onClick={() => {
              const nextSection = document.querySelector('section:nth-child(2)')
              nextSection?.scrollIntoView({ behavior: 'smooth' })
            }}
          >
            <motion.div 
              className="w-1 h-3 bg-gray-400 rounded-full mt-2"
              animate={shouldReduceMotion ? {} : { scaleY: [1, 0.3, 1] }}
              transition={shouldReduceMotion ? {} : { duration: 2, repeat: Infinity }}
            />
          </motion.div>
        </motion.div>
      </section>

      {/* Featured Products Section */}
      <motion.section 
        className="py-20 bg-white"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
        data-testid="featured-products"
      >
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <motion.h2 
              className="text-4xl md:text-5xl font-bold text-gray-900 mb-6"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              <span className="bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent">
                {featuredTitle}
              </span>
            </motion.h2>
            <motion.p 
              className="text-lg text-gray-600 max-w-2xl mx-auto"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.2, duration: 0.6 }}
            >
              {featuredSubtitle}
            </motion.p>
          </div>

          {/* Featured Products Grid */}
          <motion.div 
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12"
            variants={staggerChildren}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
          >
            {/* Featured Products from CMS or fallback data */}
            {featuredProducts.length > 0 ? featuredProducts.map((product, index) => (
              <motion.div 
                key={product._id}
                className="group relative bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 border border-gray-100"
                variants={cardVariants}
                whileHover={{ 
                  scale: 1.03,
                  y: -5
                }}
                data-testid="product-card"
              >
                {/* Product Badges */}
                {displaySettings.showBadges && product.tags && product.tags.length > 0 && (
                  <div className="absolute top-4 left-4 z-10 flex flex-col gap-2">
                    {product.tags.includes('new') && (
                      <span className="bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                        {locale === 'zh' ? '新品' : locale === 'en' ? 'NEW' : 'جديد'}
                      </span>
                    )}
                    {product.tags.includes('limited') && (
                      <span className="bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                        {locale === 'zh' ? '限定' : locale === 'en' ? 'LIMITED' : 'محدود'}
                      </span>
                    )}
                    {product.tags.includes('popular') && (
                      <span className="bg-secondary-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                        {locale === 'zh' ? '热门' : locale === 'en' ? 'POPULAR' : 'شائع'}
                      </span>
                    )}
                    {product.tags.includes('sale') && (
                      <span className="bg-orange-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                        {locale === 'zh' ? '促销' : locale === 'en' ? 'SALE' : 'تخفيض'}
                      </span>
                    )}
                    {product.tags.includes('exclusive') && (
                      <span className="bg-primary-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                        {locale === 'zh' ? '独家' : locale === 'en' ? 'EXCLUSIVE' : 'حصري'}
                      </span>
                    )}
                    {product.tags.includes('preorder') && (
                      <span className="bg-yellow-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                        {locale === 'zh' ? '预售' : locale === 'en' ? 'PRE-ORDER' : 'طلب مسبق'}
                      </span>
                    )}
                  </div>
                )}

                {/* Product Image */}
                <div className="relative bg-gray-100 overflow-hidden aspect-square">
                  <LazyImage
                    src={resolveImageUrl(product.gallery?.[0], 400, undefined, 'max') || 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=400&fit=crop'}
                    alt={resolveImageAlt(product.gallery?.[0], getLocalizedContent(product.name, locale))}
                    fill  // 添加fill以填充容器，避免需要height
                    className="w-full h-full group-hover:scale-110 transition-transform duration-500"
                    eager={index < 3} // Priority loading for first 3 products
                    critical={index < 3}
                    loadMargin="150px" // Start loading 150px before viewport
                    sizes={imageSizes.productCard}
                    onLoadTime={(loadTime: number) => {
                      const monitor = ImagePerformanceMonitor.getInstance();
                      monitor.recordLoadTime(loadTime);
                    }}
                  />
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300" />
                  
                  {/* Quick Action Button */}
                  <motion.button
                    className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white text-gray-900 p-3 rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-all duration-300"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </motion.button>
                </div>

                {/* Product Info */}
                <div className="p-6">
                  <div className="flex items-center gap-2 mb-2">
                    {displaySettings.showCategories && product.category && (
                      <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                        {getLocalizedContent(product.category.name, locale)}
                      </span>
                    )}
                    {displaySettings.showRatings && (
                      <div className="flex items-center gap-1">
                        <svg className="w-4 h-4 text-yellow-400 fill-current" viewBox="0 0 20 20">
                          <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                        </svg>
                        <span className="text-sm text-gray-600">{product.rating?.toFixed(1) || '4.5'}</span>
                      </div>
                    )}
                  </div>
                  
                  <h3 className="text-lg font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                    {getLocalizedContent(product.name, locale)}
                  </h3>
                  
                  {product.shortDescription && (
                    <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                      {getLocalizedContent(product.shortDescription, locale)}
                    </p>
                  )}
                  
                  <div className="flex items-center justify-between">
                    {displaySettings.showPrices && product.price && (
                      <span className="text-2xl font-bold text-blue-600">
                        {product.currency === 'CNY' ? '¥' : product.currency === 'USD' ? '$' : 'AED'}{product.price}
                      </span>
                    )}
                    <motion.button
                      className="bg-primary-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-primary-700 transition-colors"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => window.location.href = `/products/${product.slug.current}`}
                    >
                      {t?.viewDetails || (locale === 'zh' ? '查看详情' : locale === 'en' ? 'View Details' : 'عرض التفاصيل')}
                    </motion.button>
                  </div>
                </div>
              </motion.div>
            )) : 
            // Fallback products when CMS data is not available
            [
              {
                id: '1',
                name: { zh: '精美手办模型', en: 'Premium Figure Model', ar: 'نموذج فاخر' },
                price: '¥299',
                image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=400&fit=crop',
                category: { zh: '手办', en: 'Figures', ar: 'النماذج' },
                rating: 4.8,
                isNew: true
              },
              {
                id: '2',
                name: { zh: '限量版周边', en: 'Limited Edition Merch', ar: 'إصدار محدود' },
                price: '¥199',
                image: 'https://images.unsplash.com/photo-1572375992501-4b0892d50c69?w=400&h=400&fit=crop',
                category: { zh: '周边', en: 'Merchandise', ar: 'بضائع' },
                rating: 4.9,
                isLimited: true
              },
              {
                id: '3',
                name: { zh: '经典角色模型', en: 'Classic Character Model', ar: 'نموذج شخصية كلاسيكية' },
                price: '¥399',
                image: 'https://images.unsplash.com/photo-1572375992501-4b0892d50c69?w=400&h=400&fit=crop',
                category: { zh: '模型', en: 'Models', ar: 'نماذج' },
                rating: 4.7,
                isPopular: true
              }
            ].map((product, index) => (
              <motion.div 
                key={product.id}
                className="group relative bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 border border-gray-100"
                variants={cardVariants}
                whileHover={{ 
                  scale: 1.03,
                  y: -5
                }}
                data-testid="product-card"
              >
                {/* Product Badges - Fallback products don't have tags */}
                <div className="absolute top-4 left-4 z-10 flex flex-col gap-2">
                  {product.isNew && (
                    <span className="bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                      {locale === 'zh' ? '新品' : locale === 'en' ? 'NEW' : 'جديد'}
                    </span>
                  )}
                </div>

                {/* Product Image */}
                <div className="relative bg-gray-100 overflow-hidden aspect-square">
                  <LazyImage
                    src={product.image}
                    alt={getLocalizedContent(product.name, locale)}
                    fill  // 添加fill以填充容器，避免需要height
                    className="w-full h-full group-hover:scale-110 transition-transform duration-500"
                    eager={index < 3} // Priority loading for first 3 products
                    critical={index < 3}
                    loadMargin="150px"
                    sizes={imageSizes.productCard}
                    onLoadTime={(loadTime: number) => {
                      const monitor = ImagePerformanceMonitor.getInstance();
                      monitor.recordLoadTime(loadTime);
                    }}
                  />
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300" />
                  
                  {/* Quick Action Button */}
                  <motion.button
                    className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white text-gray-900 p-3 rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-all duration-300"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </motion.button>
                </div>

                {/* Product Info */}
                <div className="p-6">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                      {getLocalizedContent(product.category, locale)}
                    </span>
                    <div className="flex items-center gap-1">
                      <svg className="w-4 h-4 text-yellow-400 fill-current" viewBox="0 0 20 20">
                        <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                      </svg>
                      <span className="text-sm text-gray-600">{product.rating}</span>
                    </div>
                  </div>
                  
                  <h3 className="text-lg font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                    {getLocalizedContent(product.name, locale)}
                  </h3>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-2xl font-bold text-blue-600">{product.price}</span>
                    <motion.button
                      className="bg-primary-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-primary-700 transition-colors"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      {t?.viewDetails || (locale === 'zh' ? '查看详情' : locale === 'en' ? 'View Details' : 'عرض التفاصيل')}
                    </motion.button>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* View All Products CTA */}
          <motion.div 
            className="text-center"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.4, duration: 0.6 }}
          >
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Link 
                href="/products" 
                className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-primary-600 to-secondary-600 text-white font-semibold rounded-full shadow-lg hover:shadow-xl transition-all duration-300"
              >
                {t?.viewAllProducts || (locale === 'zh' ? '查看全部产品' : locale === 'en' ? 'View All Products' : 'عرض جميع المنتجات')}
                <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </Link>
            </motion.div>
          </motion.div>
        </div>
      </motion.section>

      {/* Enhanced Brand Story Section */}
      <motion.section 
        className="relative py-20 bg-gradient-to-br from-gray-50 to-primary-50 overflow-hidden"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
        data-testid="brand-story"
      >
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.4'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }} />
        </div>

        <div className="relative container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            {/* Section Header */}
            <div className="text-center mb-16">
              <motion.h2 
                className="text-4xl md:text-5xl font-bold text-gray-900 mb-6"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
              >
                <span className="bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent">
                  {brandStoryTitle}
                </span>
              </motion.h2>
            </div>

            {/* Brand Story Content */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-20">
              {/* Story Text */}
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8 }}
              >
                
                {/* Render brand story description */}
                {brandStoryDescription && Array.isArray(brandStoryDescription) && (
                  <div className="text-lg text-gray-600 mb-6 leading-relaxed">
                    <PortableText value={brandStoryDescription} />
                  </div>
                )}

              </motion.div>

              {/* Visual Elements */}
              <motion.div
                className="relative"
                initial={{ opacity: 0, x: 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8 }}
              >
                <div className="relative">
                  {/* Brand Story Image */}
                  <div className="aspect-square bg-gradient-to-br from-primary-100 to-secondary-100 rounded-3xl overflow-hidden shadow-2xl relative">
                    {(() => {
                      // Validate image data before using it
                      const hasValidImage = brandStoryBackgroundImage && (
                        (brandStoryBackgroundImage.imageType === 'external' && brandStoryBackgroundImage.externalUrl) ||
                        (brandStoryBackgroundImage.imageType === 'upload' && brandStoryBackgroundImage.uploadedImage?.asset?._ref)
                      );
                      
                      const imageSrc = hasValidImage 
                        ? resolveImageUrl(brandStoryBackgroundImage) 
                        : "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=600&h=600&fit=crop";
                      
                      const imageAlt = hasValidImage 
                        ? resolveImageAlt(brandStoryBackgroundImage, locale) || "Brand Story" 
                        : "Brand Story";
                      
                      return (
                        <LazyImage
                          src={imageSrc}
                          alt={imageAlt}
                      fill
                      className="object-contain w-full h-full"
                      placeholder="blur"
                      blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxAAPwCdABmX/9k="
                      loadMargin="200px" // Load earlier for brand story section
                      critical={false} // Not above the fold
                      onLoadTime={(loadTime: number) => {
                        const monitor = ImagePerformanceMonitor.getInstance();
                        monitor.recordLoadTime(loadTime);
                      }}
                        />
                      );
                    })()}
                  </div>
                  
                  {/* Floating Elements */}
                  <motion.div
                    className="absolute -top-4 -right-4 w-24 h-24 bg-white rounded-2xl shadow-lg flex items-center justify-center"
                    animate={{ 
                      y: [0, -10, 0],
                      rotate: [0, 5, 0]
                    }}
                    transition={{ 
                      duration: 3,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  >
                    <span className="text-3xl">🎨</span>
                  </motion.div>
                  
                  <motion.div
                    className="absolute -bottom-4 -left-4 w-20 h-20 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-2xl shadow-lg flex items-center justify-center"
                    animate={{ 
                      y: [0, 10, 0],
                      rotate: [0, -5, 0]
                    }}
                    transition={{ 
                      duration: 4,
                      repeat: Infinity,
                      ease: "easeInOut",
                      delay: 1
                    }}
                  >
                    <span className="text-2xl">⭐</span>
                  </motion.div>
                </div>
              </motion.div>
            </div>

            {/* Brand Story Products Section */}
            {homepageData?.brandStorySection?.showProducts && brandStoryProducts.length > 0 && (
              <motion.div 
                className="mt-20"
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8 }}
              >
                <div className="text-center mb-12">
                  <h3 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    {locale === 'zh' ? '品牌故事产品' : locale === 'en' ? 'Our Story Products' : 'منتجات قصتنا'}
                  </h3>
                  <p className="text-gray-600">
                    {locale === 'zh' ? '这些产品代表着我们品牌的精神与价值' : 
                     locale === 'en' ? 'These products represent our brand spirit and values' : 
                     'هذه المنتجات تمثل روح وقيم علامتنا التجارية'}
                  </p>
                </div>

                {/* Products Grid */}
                <motion.div 
                  className={`grid gap-8 ${
                    brandStoryProductSettings.layout === 'carousel' 
                      ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4' 
                      : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
                  }`}
                  variants={staggerChildren}
                  initial="initial"
                  whileInView="animate"
                  viewport={{ once: true }}
                >
                  {brandStoryProducts.map((product, index) => (
                    <motion.div 
                      key={product._id}
                      className="group relative bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 border border-gray-100"
                      variants={cardVariants}
                      whileHover={{ 
                        scale: 1.03,
                        y: -5
                      }}
                      data-testid="product-card"
                    >
                      {/* Product Badges */}
                      {brandStoryProductSettings.showBadges && product.tags && product.tags.length > 0 && (
                        <div className="absolute top-4 left-4 z-10 flex flex-col gap-2">
                          {product.tags.includes('new') && (
                            <span className="bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                              {locale === 'zh' ? '新品' : locale === 'en' ? 'NEW' : 'جديد'}
                            </span>
                          )}
                          {product.tags.includes('limited') && (
                            <span className="bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                              {locale === 'zh' ? '限定' : locale === 'en' ? 'LIMITED' : 'محدود'}
                            </span>
                          )}
                          {product.tags.includes('popular') && (
                            <span className="bg-secondary-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                              {locale === 'zh' ? '热门' : locale === 'en' ? 'POPULAR' : 'شائع'}
                            </span>
                          )}
                          {product.tags.includes('sale') && (
                            <span className="bg-orange-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                              {locale === 'zh' ? '促销' : locale === 'en' ? 'SALE' : 'تخفيض'}
                            </span>
                          )}
                          {product.tags.includes('exclusive') && (
                            <span className="bg-primary-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                              {locale === 'zh' ? '独家' : locale === 'en' ? 'EXCLUSIVE' : 'حصري'}
                            </span>
                          )}
                          {product.tags.includes('preorder') && (
                            <span className="bg-yellow-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                              {locale === 'zh' ? '预售' : locale === 'en' ? 'PRE-ORDER' : 'طلب مسبق'}
                            </span>
                          )}
                        </div>
                      )}

                      {/* Product Image */}
                      <div className="relative bg-gray-100 overflow-hidden aspect-square">
                        <LazyImage
                          src={resolveImageUrl(product.gallery?.[0], 400, undefined, 'max') || 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=400&fit=crop'}
                          alt={resolveImageAlt(product.gallery?.[0], getLocalizedContent(product.name, locale))}
                          fill
                          className="w-full h-full group-hover:scale-110 transition-transform duration-500"
                          eager={index < 2} // Priority for first 2 brand story products
                          critical={false} // Not critical (below the fold)
                          loadMargin="250px" // Load earlier for brand story
                          sizes={imageSizes.productCard}
                          onLoadTime={(loadTime: number) => {
                            const monitor = ImagePerformanceMonitor.getInstance();
                            monitor.recordLoadTime(loadTime);
                          }}
                        />
                        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300" />
                        
                        {/* Quick Action Button */}
                        <motion.button
                          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white text-gray-900 p-3 rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-all duration-300"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                        >
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                        </motion.button>
                      </div>

                      {/* Product Info */}
                      <div className="p-6">
                        <div className="flex items-center gap-2 mb-2">
                          {brandStoryProductSettings.showCategories && product.category && (
                            <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                              {getLocalizedContent(product.category.name, locale)}
                            </span>
                          )}
                        </div>
                        
                        <h3 className="text-lg font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                          {getLocalizedContent(product.name, locale)}
                        </h3>
                        
                        {product.shortDescription && (
                          <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                            {getLocalizedContent(product.shortDescription, locale)}
                          </p>
                        )}
                        
                        <div className="flex items-center justify-between">
                          {brandStoryProductSettings.showPrices && product.price && (
                            <span className="text-xl font-bold text-blue-600">
                              {product.currency === 'CNY' ? '¥' : product.currency === 'USD' ? '$' : 'AED'}{product.price}
                            </span>
                          )}
                          <motion.button
                            className="bg-primary-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-primary-700 transition-colors"
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={() => window.location.href = `/products/${product.slug.current}`}
                          >
                            {t?.viewDetails || (locale === 'zh' ? '查看详情' : locale === 'en' ? 'View Details' : 'عرض التفاصيل')}
                          </motion.button>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </motion.div>
              </motion.div>
            )}

            {/* Enhanced Stats Section */}
            {(homepageData?.statsSection?.showStats !== false) && (
              <motion.div 
                className="bg-white rounded-3xl shadow-xl p-8 md:p-12"
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8 }}
                data-testid="stats"
              >
                <div className="text-center mb-12">
                  <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
                    {t?.achievements || (locale === 'zh' ? '我们的成就' : locale === 'en' ? 'Our Achievements' : 'إنجازاتنا')}
                  </h3>
                  <p className="text-gray-600">
                    {t?.trustMessage || (locale === 'zh' ? '用数字见证我们的成长与用户的信任' : 
                     locale === 'en' ? 'Numbers that showcase our growth and user trust' : 
                     'أرقام تُظهر نمونا وثقة المستخدمين')}
                  </p>
                </div>

                <motion.div 
                  className="grid grid-cols-2 lg:grid-cols-4 gap-8"
                  variants={staggerChildren}
                  initial="initial"
                  whileInView="animate"
                  viewport={{ once: true }}
                >
                  {stats.map((stat, index) => (
                    <motion.div 
                      key={index}
                      className="text-center group"
                      variants={cardVariants}
                      whileHover={{ scale: 1.05 }}
                    >
                      <motion.div 
                        className={`text-4xl md:text-5xl font-bold mb-2 ${
                          stat.color === 'green' ? 'text-green-600' :
                          stat.color === 'red' ? 'text-red-600' :
                          stat.color === 'purple' ? 'text-purple-600' :
                          stat.color === 'orange' ? 'text-orange-600' :
                          'text-blue-600'
                        }`}
                        initial={{ scale: 0 }}
                        whileInView={{ scale: 1 }}
                        viewport={{ once: true }}
                        transition={{ 
                          delay: index * 0.1,
                          type: "spring",
                          stiffness: 100
                        }}
                      >
                        {stat.number}
                      </motion.div>
                      <div className="text-gray-600 font-medium group-hover:text-gray-900 transition-colors">
                        {getLocalizedContent(stat.label, locale)}
                      </div>
                    </motion.div>
                  ))}
                </motion.div>
              </motion.div>
            )}
          </div>
        </div>
      </motion.section>
    </main>
  )
}
