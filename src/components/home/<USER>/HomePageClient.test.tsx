import { render, screen, fireEvent } from '@testing-library/react'
import { HomePageClient } from '../HomePageClient'
import { FeaturedProductsConfig } from '@/types/sanity'

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    section: ({ children, ...props }: any) => <section {...props}>{children}</section>,
    h1: ({ children, ...props }: any) => <h1 {...props}>{children}</h1>,
    h2: ({ children, ...props }: any) => <h2 {...props}>{children}</h2>,
    p: ({ children, ...props }: any) => <p {...props}>{children}</p>,
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
  },
  useReducedMotion: () => false,
}))

// Mock Next.js components
jest.mock('next/link', () => {
  const MockLink = ({ children, href, ...props }: any) => (
    <a href={href} {...props}>{children}</a>
  )
  MockLink.displayName = 'MockLink'
  return MockLink
})

jest.mock('next/image', () => {
  const MockImage = ({ src, alt, ...props }: any) => (
    // eslint-disable-next-line @next/next/no-img-element
    <img src={src} alt={alt} {...props} />
  )
  MockImage.displayName = 'MockImage'
  return MockImage
})

// Mock image resolver
jest.mock('@/lib/sanity/image', () => ({
  resolveImageUrl: (image: any, width?: number, height?: number) => {
    if (!image) return '/images/placeholder.svg'
    return 'https://example.com/test-image.jpg'
  }
}))

const mockFeaturedProducts: FeaturedProductsConfig = {
  _id: 'featured-1',
  title: {
    zh: '精选产品',
    en: 'Featured Products',
    ar: 'المنتجات المميزة'
  },
  subtitle: {
    zh: '探索我们精心挑选的热门动漫周边产品',
    en: 'Explore our carefully selected anime merchandise',
    ar: 'استكشف منتجاتنا المختارة بعناية'
  },
  products: [
    {
      _id: 'product-1',
      name: {
        zh: '测试手办',
        en: 'Test Figure',
        ar: 'نموذج اختبار'
      },
      slug: { current: 'test-figure' },
      shortDescription: {
        zh: '测试描述',
        en: 'Test description',
        ar: 'وصف الاختبار'
      },
      gallery: [
        {
          _type: 'flexibleImage',
          imageType: 'upload',
          uploadedImage: {
            _type: 'image',
            asset: {
              _ref: 'image-123',
              _type: 'reference'
            }
          }
        }
      ],
      price: 299,
      currency: 'CNY',
      stockStatus: 'in-stock',
      tags: ['new', 'popular'],
      category: {
        _id: 'cat-1',
        name: {
          zh: '手办',
          en: 'Figures',
          ar: 'النماذج'
        },
        slug: { current: 'figures' }
      },
      publishedAt: '2024-01-01',
      rating: 4.8,
      isPublished: true
    }
  ],
  displaySettings: {
    showPrices: true,
    showRatings: true,
    showCategories: true,
    showBadges: true
  },
  layout: {
    columnsDesktop: 3,
    columnsTablet: 2,
    columnsMobile: 1
  },
  isActive: true
}

describe('HomePageClient', () => {
  beforeAll(() => {
    // Mock environment variables
    process.env.NEXT_PUBLIC_SANITY_PROJECT_ID = 'test-project'
    process.env.NEXT_PUBLIC_SANITY_DATASET = 'test-dataset'
  })

  it('应该渲染首页基本内容', () => {
    render(<HomePageClient locale="zh" />)
    
    expect(screen.getByText('MyNgaPop')).toBeInTheDocument()
    expect(screen.getByText('欢迎来到动漫世界')).toBeInTheDocument()
  })

  it('应该渲染特色产品模块', () => {
    render(
      <HomePageClient 
        locale="zh" 
        featuredProductsData={mockFeaturedProducts}
      />
    )
    
    expect(screen.getByText('精选产品')).toBeInTheDocument()
    expect(screen.getByText('测试手办')).toBeInTheDocument()
    expect(screen.getByText('¥299')).toBeInTheDocument()
    expect(screen.getByText('新品')).toBeInTheDocument()
  })

  it('应该支持多语言显示', () => {
    render(
      <HomePageClient 
        locale="en" 
        featuredProductsData={mockFeaturedProducts}
      />
    )
    
    expect(screen.getByText('Featured Products')).toBeInTheDocument()
    expect(screen.getByText('Test Figure')).toBeInTheDocument()
    expect(screen.getByText('NEW')).toBeInTheDocument()
  })

  it('应该处理无特色产品数据的情况', () => {
    render(<HomePageClient locale="zh" />)
    
    // 应该显示默认的样例产品
    expect(screen.getByText('精选产品')).toBeInTheDocument()
    expect(screen.getByText('精美手办模型')).toBeInTheDocument()
  })

  it('应该正确处理产品点击事件', () => {
    // Mock window.location.href
    delete (window as any).location
    window.location = { href: '' } as any

    render(
      <HomePageClient 
        locale="zh" 
        featuredProductsData={mockFeaturedProducts}
      />
    )
    
    const viewDetailButton = screen.getByText('查看详情')
    fireEvent.click(viewDetailButton)
    
    expect(window.location.href).toBe('/products/test-figure')
  })

  it('应该根据显示设置控制元素显示', () => {
    const configWithoutBadges = {
      ...mockFeaturedProducts,
      displaySettings: {
        showPrices: false,
        showRatings: false,
        showCategories: false,
        showBadges: false
      }
    }

    render(
      <HomePageClient 
        locale="zh" 
        featuredProductsData={configWithoutBadges}
      />
    )
    
    expect(screen.queryByText('新品')).not.toBeInTheDocument()
    expect(screen.queryByText('¥299')).not.toBeInTheDocument()
  })

  it('应该处理滚动指示器点击', () => {
    // Mock querySelector
    const mockElement = { scrollIntoView: jest.fn() }
    jest.spyOn(document, 'querySelector').mockReturnValue(mockElement as any)

    render(<HomePageClient locale="zh" />)
    
    const scrollIndicator = screen.getByRole('button', { name: /scroll down/i })
    fireEvent.click(scrollIndicator)
    
    expect(mockElement.scrollIntoView).toHaveBeenCalledWith({ behavior: 'smooth' })
  })
})