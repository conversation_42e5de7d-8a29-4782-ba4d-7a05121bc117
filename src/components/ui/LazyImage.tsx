'use client';

import { useState, useEffect, ReactNode } from 'react';
import { useIntersectionObserver } from '@/hooks/useIntersectionObserver';
import OptimizedImage, { OptimizedImageProps } from './OptimizedImage';
import { cn } from '@/lib/utils';

interface LazyImageProps extends Omit<OptimizedImageProps, 'priority'> {
  /**
   * Whether to use eager loading for critical images
   * @default false
   */
  eager?: boolean;
  
  /**
   * Custom skeleton component to show while loading
   */
  skeleton?: ReactNode;
  
  /**
   * Distance from viewport to start loading (in pixels)
   * @default '100px'
   */
  loadMargin?: string;
  
  /**
   * Loading animation type
   * @default 'skeleton'
   */
  loadingType?: 'skeleton' | 'shimmer';
  
  /**
   * Custom className for the container
   */
  containerClassName?: string;
  
  /**
   * Whether this image is critical and should be prioritized
   * @default false
   */
  critical?: boolean;
  style?: React.CSSProperties;
}

/**
 * Lazy loading image component with advanced features
 * - Intersection Observer for viewport detection
 * - Progressive loading with LQIP support
 * - Multiple skeleton/loading states
 * - Network-aware quality adjustment
 */
export default function LazyImage(props: LazyImageProps) {
  const { eager = false, skeleton, loadMargin = '100px', loadingType = 'skeleton', containerClassName, critical = false, className, style, alt, ...imageProps } = props;
  const [shouldLoad, setShouldLoad] = useState(eager || critical);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);
  
  const { ref, inView } = useIntersectionObserver<HTMLDivElement>({
    rootMargin: loadMargin,
    triggerOnce: true,
    enabled: !shouldLoad,
  });

  // Start loading when in view or if eager/critical
  useEffect(() => {
    if (inView || eager || critical) {
      setShouldLoad(true);
    }
  }, [inView, eager, critical]);

  const handleImageLoad = () => {
    setImageLoaded(true);
    setImageError(false);
  };

  const handleImageError = () => {
    setImageError(true);
    setImageLoaded(false);
  };

  // Default skeleton component
  const DefaultSkeleton = () => (
    <div 
      className={cn(
        "animate-pulse bg-gray-200 dark:bg-gray-700",
        loadingType === 'shimmer' && "relative overflow-hidden",
        className
      )}
      style={style}
    >
      {loadingType === 'shimmer' && (
        <div className="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" />
      )}
    </div>
  );

  // Blur placeholder component
  const BlurPlaceholder = () => (
    <div 
      className={cn(
        "bg-gray-100 dark:bg-gray-800 flex items-center justify-center",
        className
      )}
      style={style}
    >
      <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded animate-pulse" />
    </div>
  );

  // Show skeleton/placeholder until we should load
  if (!shouldLoad) {
    return (
      <div ref={ref} className={cn("relative", containerClassName)}>
        {skeleton || <DefaultSkeleton />}
      </div>
    );
  }

  return (
    <div className={cn("relative", containerClassName)}>
      {/* Main image */}
      {shouldLoad && (
        <OptimizedImage
          {...imageProps}
          alt={alt}
          className={cn(
            "transition-opacity duration-500",
            imageLoaded ? "opacity-100" : "opacity-0",
            className
          )}
          style={style}
          onLoad={handleImageLoad}
          onError={handleImageError}
          priority={critical}
          isCritical={critical}
          isAboveFold={eager || critical}
        />
      )}
      
      {shouldLoad && !imageLoaded && !imageError && (
        <div className={cn(
          "absolute inset-0 z-20 pointer-events-none transition-opacity duration-300",
          imageLoaded ? "opacity-0" : "opacity-100"
        )}>
          {skeleton || <DefaultSkeleton />}
        </div>
      )}
      
      {/* Error state */}
      {imageError && (
        <div className={cn(
          "absolute inset-0 z-30 bg-gray-100 dark:bg-gray-800 flex items-center justify-center",
          className
        )}>
          <div className="text-center text-gray-500 dark:text-gray-400">
            <svg className="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <p className="text-xs">Image failed to load</p>
          </div>
        </div>
      )}
    </div>
  );
}

// Utility component for product grid skeletons
export function ProductImageSkeleton({ 
  className,
  aspectRatio = 'square' 
}: { 
  className?: string;
  aspectRatio?: 'square' | 'portrait' | 'landscape';
}) {
  const aspectClasses = {
    square: 'aspect-square',
    portrait: 'aspect-[3/4]',
    landscape: 'aspect-[4/3]',
  };

  return (
    <div className={cn(
      "relative overflow-hidden bg-gray-200 dark:bg-gray-700 rounded-lg",
      aspectClasses[aspectRatio],
      className
    )}>
      <div className="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/10 to-transparent" />
      
      {/* Fake image content */}
      <div className="absolute inset-4 bg-gray-300 dark:bg-gray-600 rounded opacity-50" />
      
      {/* Fake loading icon */}
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="w-8 h-8 bg-gray-400 dark:bg-gray-500 rounded-full animate-pulse" />
      </div>
    </div>
  );
}