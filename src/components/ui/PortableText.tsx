import React from 'react'
import { PortableText as BasePortableText } from '@portabletext/react'
import { cn } from '@/lib/utils'

interface FontSizeAnnotation {
  size: 'small' | 'default' | 'medium' | 'large' | 'xlarge' | 'xxlarge'
}

interface PortableTextProps {
  value: any[]
  className?: string
}

const fontSizeClasses = {
  small: 'text-sm',                    // 14px
  default: 'text-base',                 // 16px
  medium: 'text-xl',                    // 20px
  large: 'text-2xl',                    // 24px
  xlarge: 'text-4xl',                   // 32px (text-4xl is 36px, closest to 32px)
  xxlarge: 'text-5xl'                   // 40px (text-5xl is 48px, closest to 40px)
}

const portableTextComponents = {
  list: {
    bullet: ({ children }: any) => (
      <ul className="list-disc list-inside my-4 space-y-1">{children}</ul>
    ),
    number: ({ children }: any) => (
      <ol className="list-decimal list-inside my-4 space-y-1">{children}</ol>
    ),
  },
  listItem: {
    bullet: ({ children }: any) => (
      <li className="ml-4">{children}</li>
    ),
    number: ({ children }: any) => (
      <li className="ml-4">{children}</li>
    ),
  },
  block: {
    h1: ({ children }: any) => (
      <h1 className="text-4xl font-bold my-4 leading-tight">{children}</h1>
    ),
    h2: ({ children }: any) => (
      <h2 className="text-3xl font-bold my-3 leading-tight">{children}</h2>
    ),
    h3: ({ children }: any) => (
      <h3 className="text-2xl font-bold my-2 leading-tight">{children}</h3>
    ),
    blockquote: ({ children }: any) => (
      <blockquote className="border-l-4 border-gray-300 pl-4 italic my-4 text-gray-700">
        {children}
      </blockquote>
    ),
    normal: ({ children }: any) => (
      <p className="my-2 leading-relaxed">{children}</p>
    ),
  },
  marks: {
    // Decorators
    strong: ({ children }: any) => <strong className="font-bold">{children}</strong>,
    em: ({ children }: any) => <em className="italic">{children}</em>,
    underline: ({ children }: any) => <span className="underline">{children}</span>,
    // Annotations
    link: ({ value, children }: any) => {
      const target = (value?.href || '').startsWith('http') ? '_blank' : undefined
      return (
        <a
          href={value?.href}
          target={target}
          rel={target === '_blank' ? 'noindex nofollow' : ''}
          className="text-blue-600 hover:text-blue-800 underline transition-colors"
        >
          {children}
        </a>
      )
    },
    fontSize: ({ value, children }: any) => {
      const size = value?.size as keyof typeof fontSizeClasses
      const sizeClass = fontSizeClasses[size] || fontSizeClasses.default
      
      // 使用自定义样式确保精确的字号
      const exactSizes = {
        small: '14px',
        default: '16px',
        medium: '20px',
        large: '24px',
        xlarge: '32px',
        xxlarge: '40px'
      }
      
      return (
        <span 
          className={cn(sizeClass, 'inline leading-relaxed')}
          style={{ fontSize: exactSizes[size as keyof typeof exactSizes] || '16px' }}
        >
          {children}
        </span>
      )
    },
  },
}

export default function PortableText({ value, className }: PortableTextProps) {
  if (!value || !Array.isArray(value) || value.length === 0) {
    return null
  }

  return (
    <div className={cn('prose prose-gray max-w-none', className)}>
      <BasePortableText
        value={value}
        components={portableTextComponents}
      />
    </div>
  )
}