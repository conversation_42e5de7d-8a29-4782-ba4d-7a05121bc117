'use client'

import dynamic from 'next/dynamic'
import { SearchBoxSkeleton } from './SearchBoxSkeleton'

// Dynamically import SearchBox to reduce initial bundle size
// Search functionality is often used conditionally/on-demand
const SearchBox = dynamic(
  () => import('./SearchBox').then((mod) => ({ default: mod.SearchBox })),
  {
    ssr: false, // Search is interactive and client-side only
    loading: () => <SearchBoxSkeleton />,
  }
)

interface SearchBoxDynamicProps {
  locale?: string
  onClose?: () => void
  isFullScreen?: boolean
  placeholder?: string
}

export function SearchBoxDynamic(props: SearchBoxDynamicProps) {
  return <SearchBox {...props} />
}

export default SearchBoxDynamic