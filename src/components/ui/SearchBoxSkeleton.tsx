'use client'

interface SearchBoxSkeletonProps {
  isFullScreen?: boolean
  className?: string
}

export function SearchBoxSkeleton({ isFullScreen = false, className = '' }: SearchBoxSkeletonProps) {
  if (isFullScreen) {
    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-start justify-center pt-20">
        <div className="bg-white rounded-2xl shadow-2xl w-full max-w-2xl mx-4 max-h-[70vh] flex flex-col">
          {/* Header skeleton */}
          <div className="p-6 border-b border-gray-200">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <div className="w-5 h-5 bg-gray-300 rounded animate-pulse"></div>
              </div>
              <div className="w-full pl-12 pr-12 py-4 bg-gray-100 border-2 border-gray-200 rounded-xl animate-pulse">
                <div className="h-6 bg-gray-200 rounded w-1/3"></div>
              </div>
            </div>
          </div>

          {/* Results area skeleton */}
          <div className="flex-1 overflow-y-auto p-6">
            <div className="animate-pulse">
              <div className="flex justify-center mb-4">
                <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
              </div>
              <div className="text-center">
                <div className="h-4 bg-gray-200 rounded w-24 mx-auto"></div>
              </div>
            </div>
          </div>

          {/* Footer skeleton */}
          <div className="p-4 border-t border-gray-200 bg-gray-50 rounded-b-2xl">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-1">
                  <div className="w-8 h-6 bg-gray-200 rounded"></div>
                  <div className="w-12 h-4 bg-gray-200 rounded"></div>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-6 h-6 bg-gray-200 rounded"></div>
                  <div className="w-10 h-4 bg-gray-200 rounded"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Inline search box skeleton
  return (
    <div className={`relative ${className}`}>
      <div className="relative animate-pulse">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <div className="w-5 h-5 bg-gray-300 rounded"></div>
        </div>
        <div className="w-full pl-10 pr-4 py-3 bg-gray-100 border border-gray-200 rounded-lg">
          <div className="h-5 bg-gray-200 rounded w-32"></div>
        </div>
      </div>
    </div>
  )
}

export default SearchBoxSkeleton