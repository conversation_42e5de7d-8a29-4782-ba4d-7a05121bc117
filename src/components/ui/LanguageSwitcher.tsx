'use client'

import { useLocale } from 'next-intl'
import { usePathname, useRouter } from '@/i18n/routing'
import { useState, useEffect, useRef } from 'react'
import { routing } from '@/i18n/routing'

const languageNames = {
  zh: '中文',
  en: 'English',
  ar: 'العربية'
}

const languageFlags = {
  zh: '🇨🇳',
  en: '🇺🇸',
  ar: '🇸🇦'
}

export function LanguageSwitcher() {
  const locale = useLocale()
  const pathname = usePathname()
  const router = useRouter()
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Close dropdown when clicking outside or pressing Escape
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsOpen(false)
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      document.addEventListener('keydown', handleKeyDown)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [isOpen])

  const handleLanguageChange = (newLocale: string) => {
    console.log(`Switching from ${locale} to ${newLocale}`) // Debug log
    try {
      // Use next-intl router with explicit locale parameter
      router.push(pathname, { locale: newLocale })
      setIsOpen(false)
    } catch (error) {
      console.error('Language switch error:', error)
      // Fallback: force page reload with new locale
      window.location.href = `/${newLocale}${pathname}`
    }
  }

  return (
    <div className="relative" ref={dropdownRef} data-testid="language-switcher">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-3 py-2 rounded-lg border border-gray-300 hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        aria-expanded={isOpen}
        aria-haspopup="true"
        aria-label="Select language"
      >
        <span className="text-sm">{languageFlags[locale as keyof typeof languageFlags]}</span>
        <span className="text-sm font-medium">{languageNames[locale as keyof typeof languageNames]}</span>
        <svg
          className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
          <div className="py-1">
            {routing.locales.map((localeOption) => (
              <button
                key={localeOption}
                onClick={() => handleLanguageChange(localeOption)}
                className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-50 transition-colors flex items-center space-x-2 ${
                  locale === localeOption ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                }`}
              >
                <span>{languageFlags[localeOption as keyof typeof languageFlags]}</span>
                <span>{languageNames[localeOption as keyof typeof languageNames]}</span>
                {locale === localeOption && (
                  <svg className="w-4 h-4 ml-auto" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                )}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}