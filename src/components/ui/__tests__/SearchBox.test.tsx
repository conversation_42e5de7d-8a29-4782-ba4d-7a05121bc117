import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { useRouter } from 'next/navigation'
import { SearchBox } from '../SearchBox'

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}))

// Mock fetch
global.fetch = jest.fn()

// Mock Framer Motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
  },
  AnimatePresence: ({ children }: any) => children,
}))

describe('SearchBox', () => {
  const mockPush = jest.fn()
  const mockOnClose = jest.fn()

  beforeEach(() => {
    (useRouter as jest.Mock).mockReturnValue({
      push: mockPush,
    })
    jest.clearAllMocks()
  })

  afterEach(() => {
    jest.resetAllMocks()
  })

  it('渲染搜索框', () => {
    render(<SearchBox />)
    
    const input = screen.getByPlaceholderText('搜索产品、分类或页面...')
    expect(input).toBeInTheDocument()
  })

  it('支持多语言占位符', () => {
    const { rerender } = render(<SearchBox locale="en" />)
    expect(screen.getByPlaceholderText('Search products, categories or pages...')).toBeInTheDocument()

    rerender(<SearchBox locale="ar" />)
    expect(screen.getByPlaceholderText('البحث عن المنتجات أو الفئات أو الصفحات...')).toBeInTheDocument()
  })

  it('自定义占位符文本', () => {
    render(<SearchBox placeholder="自定义搜索提示" />)
    expect(screen.getByPlaceholderText('自定义搜索提示')).toBeInTheDocument()
  })

  it('输入查询后调用搜索 API', async () => {
    const mockResponse = {
      query: 'test',
      results: [
        {
          id: '1',
          title: '测试产品',
          type: 'product',
          url: '/products/test',
          description: '测试描述',
          price: '¥100'
        }
      ],
      total: 1,
      locale: 'zh'
    }

    ;(global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => mockResponse,
    })

    render(<SearchBox locale="zh" />)
    
    const input = screen.getByPlaceholderText('搜索产品、分类或页面...')
    fireEvent.change(input, { target: { value: 'test' } })

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith('/api/search?q=test&locale=zh', {
        signal: expect.any(AbortSignal),
      })
    })
  })

  it('显示搜索结果', async () => {
    const mockResponse = {
      query: 'test',
      results: [
        {
          id: '1',
          title: '测试产品',
          type: 'product',
          url: '/products/test',
          description: '测试描述',
          price: '¥100'
        }
      ],
      total: 1,
      locale: 'zh'
    }

    ;(global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => mockResponse,
    })

    render(<SearchBox locale="zh" />)
    
    const input = screen.getByPlaceholderText('搜索产品、分类或页面...')
    fireEvent.change(input, { target: { value: 'test' } })

    await waitFor(() => {
      expect(screen.getByText('测试产品')).toBeInTheDocument()
      expect(screen.getByText('测试描述')).toBeInTheDocument()
      expect(screen.getByText('¥100')).toBeInTheDocument()
    })
  })

  it('处理搜索错误', async () => {
    ;(global.fetch as jest.Mock).mockRejectedValueOnce(new Error('搜索失败'))

    render(<SearchBox locale="zh" />)
    
    const input = screen.getByPlaceholderText('搜索产品、分类或页面...')
    fireEvent.change(input, { target: { value: 'test' } })

    await waitFor(() => {
      expect(screen.getByText('搜索服务暂时不可用，请稍后重试')).toBeInTheDocument()
    })
  })

  it('键盘导航功能', async () => {
    const mockResponse = {
      query: 'test',
      results: [
        {
          id: '1',
          title: '测试产品1',
          type: 'product',
          url: '/products/test1',
        },
        {
          id: '2',
          title: '测试产品2',
          type: 'product',
          url: '/products/test2',
        }
      ],
      total: 2,
      locale: 'zh'
    }

    ;(global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => mockResponse,
    })

    render(<SearchBox locale="zh" />)
    
    const input = screen.getByPlaceholderText('搜索产品、分类或页面...')
    fireEvent.change(input, { target: { value: 'test' } })

    await waitFor(() => {
      expect(screen.getByText('测试产品1')).toBeInTheDocument()
    })

    // 测试向下箭头键
    fireEvent.keyDown(document, { key: 'ArrowDown' })
    
    // 测试回车键选择
    fireEvent.keyDown(document, { key: 'Enter' })
    
    expect(mockPush).toHaveBeenCalledWith('/products/test1')
  })

  it('Escape 键关闭搜索框', () => {
    render(<SearchBox onClose={mockOnClose} isFullScreen />)
    
    fireEvent.keyDown(document, { key: 'Escape' })
    
    expect(mockOnClose).toHaveBeenCalled()
  })

  it('点击结果项跳转到对应页面', async () => {
    const mockResponse = {
      query: 'test',
      results: [
        {
          id: '1',
          title: '测试产品',
          type: 'product',
          url: '/products/test',
        }
      ],
      total: 1,
      locale: 'zh'
    }

    ;(global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => mockResponse,
    })

    render(<SearchBox locale="zh" onClose={mockOnClose} />)
    
    const input = screen.getByPlaceholderText('搜索产品、分类或页面...')
    fireEvent.change(input, { target: { value: 'test' } })

    await waitFor(() => {
      const resultButton = screen.getByText('测试产品').closest('button')
      fireEvent.click(resultButton!)
    })

    expect(mockPush).toHaveBeenCalledWith('/products/test')
    expect(mockOnClose).toHaveBeenCalled()
  })

  it('处理空搜索查询', async () => {
    render(<SearchBox />)
    
    const input = screen.getByPlaceholderText('搜索产品、分类或页面...')
    
    // 输入空白字符
    fireEvent.change(input, { target: { value: '  ' } })
    
    // 不应该调用 API
    expect(global.fetch).not.toHaveBeenCalled()
  })

  it('防抖功能工作正常', async () => {
    render(<SearchBox />)
    
    const input = screen.getByPlaceholderText('搜索产品、分类或页面...')
    
    // 快速输入多个字符
    fireEvent.change(input, { target: { value: 't' } })
    fireEvent.change(input, { target: { value: 'te' } })
    fireEvent.change(input, { target: { value: 'tes' } })
    fireEvent.change(input, { target: { value: 'test' } })
    
    // 应该只调用一次 API（防抖后）
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledTimes(1)
    })
  })
})