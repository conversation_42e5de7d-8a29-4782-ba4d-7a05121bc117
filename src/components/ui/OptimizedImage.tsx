'use client'

import Image from 'next/image'
import { sanityImageLoader } from '@/lib/sanity/image'
import { cn } from '@/lib/utils'
import { IMAGE_QUALITY } from '@/constants'
import { useState, useEffect } from 'react'
import { NetworkQualityAdapter, getImagePriority } from '@/utils/network-detector'


/**
 * Calculate final image quality based on adaptive quality
 */
function getFinalQuality(adaptedQuality: number): number {
  return typeof adaptedQuality === 'number' ? Math.round(adaptedQuality) : IMAGE_QUALITY.DEFAULT
}

export interface OptimizedImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  fill?: boolean
  sizes?: string
  className?: string
  priority?: boolean
  quality?: number
  placeholder?: 'blur' | 'empty'
  blurDataURL?: string
  onLoad?: () => void
  onError?: () => void
  fallbackSrc?: string
  style?: React.CSSProperties
  onLoadTime?: (loadTime: number) => void
  aspectRatio?: string;  // e.g., '16/9' or '1/1'
  objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down'
  
  // New Priority Hints API support
  importance?: 'high' | 'low' | 'auto'
  
  // Network-aware quality adaptation
  isAboveFold?: boolean
  isCritical?: boolean
  index?: number // For priority calculation
  adaptiveQuality?: boolean // Enable network-aware quality adaptation
  
}

export default function OptimizedImage({
  src,
  alt,
  width,
  height,
  fill = false,
  sizes,
  className,
  priority = false,
  quality = IMAGE_QUALITY.DEFAULT,
  placeholder = 'empty',
  blurDataURL,
  onLoad,
  onError,
  fallbackSrc = '/images/placeholder.svg',
  style,
  onLoadTime,
  aspectRatio,
  objectFit = 'cover',
  
  // New Priority Hints API support
  importance,
  
  // Network-aware quality adaptation
  isAboveFold = false,
  isCritical = false,
  index = 0,
  adaptiveQuality = true,
  
}: OptimizedImageProps) {
  const [currentSrc, setCurrentSrc] = useState(src)
  const [hasError, setHasError] = useState(false)
  const [loadStartTime] = useState(() => Date.now())
  const [adaptedQuality, setAdaptedQuality] = useState<number>(typeof quality === 'number' ? quality : IMAGE_QUALITY.DEFAULT)
  const [adaptedPlaceholder, setAdaptedPlaceholder] = useState(placeholder)

  // Network quality adapter
  const networkAdapter = NetworkQualityAdapter.getInstance()
  
  // Reset internal state when src changes
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('OptimizedImage: src changed to', src)
    }
    setCurrentSrc(src)
    setHasError(false)
  }, [src])

  // Apply network-aware quality adaptation
  useEffect(() => {
    if (!adaptiveQuality) {
      // Ensure we set the original quality as a number
      setAdaptedQuality(typeof quality === 'number' ? quality : IMAGE_QUALITY.DEFAULT)
      return
    }

    try {
      const qualitySettings = networkAdapter.getOptimalQuality(isAboveFold, isCritical)
      
      // Update quality based on network conditions - ensure it's a number
      const newQuality = typeof qualitySettings.imageQuality === 'number' 
        ? qualitySettings.imageQuality 
        : IMAGE_QUALITY.DEFAULT
        
      setAdaptedQuality(newQuality)
      
      // Enable blur placeholder on slow networks unless explicitly disabled
      if (qualitySettings.enableBlur && placeholder === 'empty') {
        setAdaptedPlaceholder('blur')
      }
      
      // Listen for network changes and re-adapt quality
      const cleanup = networkAdapter.onNetworkChange((networkInfo) => {
        try {
          const newSettings = networkAdapter.getOptimalQuality(isAboveFold, isCritical)
          const newQuality = typeof newSettings.imageQuality === 'number' 
            ? newSettings.imageQuality 
            : IMAGE_QUALITY.DEFAULT
          setAdaptedQuality(newQuality)
          
          if (newSettings.enableBlur && placeholder === 'empty') {
            setAdaptedPlaceholder('blur')
          }
        } catch (error) {
          console.warn('Error in network change handler:', error)
          setAdaptedQuality(IMAGE_QUALITY.DEFAULT)
        }
      })

      return cleanup
    } catch (error) {
      console.warn('Error in quality adaptation:', error)
      setAdaptedQuality(typeof quality === 'number' ? quality : IMAGE_QUALITY.DEFAULT)
    }
  }, [adaptiveQuality, isAboveFold, isCritical, networkAdapter, placeholder, quality])


  // 判断是否为站内静态图片（以 "/" 开头或以 window.location.origin 开头）
  const isLocal = currentSrc.startsWith('/') || (typeof window !== 'undefined' && currentSrc.startsWith(window.location.origin))

  // Determine Priority Hints API importance value
  const resolvedImportance = importance || getImagePriority(isAboveFold, isCritical, index)

  // fill 模式下若未指定 sizes，则默认 100vw 以消除警告
  const resolvedSizes = fill ? (sizes || '100vw') : sizes

  const handleError = () => {
    if (process.env.NODE_ENV === 'development') {
      console.error('OptimizedImage: Failed to load image', currentSrc)
    }
    
    // Record failure
    const monitor = ImagePerformanceMonitor.getInstance()
    monitor.recordFailure()
    
    if (currentSrc !== fallbackSrc) {
      setCurrentSrc(fallbackSrc)
    } else {
      setHasError(true)
    }
    onError?.()
  }

  if (hasError) {
    return <div className={cn('bg-gray-100', className)} style={{ width, height, ...style }}>Error</div>
  }

  // 中文注释：根据是否 fill 动态生成容器样式，确保容器有实际高度
  const wrapperStyle: React.CSSProperties = {
    ...style,
    // 如果调用方未显式传入 aspectRatio，则保持 auto；否则使用传入的值
    aspectRatio: aspectRatio || style?.aspectRatio || 'auto',
  }

  // fill=false 且显式给出宽高时，直接把值写进 style，避免外层容器高度为 0
  if (!fill && width && height) {
    wrapperStyle.width  = width
    wrapperStyle.height = height
  }

  // fill=true 且未指定 aspectRatio，通过默认 1/1 让父容器有高度，避免 0 高警告
  if (fill && !aspectRatio && !style?.aspectRatio) {
    wrapperStyle.aspectRatio = '1 / 1'
  }

  // fill=true 时让容器占满父级
  const containerClass = cn('relative', fill && 'w-full h-full')

  return (
    <div style={wrapperStyle} className={containerClass}>
      <Image
        {...(!isLocal && { loader: sanityImageLoader })}
        src={currentSrc}
        alt={alt}
        width={fill ? undefined : width}
        height={fill ? undefined : height}
        fill={fill}
        sizes={resolvedSizes}
        priority={priority}
        quality={getFinalQuality(adaptedQuality)}
        placeholder={adaptedPlaceholder}
        // Priority Hints API support
        {...(resolvedImportance && { 
          // @ts-ignore - Priority Hints API is not yet in TypeScript definitions
          importance: resolvedImportance 
        })}
        // 如果 placeholder=="blur" 且调用方未提供 blurDataURL，则使用 1×1 透明像素作为后备，避免 Next.js 报错
        blurDataURL={blurDataURL || (adaptedPlaceholder === 'blur' ? 'data:image/gif;base64,R0lGODlhAQABAIAAAP///wAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==' : undefined)}
        onLoad={() => {
          const loadTime = Date.now() - loadStartTime
          onLoadTime?.(loadTime)
          
          // Record performance metrics
          const monitor = ImagePerformanceMonitor.getInstance()
          monitor.recordLoadTime(loadTime)
          
          onLoad?.()
        }}
        onError={handleError}
        className={cn(className, fill && !className?.includes('object-') && `object-${objectFit}`)}
        style={{ ...style, ...(fill ? { objectFit } : {}) }}
      />
    </div>
  )
}

// Helper function to create responsive sizes string
export function createSizes(breakpoints: { [key: string]: string }) {
  return Object.entries(breakpoints)
    .map(([breakpoint, size]) => {
      if (breakpoint === 'default') return size
      return `(${breakpoint}) ${size}`
    })
    .join(', ')
}

// Predefined common sizes
export const imageSizes = {
  productCard: createSizes({
    '(max-width: 640px)': '100vw',
    '(max-width: 1024px)': '50vw',
    'default': '33vw'
  }),
  hero: createSizes({
    'default': '100vw'
  }),
  thumbnail: createSizes({
    'default': '150px'
  }),
  gallery: createSizes({
    '(max-width: 768px)': '100vw',
    'default': '50vw'
  })
}

// Image performance monitoring utility
export class ImagePerformanceMonitor {
  private static instance: ImagePerformanceMonitor
  private loadTimes: number[] = []
  private failures: number = 0
  private totalLoads: number = 0

  static getInstance(): ImagePerformanceMonitor {
    if (!ImagePerformanceMonitor.instance) {
      ImagePerformanceMonitor.instance = new ImagePerformanceMonitor()
    }
    return ImagePerformanceMonitor.instance
  }

  recordLoadTime(loadTime: number): void {
    this.loadTimes.push(loadTime)
    this.totalLoads++
    
    // Keep only last 100 measurements
    if (this.loadTimes.length > 100) {
      this.loadTimes.shift()
    }
    
    // Log slow loads in development
    if (process.env.NODE_ENV === 'development' && loadTime > 3000) {
      console.warn(`Slow image load detected: ${loadTime}ms`)
    }
  }

  recordFailure(): void {
    this.failures++
    this.totalLoads++
  }

  getAverageLoadTime(): number {
    if (this.loadTimes.length === 0) return 0
    return this.loadTimes.reduce((sum, time) => sum + time, 0) / this.loadTimes.length
  }

  getFailureRate(): number {
    if (this.totalLoads === 0) return 0
    return this.failures / this.totalLoads
  }

  getPerformanceReport(): {
    averageLoadTime: number
    failureRate: number
    totalLoads: number
    slowLoads: number
    strategyBreakdown: Record<string, {
      averageLoadTime: number
      failureRate: number
      totalLoads: number
      slowLoads: number
    }>
  } {
    const slowLoads = this.loadTimes.filter(time => time > 3000).length
    
    return {
      averageLoadTime: this.getAverageLoadTime(),
      failureRate: this.getFailureRate(),
      totalLoads: this.totalLoads,
      slowLoads,
      strategyBreakdown: {}
    }
  }


  reset(): void {
    this.loadTimes = []
    this.failures = 0
    this.totalLoads = 0
  }
}