'use client'

import { useState, useRef, useEffect, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import { useTranslations } from 'next-intl'

interface SearchBoxProps {
  locale?: string
  onClose?: () => void
  isFullScreen?: boolean
  placeholder?: string
}

interface SearchResult {
  id: string
  title: string
  type: 'product' | 'category' | 'page'
  url: string
  image?: string
  description?: string
  category?: string
  price?: string
}

interface SearchResponse {
  query: string
  results: SearchResult[]
  total: number
  locale: string
  error?: string
}

export function SearchBox({ 
  locale = 'zh', 
  onClose, 
  isFullScreen = false,
  placeholder 
}: SearchBoxProps) {
  const [query, setQuery] = useState('')
  const [isSearching, setIsSearching] = useState(false)
  const [results, setResults] = useState<SearchResult[]>([])
  const [selectedIndex, setSelectedIndex] = useState(-1)
  const [error, setError] = useState<string | null>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  const router = useRouter()
  const abortControllerRef = useRef<AbortController | null>(null)

  // 实际搜索函数
  const performSearch = useCallback(async (searchQuery: string) => {
    if (!searchQuery.trim() || searchQuery.length < 2) {
      setResults([])
      return
    }

    // 取消前一个请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    setIsSearching(true)
    setError(null)
    
    // 创建新的 AbortController
    abortControllerRef.current = new AbortController()

    try {
      const searchParams = new URLSearchParams({
        q: searchQuery,
        locale: locale
      })

      const response = await fetch(`/api/search?${searchParams}`, {
        signal: abortControllerRef.current.signal
      })

      if (!response.ok) {
        throw new Error(`${response.status}`)
      }

      const data: SearchResponse = await response.json()
      
      if (data.error) {
        setError(data.error)
        setResults([])
      } else {
        setResults(data.results)
        setSelectedIndex(-1)
      }
    } catch (error) {
      if (error instanceof Error && error.name !== 'AbortError') {
        console.error('Search error:', error)
        // Error message will be set using translations in the component
        setError('SEARCH_SERVICE_UNAVAILABLE')
        setResults([])
      }
    } finally {
      setIsSearching(false)
    }
  }, [locale])

  const handleSearch = useCallback(() => {
    if (query.trim()) {
      router.push(`/${locale}/products?search=${encodeURIComponent(query)}`)
      onClose?.()
    }
  }, [query, router, onClose, locale])

  const handleResultClick = useCallback((result: SearchResult) => {
    router.push(result.url)
    onClose?.()
  }, [router, onClose])

  // 防抖搜索
  useEffect(() => {
    if (!query.trim()) {
      setResults([])
      setError(null)
      return
    }

    const timeoutId = setTimeout(() => {
      performSearch(query)
    }, 300)

    return () => clearTimeout(timeoutId)
  }, [query, performSearch])

  // 清理 AbortController
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [])

  // 键盘导航处理
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!results.length) return

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault()
          setSelectedIndex(prev => (prev < results.length - 1 ? prev + 1 : 0))
          break
        case 'ArrowUp':
          e.preventDefault()
          setSelectedIndex(prev => (prev > 0 ? prev - 1 : results.length - 1))
          break
        case 'Enter':
          e.preventDefault()
          if (selectedIndex >= 0 && selectedIndex < results.length) {
            handleResultClick(results[selectedIndex])
          } else if (query.trim()) {
            handleSearch()
          }
          break
        case 'Escape':
          e.preventDefault()
          onClose?.()
          break
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [results, selectedIndex, query, onClose, handleResultClick, handleSearch])

  // 自动聚焦输入框
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [])

  const getResultIcon = (type: string) => {
    switch (type) {
      case 'product':
        return '🛍️'
      case 'category':
        return '📂'
      case 'page':
        return '📄'
      default:
        return '🔍'
    }
  }

  const t = useTranslations('SearchBox')
  
  const getPlaceholder = () => {
    if (placeholder) return placeholder
    return t('placeholder')
  }
  
  const getErrorMessage = (errorKey: string) => {
    if (errorKey === 'SEARCH_SERVICE_UNAVAILABLE') {
      return t('serviceUnavailable')
    }
    return errorKey
  }

  if (isFullScreen) {
    return (
      <motion.div
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-start justify-center pt-20"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={onClose}
      >
        <motion.div
          className="bg-white rounded-2xl shadow-2xl w-full max-w-2xl mx-4 max-h-[70vh] flex flex-col"
          initial={{ opacity: 0, y: -50, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: -50, scale: 0.95 }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* 搜索头部 */}
          <div className="p-6 border-b border-gray-200">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                ref={inputRef}
                type="text"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                placeholder={getPlaceholder()}
                className="w-full pl-12 pr-12 py-4 text-lg border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:outline-none transition-colors"
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && selectedIndex === -1) {
                    e.preventDefault()
                    handleSearch()
                  }
                }}
              />
              {query && (
                <button
                  onClick={() => setQuery('')}
                  className="absolute inset-y-0 right-0 pr-4 flex items-center"
                >
                  <svg className="h-5 w-5 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              )}
            </div>
          </div>

          {/* 搜索结果 */}
          <div className="flex-1 overflow-y-auto">
            <AnimatePresence>
              {isSearching && (
                <motion.div
                  className="p-6 text-center"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                >
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-2 text-gray-500">
                    {t('searching')}
                  </p>
                </motion.div>
              )}

              {error && (
                <motion.div
                  className="p-6 text-center"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                >
                  <div className="text-6xl mb-4">⚠️</div>
                  <h3 className="text-lg font-semibold text-red-600 mb-2">
                    {t('searchError')}
                  </h3>
                  <p className="text-gray-500">{getErrorMessage(error)}</p>
                </motion.div>
              )}

              {!isSearching && !error && query && results.length === 0 && (
                <motion.div
                  className="p-6 text-center"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                >
                  <div className="text-6xl mb-4">🔍</div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {t('noResults')}
                  </h3>
                  <p className="text-gray-500">
                    {t('tryDifferentKeywords')}
                  </p>
                </motion.div>
              )}

              {!isSearching && !error && results.length > 0 && (
                <motion.div
                  className="py-2"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                >
                  {results.map((result, index) => (
                    <motion.button
                      key={result.id}
                      className={`w-full p-4 text-left hover:bg-gray-50 border-l-4 transition-colors ${
                        selectedIndex === index 
                          ? 'bg-blue-50 border-blue-500' 
                          : 'border-transparent'
                      }`}
                      onClick={() => handleResultClick(result)}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.05 }}
                    >
                      <div className="flex items-center gap-4">
                        {result.image ? (
                          <Image 
                            src={result.image} 
                            alt={result.title}
                            width={48}
                            height={48}
                            className="w-12 h-12 rounded-lg object-cover flex-shrink-0"
                          />
                        ) : (
                          <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0">
                            <span className="text-xl">{getResultIcon(result.type)}</span>
                          </div>
                        )}
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="font-semibold text-gray-900 truncate">{result.title}</h4>
                            {result.price && (
                              <span className="text-blue-600 font-bold">{result.price}</span>
                            )}
                          </div>
                          {result.description && (
                            <p className="text-sm text-gray-500 truncate">{result.description}</p>
                          )}
                          {result.category && (
                            <span className="inline-block mt-1 px-2 py-1 bg-gray-100 text-xs rounded-full">
                              {result.category}
                            </span>
                          )}
                        </div>
                        
                        <svg className="w-5 h-5 text-gray-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </div>
                    </motion.button>
                  ))}
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* 搜索页脚 */}
          <div className="p-4 border-t border-gray-200 bg-gray-50 rounded-b-2xl">
            <div className="flex items-center justify-between text-sm text-gray-500">
              <div className="flex items-center gap-4">
                <span className="flex items-center gap-1">
                  <kbd className="px-2 py-1 bg-white border border-gray-200 rounded">↑↓</kbd>
                  {t('navigate')}
                </span>
                <span className="flex items-center gap-1">
                  <kbd className="px-2 py-1 bg-white border border-gray-200 rounded">↵</kbd>
                  {t('select')}
                </span>
                <span className="flex items-center gap-1">
                  <kbd className="px-2 py-1 bg-white border border-gray-200 rounded">ESC</kbd>
                  {t('close')}
                </span>
              </div>
              {query && !error && (
                <button
                  onClick={handleSearch}
                  className="text-blue-600 hover:text-blue-700 font-medium"
                >
                  {t('viewAllResults')}
                </button>
              )}
            </div>
          </div>
        </motion.div>
      </motion.div>
    )
  }

  // 内联搜索框
  return (
    <div className="relative">
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder={getPlaceholder()}
          className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all"
        />
      </div>

      {/* 下拉结果 */}
      <AnimatePresence>
        {query && (isSearching || results.length > 0 || error) && (
          <motion.div
            className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-64 overflow-y-auto"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
          >
            {isSearching ? (
              <div className="p-4 text-center">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
              </div>
            ) : error ? (
              <div className="p-4 text-center text-red-500">
                <p className="text-sm">{getErrorMessage(error)}</p>
              </div>
            ) : (
              results.map((result, index) => (
                <button
                  key={result.id}
                  className={`w-full p-3 text-left hover:bg-gray-50 border-b border-gray-100 last:border-b-0 transition-colors ${
                    selectedIndex === index ? 'bg-blue-50' : ''
                  }`}
                  onClick={() => handleResultClick(result)}
                >
                  <div className="flex items-center gap-3">
                    <span className="text-lg">{getResultIcon(result.type)}</span>
                    <div>
                      <div className="font-medium text-gray-900">{result.title}</div>
                      {result.description && (
                        <div className="text-sm text-gray-500">{result.description}</div>
                      )}
                    </div>
                  </div>
                </button>
              ))
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}