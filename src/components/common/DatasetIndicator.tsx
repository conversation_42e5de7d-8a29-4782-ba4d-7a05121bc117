'use client'

import { useEffect, useState } from 'react'
import { currentDataset } from '@/lib/sanity/client'

export default function DatasetIndicator() {
  const [isStaging, setIsStaging] = useState(false)
  const [dataset, setDataset] = useState<string>('')

  useEffect(() => {
    // Only show in development or when explicitly enabled
    const showIndicator = 
      process.env.NODE_ENV !== 'production' || 
      process.env.NEXT_PUBLIC_SHOW_DATASET_INDICATOR === 'true'
    
    if (showIndicator && currentDataset !== 'production') {
      setIsStaging(true)
      setDataset(currentDataset)
    }
  }, [])

  if (!isStaging) return null

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div className="bg-yellow-500 text-black px-4 py-2 rounded-lg shadow-lg flex items-center space-x-2">
        <svg 
          className="w-5 h-5" 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" 
          />
        </svg>
        <span className="font-semibold">
          Staging: {dataset}
        </span>
      </div>
    </div>
  )
}