export function ContactPageContentSkeleton() {
  return (
    <div className="container mx-auto px-4 py-16 animate-pulse">
      <div className="max-w-6xl mx-auto">
        {/* Hero Section Skeleton */}
        <div className="text-center mb-16">
          <div className="h-10 bg-gray-200 rounded-lg w-96 mx-auto mb-4" />
          <div className="h-6 bg-gray-200 rounded-lg max-w-2xl mx-auto" />
        </div>

        <div className="grid lg:grid-cols-2 gap-16">
          {/* Contact Information Skeleton */}
          <div>
            <div className="h-8 bg-gray-200 rounded-lg w-48 mb-8" />
            
            <div className="space-y-6">
              {/* Address */}
              <div className="flex items-start space-x-4">
                <div className="w-6 h-6 bg-gray-200 rounded-full" />
                <div className="flex-1">
                  <div className="h-5 bg-gray-200 rounded w-24 mb-2" />
                  <div className="h-4 bg-gray-200 rounded w-48" />
                </div>
              </div>

              {/* Phone */}
              <div className="flex items-start space-x-4">
                <div className="w-6 h-6 bg-gray-200 rounded-full" />
                <div className="flex-1">
                  <div className="h-5 bg-gray-200 rounded w-24 mb-2" />
                  <div className="h-4 bg-gray-200 rounded w-36" />
                </div>
              </div>

              {/* Email */}
              <div className="flex items-start space-x-4">
                <div className="w-6 h-6 bg-gray-200 rounded-full" />
                <div className="flex-1">
                  <div className="h-5 bg-gray-200 rounded w-24 mb-2" />
                  <div className="h-4 bg-gray-200 rounded w-40" />
                </div>
              </div>

              {/* Working Hours */}
              <div className="flex items-start space-x-4">
                <div className="w-6 h-6 bg-gray-200 rounded-full" />
                <div className="flex-1">
                  <div className="h-5 bg-gray-200 rounded w-32 mb-2" />
                  <div className="h-4 bg-gray-200 rounded w-44" />
                </div>
              </div>
            </div>

            {/* Social Links Skeleton */}
            <div className="mt-8">
              <div className="h-5 bg-gray-200 rounded w-24 mb-4" />
              <div className="flex space-x-4">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="flex items-center space-x-2">
                    <div className="w-5 h-5 bg-gray-200 rounded" />
                    <div className="h-4 bg-gray-200 rounded w-16" />
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Contact Form Skeleton */}
          <div>
            <div className="h-8 bg-gray-200 rounded-lg w-36 mb-4" />
            <div className="h-5 bg-gray-200 rounded-lg w-full mb-8" />

            <div className="space-y-6">
              {/* Form fields */}
              {[1, 2, 3].map((i) => (
                <div key={i}>
                  <div className="h-4 bg-gray-200 rounded w-20 mb-2" />
                  <div className="h-10 bg-gray-200 rounded-md w-full" />
                </div>
              ))}

              {/* Textarea */}
              <div>
                <div className="h-4 bg-gray-200 rounded w-20 mb-2" />
                <div className="h-32 bg-gray-200 rounded-md w-full" />
              </div>

              {/* Submit button */}
              <div className="h-12 bg-gray-200 rounded-md w-full" />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}