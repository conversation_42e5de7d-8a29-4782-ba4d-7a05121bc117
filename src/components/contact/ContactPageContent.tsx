'use client'

import { ContactPage, LocaleString, LocaleText } from '@/types/sanity'
import Image from 'next/image'
import { useState } from 'react'
import { resolveImageUrl, resolveImageAlt } from '@/lib/sanity/image'
import { useTranslations } from 'next-intl'
import PortableText from '@/components/ui/PortableText'

interface ContactPageContentProps {
  data: ContactPage
  locale: string
}

// Helper function to get localized content
function getLocalizedContent(
  content: LocaleString | LocaleText | undefined, 
  locale: string
): string | any[] | undefined {
  if (!content) return undefined
  return content[locale as keyof typeof content] || content.zh || content.en
}

// Helper function for secure image URLs
function getSecureImageUrl(imageData: any, width?: number, height?: number): string {
  // 中文注释：统一使用 resolveImageUrl 解析图片
  return resolveImageUrl(imageData, width, height)
}

export default function ContactPageContent({ data, locale }: ContactPageContentProps) {
  const t = useTranslations('Actions')
  
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle')

  const title = getLocalizedContent(data.title, locale)
  const heroHeadline = getLocalizedContent(data.heroSection?.headline, locale)
  const heroSubtitle = getLocalizedContent(data.heroSection?.subtitle, locale)

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    
    // Simulate form submission (replace with actual API call)
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))
      setSubmitStatus('success')
      setFormData({ name: '', email: '', subject: '', message: '' })
    } catch (error) {
      setSubmitStatus('error')
    } finally {
      setIsSubmitting(false)
    }
  }

  const getSocialIcon = (platform: string) => {
    const iconClass = "w-5 h-5"
    switch (platform) {
      case 'wechat':
        return <div className={iconClass + " bg-green-500 rounded"} />
      case 'weibo':
        return <div className={iconClass + " bg-red-500 rounded"} />
      case 'facebook':
        return <div className={iconClass + " bg-blue-600 rounded"} />
      case 'twitter':
        return <div className={iconClass + " bg-blue-400 rounded"} />
      case 'instagram':
        return <div className={iconClass + " bg-gradient-to-r from-purple-500 to-pink-500 rounded"} />
      case 'linkedin':
        return <div className={iconClass + " bg-blue-700 rounded"} />
      case 'youtube':
        return <div className={iconClass + " bg-red-600 rounded"} />
      default:
        return <div className={iconClass + " bg-gray-500 rounded"} />
    }
  }

  return (
    <div className="container mx-auto px-4 py-16">
      <div className="max-w-6xl mx-auto">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {heroHeadline || title}
          </h1>
          {heroSubtitle && (
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              {heroSubtitle}
            </p>
          )}
        </div>

        <div className="grid lg:grid-cols-2 gap-16">
          {/* Contact Information */}
          <div>
            {data.contactInfo?.sectionTitle && (
              <h2 className="text-2xl font-semibold mb-8">
                {getLocalizedContent(data.contactInfo.sectionTitle, locale)}
              </h2>
            )}
            
            <div className="space-y-6">
              {data.contactInfo?.address && (
                <div className="flex items-start space-x-4">
                  <div className="w-6 h-6 text-blue-600 mt-1">
                    <svg fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-semibold mb-1">
                      {getLocalizedContent(data.contactInfo.address.label, locale)}
                    </h3>
                    <p className="text-gray-600">
                      {getLocalizedContent(data.contactInfo.address.value, locale)}
                    </p>
                  </div>
                </div>
              )}

              {data.contactInfo?.phone && (
                <div className="flex items-start space-x-4">
                  <div className="w-6 h-6 text-blue-600 mt-1">
                    <svg fill="currentColor" viewBox="0 0 20 20">
                      <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-semibold mb-1">
                      {getLocalizedContent(data.contactInfo.phone.label, locale)}
                    </h3>
                    <p className="text-gray-600">
                      {data.contactInfo.phone.value}
                    </p>
                  </div>
                </div>
              )}

              {data.contactInfo?.email && (
                <div className="flex items-start space-x-4">
                  <div className="w-6 h-6 text-blue-600 mt-1">
                    <svg fill="currentColor" viewBox="0 0 20 20">
                      <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                      <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-semibold mb-1">
                      {getLocalizedContent(data.contactInfo.email.label, locale)}
                    </h3>
                    <p className="text-gray-600">
                      {data.contactInfo.email.value}
                    </p>
                  </div>
                </div>
              )}

              {data.contactInfo?.workingHours && (
                <div className="flex items-start space-x-4">
                  <div className="w-6 h-6 text-blue-600 mt-1">
                    <svg fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-semibold mb-1">
                      {getLocalizedContent(data.contactInfo.workingHours.label, locale)}
                    </h3>
                    <p className="text-gray-600">
                      {getLocalizedContent(data.contactInfo.workingHours.value, locale)}
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Social Links */}
            {data.socialLinks && data.socialLinks.length > 0 && (
              <div className="mt-8">
                <h3 className="font-semibold mb-4">
                  {t('followUs')}
                </h3>
                <div className="flex space-x-4">
                  {data.socialLinks.map((link, index) => (
                    <a
                      key={index}
                      href={link.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors"
                      title={(() => {
                        const label = getLocalizedContent(link.label, locale)
                        return Array.isArray(label) ? '' : label || ''
                      })()}
                    >
                      {link.icon ? (
                        <Image
                          src={getSecureImageUrl(link.icon, 20, 20)}
                          alt={link.platform}
                          width={20}
                          height={20}
                          className="w-5 h-5"
                        />
                      ) : (
                        getSocialIcon(link.platform)
                      )}
                      <span>{getLocalizedContent(link.label, locale)}</span>
                    </a>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Contact Form */}
          <div>
            {data.contactForm?.formTitle && (
              <h2 className="text-2xl font-semibold mb-4">
                {getLocalizedContent(data.contactForm.formTitle, locale)}
              </h2>
            )}
            
            {data.contactForm?.formDescription && (() => {
              const desc = getLocalizedContent(data.contactForm.formDescription, locale)
              return Array.isArray(desc) ? (
                <div className="mb-8">
                  <PortableText value={desc} className="text-gray-600" />
                </div>
              ) : (
                <p className="text-gray-600 mb-8">
                  {desc}
                </p>
              )
            })()}

            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                  {getLocalizedContent(data.contactForm?.nameLabel, locale) || '姓名'}
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                  {getLocalizedContent(data.contactForm?.emailLabel, locale) || '邮箱'}
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-2">
                  {getLocalizedContent(data.contactForm?.subjectLabel, locale) || '主题'}
                </label>
                <input
                  type="text"
                  id="subject"
                  name="subject"
                  value={formData.subject}
                  onChange={handleInputChange}
                  required
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                  {getLocalizedContent(data.contactForm?.messageLabel, locale) || '消息'}
                </label>
                <textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  required
                  rows={5}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <button
                type="submit"
                disabled={isSubmitting}
                className="w-full bg-blue-600 text-white py-3 px-6 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50"
              >
                {isSubmitting 
                  ? '发送中...' 
                  : getLocalizedContent(data.contactForm?.submitButtonText, locale) || '发送消息'
                }
              </button>

              {submitStatus === 'success' && (
                <div className="p-4 bg-green-50 border border-green-200 rounded-md">
                  <p className="text-green-700">
                    {getLocalizedContent(data.contactForm?.successMessage, locale) || '消息发送成功！'}
                  </p>
                </div>
              )}

              {submitStatus === 'error' && (
                <div className="p-4 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-red-700">
                    {getLocalizedContent(data.contactForm?.errorMessage, locale) || '发送失败，请重试。'}
                  </p>
                </div>
              )}
            </form>
          </div>
        </div>

        {/* Map Section */}
        {data.mapSection?.showMap && (
          <div className="mt-16">
            {data.mapSection.mapTitle && (
              <h2 className="text-2xl font-semibold mb-8 text-center">
                {getLocalizedContent(data.mapSection.mapTitle, locale)}
              </h2>
            )}
            
            <div className="bg-gray-200 h-96 rounded-lg flex items-center justify-center">
              <div className="text-center text-gray-600">
                <div className="w-16 h-16 mx-auto mb-4 text-gray-400">
                  <svg fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                  </svg>
                </div>
                <p>地图将在此处显示</p>
                {data.mapSection.mapDescription && (
                  <p className="mt-2 text-sm">
                    {getLocalizedContent(data.mapSection.mapDescription, locale)}
                  </p>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}