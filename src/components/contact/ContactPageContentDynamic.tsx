'use client'

import dynamic from 'next/dynamic'
import { ContactPageContentSkeleton } from './ContactPageContentSkeleton'
import type { ContactPage } from '@/types/sanity'

// Dynamically import ContactPageContent to reduce initial bundle size
// Contact form functionality is often used conditionally
const ContactPageContent = dynamic(
  () => import('./ContactPageContent'),
  {
    ssr: true, // Keep SSR for SEO as contact pages should be indexed
    loading: () => <ContactPageContentSkeleton />,
  }
)

interface ContactPageContentDynamicProps {
  data: ContactPage
  locale: string
}

export function ContactPageContentDynamic(props: ContactPageContentDynamicProps) {
  return <ContactPageContent {...props} />
}

export default ContactPageContentDynamic