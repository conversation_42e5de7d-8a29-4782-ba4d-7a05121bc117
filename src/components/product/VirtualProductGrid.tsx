'use client';

import { useState, useEffect, useMemo, useCallback } from 'react';
import { FixedSizeGrid as Grid } from 'react-window';
import { useIntersectionObserver } from '@/hooks/useIntersectionObserver';
import ProductCard from './ProductCard';
import { ProductImageSkeleton } from '@/components/ui/LazyImage';
import { Product } from '@/types/sanity';
import { cn } from '@/lib/utils';

interface VirtualProductGridProps {
  products: Product[];
  locale: string;
  className?: string;
  /**
   * Number of columns for different screen sizes
   */
  columns?: {
    mobile: number;
    tablet: number;
    desktop: number;
  };
  /**
   * Enable virtual scrolling for large lists
   * @default true for lists > 20 items
   */
  enableVirtualization?: boolean;
  /**
   * Loading state
   */
  loading?: boolean;
  /**
   * Number of skeleton items to show when loading
   */
  skeletonCount?: number;
}

interface CellProps {
  columnIndex: number;
  rowIndex: number;
  style: React.CSSProperties;
  data: {
    products: Product[];
    locale: string;
    columnsCount: number;
    itemWidth: number;
    gap: number;
  };
}

// Virtual grid cell component
function VirtualGridCell({ columnIndex, rowIndex, style, data }: CellProps) {
  const { products, locale, columnsCount, itemWidth, gap } = data;
  const index = rowIndex * columnsCount + columnIndex;
  const product = products[index];

  if (!product) {
    return <div style={style} />;
  }

  return (
    <div
      style={{
        ...style,
        left: `${parseFloat(style.left as string) + gap / 2}px`,
        top: `${parseFloat(style.top as string) + gap / 2}px`,
        width: `${itemWidth - gap}px`,
        height: `${parseFloat(style.height as string) - gap}px`,
      }}
    >
      <ProductCard
        product={product}
        locale={locale}
        index={index}
        priority={index < 6} // First 6 items get priority loading
      />
    </div>
  );
}

// Skeleton grid component
function SkeletonGrid({ 
  count, 
  columns, 
  className 
}: { 
  count: number; 
  columns: number; 
  className?: string; 
}) {
  const skeletonItems = Array.from({ length: count }, (_, i) => (
    <div key={i} className="animate-pulse">
      <ProductImageSkeleton className="w-full mb-4" aspectRatio="square" />
      <div className="space-y-2">
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4" />
        <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2" />
        <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/4" />
      </div>
    </div>
  ));

  return (
    <div 
      className={cn(
        'grid gap-6',
        {
          'grid-cols-1': columns === 1,
          'grid-cols-2': columns === 2,
          'grid-cols-3': columns === 3,
          'grid-cols-4': columns === 4,
          'grid-cols-5': columns === 5,
          'grid-cols-6': columns === 6,
        },
        className
      )}
    >
      {skeletonItems}
    </div>
  );
}

export default function VirtualProductGrid({
  products,
  locale,
  className,
  columns = { mobile: 2, tablet: 3, desktop: 4 },
  enableVirtualization,
  loading = false,
  skeletonCount = 12,
}: VirtualProductGridProps) {
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });
  const [currentColumns, setCurrentColumns] = useState(columns.desktop);
  
  const { ref: containerRef, inView } = useIntersectionObserver<HTMLDivElement>({
    triggerOnce: false,
    threshold: 0.1,
  });

  // Determine if virtualization should be enabled
  const shouldVirtualize = useMemo(() => {
    if (enableVirtualization !== undefined) return enableVirtualization;
    return products.length > 20;
  }, [enableVirtualization, products.length]);

  // Update columns based on screen size
  useEffect(() => {
    const updateColumns = () => {
      const width = window.innerWidth;
      if (width < 768) {
        setCurrentColumns(columns.mobile);
      } else if (width < 1024) {
        setCurrentColumns(columns.tablet);
      } else {
        setCurrentColumns(columns.desktop);
      }
    };

    updateColumns();
    window.addEventListener('resize', updateColumns);
    return () => window.removeEventListener('resize', updateColumns);
  }, [columns]);

  // Update container size
  useEffect(() => {
    const updateSize = () => {
      if (containerRef.current) {
        const { offsetWidth } = containerRef.current;
        setContainerSize({
          width: offsetWidth,
          height: window.innerHeight, // Use viewport height for virtual scrolling
        });
      }
    };

    updateSize();
    window.addEventListener('resize', updateSize);
    return () => window.removeEventListener('resize', updateSize);
  }, [containerRef]);

  // Calculate grid dimensions
  const gap = 24; // 6 * 4px (gap-6 in Tailwind)
  const itemWidth = useMemo(() => {
    if (containerSize.width === 0) return 0;
    return (containerSize.width - gap * (currentColumns - 1)) / currentColumns;
  }, [containerSize.width, currentColumns, gap]);

  const itemHeight = useMemo(() => {
    // Approximate height: square image + text content
    return itemWidth + 120; // 120px for text content
  }, [itemWidth]);

  const rowCount = Math.ceil(products.length / currentColumns);

  // Grid data for virtual scrolling
  const gridData = useMemo(() => ({
    products,
    locale,
    columnsCount: currentColumns,
    itemWidth,
    gap,
  }), [products, locale, currentColumns, itemWidth, gap]);

  // Loading state
  if (loading) {
    return (
      <div ref={containerRef} className={className}>
        <SkeletonGrid 
          count={skeletonCount} 
          columns={currentColumns}
          className="animate-pulse"
        />
      </div>
    );
  }

  // Empty state
  if (products.length === 0) {
    return (
      <div className={cn(
        'flex flex-col items-center justify-center py-12 text-center',
        className
      )}>
        <div className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-full mb-4 flex items-center justify-center">
          <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
          No products found
        </h3>
        <p className="text-gray-500 dark:text-gray-400">
          Try adjusting your search or filter criteria
        </p>
      </div>
    );
  }

  // Non-virtualized grid for small lists
  if (!shouldVirtualize || containerSize.width === 0) {
    return (
      <div 
        ref={containerRef}
        className={cn(
          'grid gap-6',
          {
            'grid-cols-1': currentColumns === 1,
            'grid-cols-2': currentColumns === 2,
            'grid-cols-3': currentColumns === 3,
            'grid-cols-4': currentColumns === 4,
            'grid-cols-5': currentColumns === 5,
            'grid-cols-6': currentColumns === 6,
          },
          className
        )}
      >
        {products.map((product, index) => (
          <ProductCard
            key={product._id}
            product={product}
            locale={locale}
            index={index}
            priority={index < 6} // First 6 items get priority loading
          />
        ))}
      </div>
    );
  }

  // Virtualized grid for large lists
  return (
    <div ref={containerRef} className={className}>
      {containerSize.width > 0 && (
        <Grid
          columnCount={currentColumns}
          columnWidth={itemWidth}
          height={Math.min(800, containerSize.height * 0.8)} // Max height or 80% of viewport
          rowCount={rowCount}
          rowHeight={itemHeight}
          width={containerSize.width}
          itemData={gridData}
          overscanRowCount={2} // Render 2 extra rows for smoother scrolling
          className="scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600"
        >
          {VirtualGridCell}
        </Grid>
      )}
      
      {/* Performance indicator for development */}
      {/* @ts-ignore */}
      {process.env.NODE_ENV === 'development' && (
        <div className="mt-4 text-xs text-gray-500 text-center">
          Virtual Grid: {products.length} items, {rowCount} rows, {currentColumns} columns
        </div>
      )}
    </div>
  );
}

// Hook for managing product loading with pagination
export function useProductPagination(
  allProducts: Product[],
  pageSize: number = 20
) {
  const [currentPage, setCurrentPage] = useState(1);
  const [loadedProducts, setLoadedProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);

  const hasMore = loadedProducts.length < allProducts.length;

  const loadMore = useCallback(async () => {
    if (loading || !hasMore) return;

    setLoading(true);
    
    // Simulate network delay for demonstration
    await new Promise(resolve => setTimeout(resolve, 300));
    
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const newProducts = allProducts.slice(startIndex, endIndex);
    
    setLoadedProducts(prev => [...prev, ...newProducts]);
    setCurrentPage(prev => prev + 1);
    setLoading(false);
  }, [allProducts, currentPage, pageSize, loading, hasMore]);

  // Initial load
  useEffect(() => {
    if (loadedProducts.length === 0 && allProducts.length > 0) {
      loadMore();
    }
  }, [allProducts, loadedProducts.length, loadMore]);

  return {
    products: loadedProducts,
    loading,
    hasMore,
    loadMore,
  };
}