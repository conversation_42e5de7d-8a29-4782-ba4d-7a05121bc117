'use client'

interface ProductFilterSkeletonProps {
  className?: string
}

export function ProductFilterSkeleton({ className = '' }: ProductFilterSkeletonProps) {
  return (
    <div className={`product-filter-skeleton ${className}`}>
      {/* Mobile filter button skeleton */}
      <div className="lg:hidden mb-4">
        <div className="flex items-center gap-2 px-4 py-2 bg-gray-100 border border-gray-200 rounded-lg animate-pulse">
          <div className="w-5 h-5 bg-gray-300 rounded"></div>
          <div className="w-12 h-4 bg-gray-300 rounded"></div>
        </div>
      </div>

      {/* Filter panel skeleton */}
      <div className="bg-white border border-gray-200 rounded-lg p-4 mb-6 lg:mb-0 animate-pulse">
        {/* Header skeleton */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <div className="w-16 h-5 bg-gray-300 rounded"></div>
            <div className="w-4 h-4 bg-gray-200 rounded-full"></div>
          </div>
          <div className="w-20 h-4 bg-gray-200 rounded"></div>
        </div>

        {/* Sort section skeleton */}
        <div className="mb-6">
          <div className="flex items-center justify-between w-full mb-3">
            <div className="w-12 h-4 bg-gray-300 rounded"></div>
            <div className="w-5 h-5 bg-gray-200 rounded"></div>
          </div>
          <div className="space-y-2">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="flex items-center">
                <div className="w-4 h-4 bg-gray-200 rounded"></div>
                <div className="ml-2 w-20 h-3 bg-gray-200 rounded"></div>
              </div>
            ))}
          </div>
        </div>

        {/* Categories section skeleton */}
        <div className="mb-6">
          <div className="flex items-center justify-between w-full mb-3">
            <div className="w-20 h-4 bg-gray-300 rounded"></div>
            <div className="w-5 h-5 bg-gray-200 rounded"></div>
          </div>
          <div className="space-y-2">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="w-4 h-4 bg-gray-200 rounded"></div>
                  <div className="ml-2 w-16 h-3 bg-gray-200 rounded"></div>
                </div>
                <div className="w-8 h-3 bg-gray-100 rounded"></div>
              </div>
            ))}
          </div>
        </div>

        {/* Price range section skeleton */}
        <div className="mb-6">
          <div className="flex items-center justify-between w-full mb-3">
            <div className="w-24 h-4 bg-gray-300 rounded"></div>
            <div className="w-5 h-5 bg-gray-200 rounded"></div>
          </div>
          <div className="space-y-3">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="flex items-center">
                <div className="w-4 h-4 bg-gray-200 rounded"></div>
                <div className="ml-2 w-12 h-3 bg-gray-200 rounded"></div>
              </div>
            ))}
            <div className="pt-2 border-t border-gray-200">
              <div className="grid grid-cols-2 gap-2">
                <div className="h-8 bg-gray-100 border border-gray-200 rounded"></div>
                <div className="h-8 bg-gray-100 border border-gray-200 rounded"></div>
              </div>
            </div>
          </div>
        </div>

        {/* Stock status section skeleton */}
        <div className="mb-6">
          <div className="flex items-center justify-between w-full mb-3">
            <div className="w-20 h-4 bg-gray-300 rounded"></div>
            <div className="w-5 h-5 bg-gray-200 rounded"></div>
          </div>
          <div className="space-y-2">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="w-4 h-4 bg-gray-200 rounded"></div>
                  <div className="ml-2 w-14 h-3 bg-gray-200 rounded"></div>
                </div>
                <div className="w-6 h-3 bg-gray-100 rounded"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

export default ProductFilterSkeleton