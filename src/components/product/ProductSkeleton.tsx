interface ProductSkeletonProps {
  variant?: 'card' | 'list' | 'detail';
  count?: number;
}

export function ProductSkeleton({ variant = 'card', count = 1 }: ProductSkeletonProps) {
  const skeletons = Array.from({ length: count }, (_, index) => (
    <div key={index} className="animate-pulse">
      {variant === 'card' && <ProductCardSkeleton />}
      {variant === 'list' && <ProductListSkeleton />}
      {variant === 'detail' && <ProductDetailSkeleton />}
    </div>
  ));

  return <>{skeletons}</>;
}

function ProductCardSkeleton() {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      {/* Image placeholder */}
      <div className="w-full aspect-square sm:h-48 md:h-52 lg:h-48 bg-gray-200" />
      
      <div className="p-3 sm:p-4 space-y-3">
        {/* Title */}
        <div className="h-4 sm:h-5 bg-gray-200 rounded w-3/4" />
        
        {/* Description - hidden on small screens */}
        <div className="hidden sm:block space-y-2">
          <div className="h-3 sm:h-4 bg-gray-200 rounded w-full" />
          <div className="h-3 sm:h-4 bg-gray-200 rounded w-2/3" />
        </div>
        
        {/* Price and button */}
        <div className="flex items-center justify-between pt-2">
          <div className="h-5 sm:h-6 bg-gray-200 rounded w-16 sm:w-20" />
          <div className="h-7 sm:h-8 bg-gray-200 rounded w-20 sm:w-24" />
        </div>
      </div>
    </div>
  );
}

function ProductListSkeleton() {
  return (
    <div className="flex gap-3 sm:gap-4 p-3 sm:p-4 border-b border-gray-200">
      {/* Image */}
      <div className="w-16 h-16 sm:w-20 sm:h-20 bg-gray-200 rounded flex-shrink-0" />
      
      <div className="flex-1 space-y-2 sm:space-y-3 min-w-0">
        {/* Title */}
        <div className="h-4 sm:h-5 bg-gray-200 rounded w-2/3" />
        
        {/* Description - responsive */}
        <div className="h-3 sm:h-4 bg-gray-200 rounded w-full" />
        <div className="hidden sm:block h-3 sm:h-4 bg-gray-200 rounded w-4/5" />
        
        {/* Price */}
        <div className="h-4 sm:h-5 bg-gray-200 rounded w-20 sm:w-24" />
      </div>
    </div>
  );
}

function ProductDetailSkeleton() {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8">
      {/* Image gallery */}
      <div className="space-y-3 sm:space-y-4">
        <div className="w-full aspect-square sm:h-80 md:h-96 bg-gray-200 rounded" />
        <div className="flex gap-2 overflow-x-auto pb-2">
          {Array.from({ length: 4 }).map((_, i) => (
            <div key={i} className="w-16 h-16 sm:w-20 sm:h-20 bg-gray-200 rounded flex-shrink-0" />
          ))}
        </div>
      </div>
      
      {/* Product info */}
      <div className="space-y-4 sm:space-y-6">
        {/* Title */}
        <div className="h-6 sm:h-8 bg-gray-200 rounded w-3/4" />
        
        {/* Price */}
        <div className="h-8 sm:h-10 bg-gray-200 rounded w-24 sm:w-32" />
        
        {/* Description */}
        <div className="space-y-2 sm:space-y-3">
          {Array.from({ length: 4 }).map((_, i) => (
            <div key={i} className="h-3 sm:h-4 bg-gray-200 rounded w-full" />
          ))}
        </div>
        
        {/* Actions */}
        <div className="space-y-3 sm:space-y-4 pt-4">
          <div className="h-10 sm:h-12 bg-gray-200 rounded w-full" />
          <div className="h-8 sm:h-10 bg-gray-200 rounded w-36 sm:w-48" />
        </div>
      </div>
    </div>
  );
}