'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import { useTranslations } from 'next-intl'
import { urlFor } from '@/lib/sanity/client'
import { resolveImageUrl, resolveImageAlt } from '@/lib/sanity/image'
import LazyImage, { ProductImageSkeleton } from '@/components/ui/LazyImage'
import { imageSizes, ImagePerformanceMonitor } from '@/components/ui/OptimizedImage'
import { Product } from '@/types/sanity'

interface ProductCardProps {
  product: Product
  locale: string
  index?: number
  priority?: boolean // For above-the-fold images
}

export function ProductCard({ product, locale, index = 0, priority = false }: ProductCardProps) {
  const t = useTranslations('Products')
  
  // Debug logging to verify data structure
  console.log('ProductCard received product:', {
    _id: product._id,
    slug: product.slug,
    name: product.name,
    hasSlugCurrent: !!product.slug?.current
  })
  
  // Get localized content with fallback to Chinese
  const name = product.name?.[locale as keyof typeof product.name] || product.name?.zh || t('unnamedProduct')
  const shortDesc = product.shortDescription?.[locale as keyof typeof product.shortDescription] || product.shortDescription?.zh
  
  // Ensure product has required fields
  if (!product._id || !product.slug?.current) {
    console.log('ProductCard early return:', { 
      hasId: !!product._id, 
      hasSlugCurrent: !!product.slug?.current,
      slug: product.slug 
    })
    return null
  }
  

  return (
    <motion.article
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, delay: index * 0.1 }}
      whileHover={{ y: -5 }}
      className="group cursor-pointer"
      data-testid="product-card"
    >
      <Link href={`/${locale}/products/${product.slug.current}`}>
        <div className="relative w-full aspect-square overflow-hidden rounded-lg bg-gray-100" style={{ minHeight: '200px' }}>
          {(() => {
            // 中文注释：解析 flexibleImage 获取最终 URL 和 alt text，使用 gallery 第一张图片作为主图
            const mainImage = product.gallery?.[0]
            
            // 防御性检查：确保有有效的图片数据
            if (!mainImage) {
              console.warn('ProductCard: No gallery image found for product', { 
                productId: product._id, 
                productName: name 
              })
            }
            
            const imgSrc = resolveImageUrl(mainImage, 400, 400)
            const imgAlt = resolveImageAlt(mainImage, name || 'Product image')
            
            // Debug: 输出图片数据用于诊断（仅在开发环境）
            // if (process.env.NODE_ENV === 'development') {
            //   console.log('ProductCard Debug:', {
            //     productId: product._id,
            //     productName: name,
            //     hasGallery: !!product.gallery,
            //     galleryLength: product.gallery?.length,
            //     firstImageData: product.gallery?.[0],
            //     resolvedImageSrc: imgSrc,
            //     resolvedImageAlt: imgAlt
            //   })
            // }
            
            // Performance monitoring callback
            const handleLoadTime = (loadTime: number) => {
              const monitor = ImagePerformanceMonitor.getInstance()
              monitor.recordLoadTime(loadTime)
            }
            
            // 始终渲染 LazyImage，让组件自己处理错误和 fallback
            return (
              <LazyImage
                src={imgSrc}
                alt={imgAlt}
                fill
                sizes={imageSizes.productCard}
                className="h-auto transition-transform duration-300 group-hover:scale-105"
                placeholder="blur"
                quality={80}
                eager={priority} // Load immediately for above-the-fold images
                critical={index < 3} // First 3 images are critical
                loadMargin="200px" // Start loading 200px before viewport
                index={index} // Pass index for Priority Hints API calculation
                importance={index < 3 ? 'high' : index < 6 ? 'auto' : 'low'} // Explicit priority hints
                skeleton={
                  <ProductImageSkeleton 
                    className="w-full h-full" 
                    aspectRatio="square" 
                  />
                }
                onLoadTime={handleLoadTime}
                containerClassName="w-full h-full"
              />
            )
          })()}
          
          {/* Status badges */}
          {product.tags && product.tags.length > 0 && (
            <div className="absolute top-2 left-2 flex flex-col gap-1">
              {product.tags.includes('new') && (
                <span className="bg-green-500 text-white text-xs px-2 py-1 rounded-full">
                  {locale === 'zh' ? '新品' : locale === 'en' ? 'NEW' : 'جديد'}
                </span>
              )}
              {product.tags.includes('limited') && (
                <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                  {locale === 'zh' ? '限定' : locale === 'en' ? 'LIMITED' : 'محدود'}
                </span>
              )}
              {product.tags.includes('popular') && (
                <span className="bg-purple-500 text-white text-xs px-2 py-1 rounded-full">
                  {locale === 'zh' ? '热门' : locale === 'en' ? 'POPULAR' : 'شائع'}
                </span>
              )}
              {product.tags.includes('sale') && (
                <span className="bg-orange-500 text-white text-xs px-2 py-1 rounded-full">
                  {locale === 'zh' ? '促销' : locale === 'en' ? 'SALE' : 'تخفيض'}
                </span>
              )}
              {product.tags.includes('exclusive') && (
                <span className="bg-indigo-500 text-white text-xs px-2 py-1 rounded-full">
                  {locale === 'zh' ? '独家' : locale === 'en' ? 'EXCLUSIVE' : 'حصري'}
                </span>
              )}
              {product.tags.includes('preorder') && (
                <span className="bg-yellow-500 text-white text-xs px-2 py-1 rounded-full">
                  {locale === 'zh' ? '预售' : locale === 'en' ? 'PRE-ORDER' : 'طلب مسبق'}
                </span>
              )}
            </div>
          )}
          
          {/* Stock status badge if not using tags */}
          {product.stockStatus === 'pre-order' && !product.tags?.includes('preorder') && (
            <div className="absolute top-2 left-2">
              <span className="bg-yellow-500 text-white text-xs px-2 py-1 rounded-full">
                {t('preOrder')}
              </span>
            </div>
          )}
          
          {/* Out of stock overlay */}
          {product.stockStatus === 'sold-out' && (
            <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
              <span className="text-white font-semibold text-lg">{t('outOfStock')}</span>
            </div>
          )}
        </div>
        
        <div className="mt-4 flex flex-col h-20">
          <h3 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors line-clamp-2 mb-2" data-testid="product-name">
            {name || t('unnamedProduct')}
          </h3>
          
          <div className="flex items-center justify-between mt-auto">
            {product.price && (
              <p className="text-lg font-semibold text-gray-900" data-testid="product-price">
                {product.currency === 'CNY' && '¥'}
                {product.currency === 'USD' && '$'}
                {product.currency === 'AED' && 'د.إ'}
                {product.price}
              </p>
            )}
            
            {product.category && (
              <span className="text-xs text-gray-400 bg-gray-100 px-2 py-1 rounded">
                {product.category.name[locale as keyof typeof product.category.name] || product.category.name.zh}
              </span>
            )}
          </div>
        </div>
      </Link>
    </motion.article>
  )
}

export default ProductCard