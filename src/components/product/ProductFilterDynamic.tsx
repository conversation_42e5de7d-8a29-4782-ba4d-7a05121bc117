'use client'

import dynamic from 'next/dynamic'
import { ProductFilterSkeleton } from './ProductFilterSkeleton'
import { FilterState, FilterOptions } from './ProductFilter'

// Dynamically import the ProductFilter component to reduce initial bundle size
const ProductFilter = dynamic(
  () => import('./ProductFilter').then((mod) => ({ default: mod.ProductFilter })),
  {
    ssr: false, // Filter component is interactive and can be client-side only
    loading: () => <ProductFilterSkeleton />,
  }
)

interface ProductFilterDynamicProps {
  locale: string
  filterOptions: FilterOptions
  onFilterChange: (filters: FilterState) => void
  initialFilters?: Partial<FilterState>
  className?: string
  isLoading?: boolean
}

export function ProductFilterDynamic(props: ProductFilterDynamicProps) {
  return <ProductFilter {...props} />
}

export default ProductFilterDynamic
export type { FilterState, FilterOptions }