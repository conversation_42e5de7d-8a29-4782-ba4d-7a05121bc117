'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ChevronDownIcon, XMarkIcon, FunnelIcon } from '@heroicons/react/24/outline'
import { useSearchParams, useRouter, usePathname } from 'next/navigation'
import { useTranslations } from 'next-intl'
import { SORT_OPTIONS } from '@/constants/filters'

export interface FilterState {
  ipSeries: string[];
  categories: string[];
  priceRange: {
    min: number;
    max: number;
  };
  stockStatus: string[];
  tags: string[];
  sortBy: string;
}

export interface FilterOptions {
  ipSeries: Array<{ _id: string; name: { zh: string; en: string; ar: string }; productCount: number }>;
  categories: Array<{
    _id: string;
    name: { zh: string; en: string; ar: string };
    slug: { current: string };
    productCount: number;
  }>;
  priceRanges: Array<{
    label: string;
    min: number;
    max: number;
  }>;
  stockStatuses: Array<{
    value: string;
    label: { zh: string; en: string; ar: string };
    count: number;
  }>;
  availableTags: Array<{
    value: string;
    label: { zh: string; en: string; ar: string };
    count: number;
  }>;
  sortOptions: Array<{
    value: string;
    label: { zh: string; en: string; ar: string };
  }>;
}

interface ProductFilterProps {
  locale: string
  filterOptions: FilterOptions
  onFilterChange: (filters: FilterState) => void
  initialFilters?: Partial<FilterState>
  className?: string
  isLoading?: boolean
}

const defaultFilters: FilterState = {
  ipSeries: [],
  categories: [],
  priceRange: { min: 0, max: 10000 },
  stockStatus: [],
  tags: [],
  sortBy: 'newest'
}

const defaultFilterOptions: FilterOptions = {
  ipSeries: [],
  categories: [],
  priceRanges: [
    { label: '0-100', min: 0, max: 100 },
    { label: '100-500', min: 100, max: 500 },
    { label: '500-1000', min: 500, max: 1000 },
    { label: '1000+', min: 1000, max: 10000 }
  ],
  stockStatuses: [
    { value: 'in-stock', label: { zh: '现货', en: 'In Stock', ar: 'متوفر' }, count: 0 },
    { value: 'pre-order', label: { zh: '预售', en: 'Pre-order', ar: 'طلب مسبق' }, count: 0 },
    { value: 'sold-out', label: { zh: '售罄', en: 'Sold Out', ar: 'نفد المخزون' }, count: 0 }
  ],
  availableTags: [],
  sortOptions: SORT_OPTIONS
}

export function ProductFilter({
  locale = 'zh',
  filterOptions = defaultFilterOptions,
  onFilterChange,
  initialFilters = {},
  className = '',
  isLoading = false
}: ProductFilterProps) {
  const t = useTranslations('FilterPage')
  const tCommon = useTranslations('Common')
  const router = useRouter()
  const pathname = usePathname()
  const searchParams = useSearchParams()
  
  const [filters, setFilters] = useState<FilterState>({
    ...defaultFilters,
    ...initialFilters
  })
  
  const [isOpen, setIsOpen] = useState(false)
  const [expandedSections, setExpandedSections] = useState<Set<string>>(
    new Set(['ipSeries', 'categories','sort'])
  )

  // Initialize filters from URL parameters
  useEffect(() => {
    const urlFilters: Partial<FilterState> = {}
    
    // IP Series filters
    const ipSeries = searchParams.get('ipSeries')
    if (ipSeries) {
      urlFilters.ipSeries = ipSeries.split(',')
    }
    
    // Category filters
    const categories = searchParams.get('categories')
    if (categories) {
      urlFilters.categories = categories.split(',')
    }
    
    // Price range
    const minPrice = searchParams.get('minPrice')
    const maxPrice = searchParams.get('maxPrice')
    if (minPrice || maxPrice) {
      urlFilters.priceRange = {
        min: minPrice ? parseInt(minPrice) : 0,
        max: maxPrice ? parseInt(maxPrice) : 10000
      }
    }
    
    // Stock status
    const stockStatus = searchParams.get('stockStatus')
    if (stockStatus) {
      urlFilters.stockStatus = stockStatus.split(',')
    }
    
    // Tags
    const tags = searchParams.get('tags')
    if (tags) {
      urlFilters.tags = tags.split(',')
    }
    
    // Sort
    const sortBy = searchParams.get('sortBy')
    if (sortBy) {
      urlFilters.sortBy = sortBy
    }
    
    // Only update if filters have actually changed
    const newFilters = { ...defaultFilters, ...urlFilters }
    const filtersChanged = JSON.stringify(newFilters) !== JSON.stringify(filters)
    
    if (filtersChanged) {
      setFilters(newFilters)
      // Don't call onFilterChange here - let the parent component handle URL changes
    }
  }, [searchParams, filters]) // Added filters to dependencies

  // Update filters and sync to URL
  const updateFilters = (newFilters: FilterState) => {
    setFilters(newFilters)
    
    // Update URL parameters
    const params = new URLSearchParams()
    
    if (newFilters.ipSeries && newFilters.ipSeries.length > 0) {
      params.set('ipSeries', newFilters.ipSeries.join(','))
    }
    
    if (newFilters.categories.length > 0) {
      params.set('categories', newFilters.categories.join(','))
    }
    
    if (newFilters.priceRange.min > 0 || newFilters.priceRange.max < 10000) {
      if (newFilters.priceRange.min > 0) {
        params.set('minPrice', newFilters.priceRange.min.toString())
      }
      if (newFilters.priceRange.max < 10000) {
        params.set('maxPrice', newFilters.priceRange.max.toString())
      }
    }
    
    if (newFilters.stockStatus.length > 0) {
      params.set('stockStatus', newFilters.stockStatus.join(','))
    }
    
    if (newFilters.tags.length > 0) {
      params.set('tags', newFilters.tags.join(','))
    }
    
    if (newFilters.sortBy !== 'newest') {
      params.set('sortBy', newFilters.sortBy)
    }
    
    // Preserve search term
    const searchTerm = searchParams.get('q')
    if (searchTerm) {
      params.set('q', searchTerm)
    }
    
    const newUrl = params.toString() ? `${pathname}?${params.toString()}` : pathname
    router.replace(newUrl)
    // Note: onFilterChange will be triggered by the URL change in the parent component
  }

  // Toggle IP Series filter
  const toggleIpSeries = (ipSeriesId: string) => {
    const newIpSeries = filters.ipSeries.includes(ipSeriesId)
      ? filters.ipSeries.filter(id => id !== ipSeriesId)
      : [...filters.ipSeries, ipSeriesId];
    
    updateFilters({ ...filters, ipSeries: newIpSeries });
  };

  // Toggle category filter
  const toggleCategory = (categorySlug: string) => {
    const newCategories = filters.categories.includes(categorySlug)
      ? filters.categories.filter(c => c !== categorySlug)
      : [...filters.categories, categorySlug]
    
    updateFilters({ ...filters, categories: newCategories })
  }

  // Update price range
  const updatePriceRange = (min: number, max: number) => {
    updateFilters({ ...filters, priceRange: { min, max } })
  }

  // Toggle stock status
  const toggleStockStatus = (status: string) => {
    const newStockStatus = filters.stockStatus.includes(status)
      ? filters.stockStatus.filter(s => s !== status)
      : [...filters.stockStatus, status]
    
    updateFilters({ ...filters, stockStatus: newStockStatus })
  }

  // Toggle tag
  const toggleTag = (tag: string) => {
    const newTags = filters.tags.includes(tag)
      ? filters.tags.filter(t => t !== tag)
      : [...filters.tags, tag]
    
    updateFilters({ ...filters, tags: newTags })
  }

  // Update sort
  const updateSort = (sortBy: string) => {
    updateFilters({ ...filters, sortBy })
  }

  // Clear all filters
  const clearAllFilters = () => {
    updateFilters(defaultFilters)
  }

  // Toggle filter section expanded state
  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections)
    if (newExpanded.has(section)) {
      newExpanded.delete(section)
    } else {
      newExpanded.add(section)
    }
    setExpandedSections(newExpanded)
  }

  // Calculate active filters count
  const activeFiltersCount = 
    (filters.ipSeries?.length || 0) +
    filters.categories.length +
    filters.stockStatus.length +
    filters.tags.length +
    (filters.priceRange.min > 0 || filters.priceRange.max < 10000 ? 1 : 0) +
    (filters.sortBy !== 'newest' ? 1 : 0)

  const getLocalizedText = (textObj: { zh: string; en: string; ar: string }) => {
    return textObj[locale as keyof typeof textObj] || textObj.zh
  }

  return (
    <div className={`product-filter ${className}`}>
      {/* Mobile filter button */}
      <div className="lg:hidden mb-4">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="flex items-center gap-2 px-4 py-2 bg-white border border-gray-300 rounded-lg shadow-sm hover:bg-gray-50 transition-colors"
        >
          <FunnelIcon className="w-5 h-5" />
          <span>{tCommon('filter')}</span>
          {activeFiltersCount > 0 && (
            <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
              {activeFiltersCount}
            </span>
          )}
        </button>
      </div>

      {/* Filter panel */}
      <AnimatePresence>
        {(isOpen || (typeof window !== 'undefined' && window.innerWidth >= 1024)) && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="bg-white border border-gray-200 rounded-lg p-4 mb-6 lg:mb-0"
          >
            {/* Filter header */}
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                {t('filters.title')}
                {isLoading && (
                  <div className="w-4 h-4 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin" />
                )}
              </h3>
              <div className="flex items-center gap-2">
                {activeFiltersCount > 0 && (
                  <button
                    onClick={clearAllFilters}
                    disabled={isLoading}
                    className="text-sm text-blue-600 hover:text-blue-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {t('filters.clearAll')}
                  </button>
                )}
                <button
                  onClick={() => setIsOpen(false)}
                  className="lg:hidden p-1 hover:bg-gray-100 rounded"
                >
                  <XMarkIcon className="w-5 h-5" />
                </button>
              </div>
            </div>

            
            {/* IP Series filter */}
            {filterOptions.ipSeries && filterOptions.ipSeries.length > 0 && (
              <div className="mb-6">
                <button
                  onClick={() => toggleSection('ipSeries')}
                  className="flex items-center justify-between w-full mb-3"
                >
                  <h4 className="font-medium text-gray-900">{t('filters.ipSeries')}</h4>
                  <ChevronDownIcon 
                    className={`w-5 h-5 transition-transform ${
                      expandedSections.has('ipSeries') ? 'rotate-180' : ''
                    }`}
                  />
                </button>
                
                <AnimatePresence>
                  {expandedSections.has('ipSeries') && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      className="space-y-2"
                    >
                      {filterOptions.ipSeries.map((ipSeries) => (
                        <label key={ipSeries._id} className="flex items-center justify-between">
                          <div className="flex items-center">
                            <input
                              type="checkbox"
                              checked={filters.ipSeries.includes(ipSeries._id)}
                              onChange={() => toggleIpSeries(ipSeries._id)}
                              className="w-4 h-4 text-blue-600 focus:ring-blue-500"
                            />
                            <span className="ml-2 text-sm text-gray-700">
                              {getLocalizedText(ipSeries.name)}
                            </span>
                          </div>
                          <span className="text-xs text-gray-400">
                            ({ipSeries.productCount})
                          </span>
                        </label>
                      ))}
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            )}

            {/* Category filter */}
            {filterOptions.categories.length > 0 && (
              <div className="mb-6">
                <button
                  onClick={() => toggleSection('categories')}
                  className="flex items-center justify-between w-full mb-3"
                >
                  <h4 className="font-medium text-gray-900">{t('filters.categories')}</h4>
                  <ChevronDownIcon 
                    className={`w-5 h-5 transition-transform ${
                      expandedSections.has('categories') ? 'rotate-180' : ''
                    }`}
                  />
                </button>
                
                <AnimatePresence>
                  {expandedSections.has('categories') && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      className="space-y-2"
                    >
                      {filterOptions.categories.map((category) => (
                        <label key={category._id} className="flex items-center justify-between">
                          <div className="flex items-center">
                            <input
                              type="checkbox"
                              checked={filters.categories.includes(category.slug.current)}
                              onChange={() => toggleCategory(category.slug.current)}
                              className="w-4 h-4 text-blue-600 focus:ring-blue-500"
                            />
                            <span className="ml-2 text-sm text-gray-700">
                              {getLocalizedText(category.name)}
                            </span>
                          </div>
                          <span className="text-xs text-gray-400">
                            ({category.productCount})
                          </span>
                        </label>
                      ))}
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            )}

            {/* Sort */}
            <div className="mb-6">
              <button
                onClick={() => toggleSection('sort')}
                className="flex items-center justify-between w-full mb-3"
              >
                <h4 className="font-medium text-gray-900">{tCommon('sort')}</h4>
                <ChevronDownIcon 
                  className={`w-5 h-5 transition-transform ${
                    expandedSections.has('sort') ? 'rotate-180' : ''
                  }`}
                />
              </button>
              
              <AnimatePresence>
                {expandedSections.has('sort') && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    className="space-y-2"
                  >
                    {filterOptions.sortOptions.map((option) => (
                      <label key={option.value} className="flex items-center">
                        <input
                          type="radio"
                          checked={filters.sortBy === option.value}
                          onChange={() => updateSort(option.value)}
                          className="w-4 h-4 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="ml-2 text-sm text-gray-700">
                          {getLocalizedText(option.label)}
                        </span>
                      </label>
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>


            {/* Price range */}
            <div className="mb-6">
              <button
                onClick={() => toggleSection('price')}
                className="flex items-center justify-between w-full mb-3"
              >
                <h4 className="font-medium text-gray-900">{t('filters.priceRange')}</h4>
                <ChevronDownIcon 
                  className={`w-5 h-5 transition-transform ${
                    expandedSections.has('price') ? 'rotate-180' : ''
                  }`}
                />
              </button>
              
              <AnimatePresence>
                {expandedSections.has('price') && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    className="space-y-3"
                  >
                    {filterOptions.priceRanges.map((range) => (
                      <label key={`${range.min}-${range.max}`} className="flex items-center">
                        <input
                          type="radio"
                          checked={
                            filters.priceRange.min === range.min && 
                            filters.priceRange.max === range.max
                          }
                          onChange={() => updatePriceRange(range.min, range.max)}
                          className="w-4 h-4 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="ml-2 text-sm text-gray-700">
                          ¥{range.label}
                        </span>
                      </label>
                    ))}
                    
                    {/* Custom price range */}
                    <div className="pt-2 border-t border-gray-200">
                      <div className="grid grid-cols-2 gap-2">
                        <input
                          type="number"
                          placeholder={t('filters.minPrice')}
                          min="0"
                          value={filters.priceRange.min || ''}
                          onChange={(e) => updatePriceRange(
                            parseInt(e.target.value) || 0,
                            filters.priceRange.max
                          )}
                          className="px-3 py-2 border border-gray-300 rounded text-sm"
                        />
                        <input
                          type="number"
                          placeholder={t('filters.maxPrice')}
                          min="0"
                          value={filters.priceRange.max === 10000 ? '' : filters.priceRange.max}
                          onChange={(e) => updatePriceRange(
                            filters.priceRange.min,
                            parseInt(e.target.value) || 10000
                          )}
                          className="px-3 py-2 border border-gray-300 rounded text-sm"
                        />
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Stock status */}
            <div className="mb-6">
              <button
                onClick={() => toggleSection('stock')}
                className="flex items-center justify-between w-full mb-3"
              >
                <h4 className="font-medium text-gray-900">{t('filters.stockStatus')}</h4>
                <ChevronDownIcon 
                  className={`w-5 h-5 transition-transform ${
                    expandedSections.has('stock') ? 'rotate-180' : ''
                  }`}
                />
              </button>
              
              <AnimatePresence>
                {expandedSections.has('stock') && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    className="space-y-2"
                  >
                    {filterOptions.stockStatuses.map((status) => (
                      <label key={status.value} className="flex items-center justify-between">
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            checked={filters.stockStatus.includes(status.value)}
                            onChange={() => toggleStockStatus(status.value)}
                            className="w-4 h-4 text-blue-600 focus:ring-blue-500"
                          />
                          <span className="ml-2 text-sm text-gray-700">
                            {getLocalizedText(status.label)}
                          </span>
                        </div>
                        <span className="text-xs text-gray-400">
                          ({status.count})
                        </span>
                      </label>
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Tag filter */}
            {filterOptions.availableTags.length > 0 && (
              <div className="mb-6">
                <button
                  onClick={() => toggleSection('tags')}
                  className="flex items-center justify-between w-full mb-3"
                >
                  <h4 className="font-medium text-gray-900">{t('filters.productTags')}</h4>
                  <ChevronDownIcon 
                    className={`w-5 h-5 transition-transform ${
                      expandedSections.has('tags') ? 'rotate-180' : ''
                    }`}
                  />
                </button>
                
                <AnimatePresence>
                  {expandedSections.has('tags') && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      className="space-y-2"
                    >
                      {filterOptions.availableTags.map((tag) => (
                        <label key={tag.value} className="flex items-center justify-between">
                          <div className="flex items-center">
                            <input
                              type="checkbox"
                              checked={filters.tags.includes(tag.value)}
                              onChange={() => toggleTag(tag.value)}
                              className="w-4 h-4 text-blue-600 focus:ring-blue-500"
                            />
                            <span className="ml-2 text-sm text-gray-700">
                              {tag.value}
                            </span>
                          </div>
                          <span className="text-xs text-gray-400">
                            ({tag.count})
                          </span>
                        </label>
                      ))}
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default ProductFilter