'use client'

import { useState } from 'react'
import { resolveImageUrl } from '@/lib/sanity/image'
import OptimizedImage from '@/components/ui/OptimizedImage'
import { ChevronLeftIcon, ChevronRightIcon, XMarkIcon } from '@heroicons/react/24/outline'

interface ProductImageGalleryProps {
  gallery: any[]  // Gallery is now the primary image array, first image is main image
  productName: string
}

export function ProductImageGallery({ gallery, productName }: ProductImageGalleryProps) {
  const [selectedImageIndex, setSelectedImageIndex] = useState(0)
  const [isZoomed, setIsZoomed] = useState(false)
  const [isImageLoading, setIsImageLoading] = useState(false)
  const [showRemainingModal, setShowRemainingModal] = useState(false)

  // Use gallery as the primary and only image source
  const allImages = gallery && gallery.length > 0 ? gallery : []

  const nextImage = () => {
    setIsImageLoading(true)
    setSelectedImageIndex((prev) => (prev + 1) % allImages.length)
  }

  const prevImage = () => {
    setIsImageLoading(true)
    setSelectedImageIndex((prev) => (prev - 1 + allImages.length) % allImages.length)
  }

  const openZoom = () => {
    setIsZoomed(true)
  }

  const closeZoom = () => {
    setIsZoomed(false)
  }

  if (allImages.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-4">
        <div className="aspect-square bg-gray-200 rounded-lg flex items-center justify-center">
          <span className="text-gray-400">No image available</span>
        </div>
      </div>
    )
  }

  return (
    <>
      <div className="bg-white rounded-lg shadow-lg p-4">
        {/* Main Image Display */}
        <div 
          className="aspect-square bg-gray-200 rounded-lg mb-4 relative overflow-hidden group cursor-pointer"
          onClick={openZoom}
        >
          <OptimizedImage
            key={`gallery-main-${allImages[selectedImageIndex]?._id || selectedImageIndex}`}
            src={resolveImageUrl(allImages[selectedImageIndex], 600, 600)}
            alt={`${productName} - Image ${selectedImageIndex + 1}`}
            fill
            sizes="(max-width: 1024px) 100vw, 50vw"
            className="object-cover transition-transform duration-200 group-hover:scale-105"
            priority
            placeholder="blur"
            onLoad={() => setIsImageLoading(false)}
            onError={() => setIsImageLoading(false)}
          />
          
          {/* Loading overlay */}
          {isImageLoading && (
            <div className="absolute inset-0 bg-gray-200 flex items-center justify-center">
              <div className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
            </div>
          )}

          {/* Navigation arrows for main image */}
          {allImages.length > 1 && (
            <>
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  prevImage()
                }}
                className="absolute left-4 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                aria-label="Previous image"
              >
                <ChevronLeftIcon className="w-5 h-5" />
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  nextImage()
                }}
                className="absolute right-4 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                aria-label="Next image"
              >
                <ChevronRightIcon className="w-5 h-5" />
              </button>
            </>
          )}

          {/* Image counter */}
          {allImages.length > 1 && (
            <div className="absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/50 text-white px-3 py-1 rounded-full text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200">
              {selectedImageIndex + 1} / {allImages.length}
            </div>
          )}
        </div>

        {/* Thumbnail Grid */}
        {allImages.length > 1 && (
          <div className="grid grid-cols-4 gap-2">
            {allImages.slice(0, 8).map((image, index) => (
              <button
                key={index}
                onClick={() => {
                  // Only set loading state if clicking a different image
                  if (selectedImageIndex !== index) {
                    setIsImageLoading(true)
                    setSelectedImageIndex(index)
                  }
                }}
                className={`aspect-square bg-gray-200 rounded-lg relative overflow-hidden transition-all duration-200 ${
                  selectedImageIndex === index 
                    ? 'ring-2 ring-blue-500 ring-offset-2' 
                    : 'hover:ring-2 hover:ring-gray-300 hover:ring-offset-1'
                }`}
              >
                <OptimizedImage
                  src={resolveImageUrl(image, 150, 150)}
                  alt={`${productName} thumbnail ${index + 1}`}
                  fill
                  sizes="150px"
                  className="object-cover"
                  placeholder="blur"
                />
              </button>
            ))}
            {allImages.length > 8 && (
              <button
                onClick={() => setShowRemainingModal(true)}
                className="aspect-square bg-gray-200 rounded-lg relative overflow-hidden hover:ring-2 hover:ring-gray-300 hover:ring-offset-1 transition-all duration-200 group"
                aria-label={`View ${allImages.length - 8} more images`}
              >
                {/* 2x2 grid of preview images */}
                <div className="grid grid-cols-2 gap-0.5 h-full w-full p-0.5">
                  {allImages.slice(8, 12).map((image, index) => (
                    <div key={index} className="relative overflow-hidden rounded-sm">
                      <OptimizedImage
                        src={resolveImageUrl(image, 75, 75)}
                        alt={`Preview ${index + 9}`}
                        fill
                        sizes="75px"
                        className="object-cover"
                        placeholder="blur"
                      />
                    </div>
                  ))}
                  {/* Fill remaining slots if less than 4 images */}
                  {Array.from({ length: Math.max(0, 4 - (allImages.slice(8, 12).length)) }).map((_, index) => (
                    <div key={`filler-${index}`} className="bg-gray-300 rounded-sm opacity-50" />
                  ))}
                </div>
                
                {/* Overlay with count */}
                <div className="absolute inset-0 bg-black/40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  <span className="text-white text-xs font-medium">
                    +{allImages.length - 8}
                  </span>
                </div>
              </button>
            )}
          </div>
        )}
      </div>

      {/* Zoom Modal */}
      {isZoomed && (
        <div className="fixed inset-0 z-50 bg-black/90 flex items-center justify-center p-4">
          <div className="relative max-w-4xl max-h-full w-full h-full flex items-center justify-center">
            {/* Close button */}
            <button
              onClick={closeZoom}
              className="absolute top-4 right-4 bg-white/20 hover:bg-white/30 text-white p-2 rounded-full z-10"
              aria-label="Close zoom"
            >
              <XMarkIcon className="w-6 h-6" />
            </button>

            {/* Navigation in zoom mode */}
            {allImages.length > 1 && (
              <>
                <button
                  onClick={prevImage}
                  className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white p-3 rounded-full z-10"
                  aria-label="Previous image"
                >
                  <ChevronLeftIcon className="w-6 h-6" />
                </button>
                <button
                  onClick={nextImage}
                  className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white p-3 rounded-full z-10"
                  aria-label="Next image"
                >
                  <ChevronRightIcon className="w-6 h-6" />
                </button>
              </>
            )}

            {/* Zoomed image */}
            <div className="relative w-full h-full max-w-3xl max-h-3xl">
              <OptimizedImage
                key={`gallery-zoom-${allImages[selectedImageIndex]?._id || selectedImageIndex}`}
                src={resolveImageUrl(allImages[selectedImageIndex], 1200, 1200)}
                alt={`${productName} - Image ${selectedImageIndex + 1}`}
                fill
                sizes="(max-width: 768px) 100vw, 768px"
                className="object-contain"
                placeholder="blur"
              />
            </div>

            {/* Image counter in zoom mode */}
            {allImages.length > 1 && (
              <div className="absolute bottom-4 left-1/2 -translate-x-1/2 bg-white/20 text-white px-4 py-2 rounded-full">
                {selectedImageIndex + 1} / {allImages.length}
              </div>
            )}
          </div>

          {/* Click outside to close */}
          <div
            className="absolute inset-0 -z-10"
            onClick={closeZoom}
            aria-label="Close zoom"
          />
        </div>
      )}

      {/* Remaining Images Modal */}
      {showRemainingModal && (
        <div className="fixed inset-0 z-50 bg-black/90 flex items-center justify-center p-4">
          <div className="relative bg-white rounded-lg max-w-4xl max-h-[90vh] w-full overflow-hidden">
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b">
              <h3 className="text-lg font-semibold text-gray-900">
                All Images ({allImages.length})
              </h3>
              <button
                onClick={() => setShowRemainingModal(false)}
                className="text-gray-400 hover:text-gray-600 p-1"
                aria-label="Close modal"
              >
                <XMarkIcon className="w-6 h-6" />
              </button>
            </div>

            {/* Scrollable grid */}
            <div className="p-4 overflow-y-auto max-h-[calc(90vh-120px)]">
              <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6 gap-3">
                {allImages.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      setSelectedImageIndex(index)
                      setShowRemainingModal(false)
                      setIsZoomed(true)
                    }}
                    className={`aspect-square bg-gray-200 rounded-lg relative overflow-hidden transition-all duration-200 hover:ring-2 hover:ring-blue-300 hover:ring-offset-1 ${
                      selectedImageIndex === index 
                        ? 'ring-2 ring-blue-500 ring-offset-2' 
                        : ''
                    }`}
                  >
                    <OptimizedImage
                      src={resolveImageUrl(image, 200, 200)}
                      alt={`${productName} thumbnail ${index + 1}`}
                      fill
                      sizes="200px"
                      className="object-cover"
                      placeholder="blur"
                    />
                    
                    {/* Image number overlay */}
                    <div className="absolute top-1 left-1 bg-black/60 text-white text-xs px-1.5 py-0.5 rounded">
                      {index + 1}
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Footer with instructions */}
            <div className="px-4 py-3 bg-gray-50 border-t text-sm text-gray-600">
              Click any image to view in full screen
            </div>
          </div>

          {/* Click outside to close */}
          <div
            className="absolute inset-0 -z-10"
            onClick={() => setShowRemainingModal(false)}
            aria-label="Close modal"
          />
        </div>
      )}
    </>
  )
}