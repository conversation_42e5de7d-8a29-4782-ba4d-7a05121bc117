'use client'

import { useTranslations } from 'next-intl'
import { Link, usePathname } from '@/i18n/routing'
import { cn } from '@/lib/utils'

interface NavigationProps {
  mobile?: boolean
  onItemClick?: () => void
}

export function Navigation({ mobile = false, onItemClick }: NavigationProps) {
  const t = useTranslations('Navigation')
  const pathname = usePathname()

  const navigationItems = [
    { href: '/', label: t('home') },
    { href: '/products', label: t('products') },
    { href: '/about', label: t('about') },
    { href: '/contact', label: t('contact') },
  ]

  const baseClasses = mobile
    ? 'block py-2 px-4 text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors'
    : 'text-gray-700 hover:text-blue-600 transition-colors font-medium'

  return (
    <nav className={mobile ? 'space-y-2' : 'flex items-center space-x-8'}>
      {navigationItems.map((item) => {
        const isActive = pathname === item.href || 
          (item.href !== '/' && pathname.startsWith(item.href))

        return (
          <Link
            key={item.href}
            href={item.href}
            onClick={onItemClick}
            className={cn(
              baseClasses,
              isActive && (mobile 
                ? 'text-blue-600 bg-blue-50' 
                : 'text-blue-600'
              )
            )}
          >
            {item.label}
          </Link>
        )
      })}
    </nav>
  )
}