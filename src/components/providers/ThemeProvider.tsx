'use client'

import { useEffect } from 'react'
import { applyThemeColors } from '@/lib/utils/colors'

interface ThemeProviderProps {
  primaryColor?: string
  secondaryColor?: string
  children: React.ReactNode
}

export function ThemeProvider({ primaryColor, secondaryColor, children }: ThemeProviderProps) {
  useEffect(() => {
    // Apply theme colors when component mounts or colors change
    applyThemeColors(primaryColor, secondaryColor)
  }, [primaryColor, secondaryColor])

  return <>{children}</>
}