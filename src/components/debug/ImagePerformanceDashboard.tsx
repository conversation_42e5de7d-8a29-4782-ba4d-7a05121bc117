'use client';

import { useState, useEffect } from 'react';
import { ImagePerformanceMonitor } from '@/components/ui/OptimizedImage';
import { cn } from '@/lib/utils';

interface PerformanceMetrics {
  averageLoadTime: number;
  failureRate: number;
  totalLoads: number;
  slowLoads: number;
}

export default function ImagePerformanceDashboard() {
  const [isOpen, setIsOpen] = useState(false);
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    averageLoadTime: 0,
    failureRate: 0,
    totalLoads: 0,
    slowLoads: 0,
  });

  // Auto-refresh metrics every 30 seconds (only in development)
  useEffect(() => {
    if (process.env.NODE_ENV !== 'development') return;
    
    const updateMetrics = () => {
      const monitor = ImagePerformanceMonitor.getInstance();
      setMetrics(monitor.getPerformanceReport());
    };

    updateMetrics();
    const interval = setInterval(updateMetrics, 30000);

    return () => clearInterval(interval);
  }, []);

  const handleReset = () => {
    const monitor = ImagePerformanceMonitor.getInstance();
    monitor.reset();
    setMetrics({
      averageLoadTime: 0,
      failureRate: 0,
      totalLoads: 0,
      slowLoads: 0,
    });
  };

  // @ts-ignore  // 忽略NODE_ENV类型检查警告
  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <>
      {/* Toggle Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={cn(
          'fixed bottom-4 right-4 z-50 p-3 rounded-full shadow-lg transition-all duration-300',
          'bg-blue-600 hover:bg-blue-700 text-white',
          'flex items-center justify-center'
        )}
        title="Image Performance Monitor"
      >
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      </button>

      {/* Dashboard Panel */}
      {isOpen && (
        <div className="fixed bottom-20 right-4 z-50 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Image Performance
            </h3>
            <div className="flex gap-2">
              <button
                onClick={handleReset}
                className="text-xs px-2 py-1 bg-red-100 hover:bg-red-200 text-red-700 rounded"
                title="Reset metrics"
              >
                Reset
              </button>
              <button
                onClick={() => setIsOpen(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>

          <div className="space-y-3">
            {/* Average Load Time */}
            <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div>
                <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Avg Load Time
                </div>
                <div className={cn(
                  'text-lg font-bold',
                  metrics.averageLoadTime < 1000 ? 'text-green-600' :
                  metrics.averageLoadTime < 2000 ? 'text-yellow-600' : 'text-red-600'
                )}>
                  {metrics.averageLoadTime.toFixed(0)}ms
                </div>
              </div>
              <div className={cn(
                'w-3 h-3 rounded-full',
                metrics.averageLoadTime < 1000 ? 'bg-green-500' :
                metrics.averageLoadTime < 2000 ? 'bg-yellow-500' : 'bg-red-500'
              )} />
            </div>

            {/* Failure Rate */}
            <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div>
                <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Failure Rate
                </div>
                <div className={cn(
                  'text-lg font-bold',
                  metrics.failureRate < 0.05 ? 'text-green-600' :
                  metrics.failureRate < 0.1 ? 'text-yellow-600' : 'text-red-600'
                )}>
                  {(metrics.failureRate * 100).toFixed(1)}%
                </div>
              </div>
              <div className={cn(
                'w-3 h-3 rounded-full',
                metrics.failureRate < 0.05 ? 'bg-green-500' :
                metrics.failureRate < 0.1 ? 'bg-yellow-500' : 'bg-red-500'
              )} />
            </div>

            {/* Total Loads */}
            <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div>
                <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Total Loads
                </div>
                <div className="text-lg font-bold text-blue-600">
                  {metrics.totalLoads}
                </div>
              </div>
              <svg className="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>

            {/* Slow Loads */}
            <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div>
                <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Slow Loads (&gt;3s)
                </div>
                <div className={cn(
                  'text-lg font-bold',
                  metrics.slowLoads === 0 ? 'text-green-600' :
                  metrics.slowLoads <= 2 ? 'text-yellow-600' : 'text-red-600'
                )}>
                  {metrics.slowLoads}
                </div>
              </div>
              <div className={cn(
                'w-3 h-3 rounded-full',
                metrics.slowLoads === 0 ? 'bg-green-500' :
                metrics.slowLoads <= 2 ? 'bg-yellow-500' : 'bg-red-500'
              )} />
            </div>
          </div>

          {/* Performance Tips */}
          <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <div className="text-xs font-medium text-blue-800 dark:text-blue-200 mb-1">
              Performance Tips
            </div>
            <div className="text-xs text-blue-700 dark:text-blue-300 space-y-1">
              {metrics.averageLoadTime > 2000 && (
                <div>• Consider reducing image quality or size</div>
              )}
              {metrics.failureRate > 0.1 && (
                <div>• Check network connectivity and image URLs</div>
              )}
              {metrics.slowLoads > 5 && (
                <div>• Enable progressive loading for more images</div>
              )}
              {metrics.totalLoads === 0 && (
                <div>• Monitoring active - interact with images to see metrics</div>
              )}
            </div>
          </div>

          {/* Connection Info */}
          <ConnectionInfo />
        </div>
      )}
    </>
  );
}

// Component to show current connection information
function ConnectionInfo() {
  const [connectionInfo, setConnectionInfo] = useState<{
    effectiveType?: string;
    downlink?: number;
    saveData?: boolean;
  }>({});

  useEffect(() => {
    const updateConnectionInfo = () => {
      const connection = (navigator as any)?.connection;
      if (connection) {
        setConnectionInfo({
          effectiveType: connection.effectiveType,
          downlink: connection.downlink,
          saveData: connection.saveData,
        });
      }
    };

    updateConnectionInfo();
    
    // Listen for connection changes
    const connection = (navigator as any)?.connection;
    if (connection) {
      connection.addEventListener('change', updateConnectionInfo);
      return () => connection.removeEventListener('change', updateConnectionInfo);
    }
  }, []);

  if (!connectionInfo.effectiveType) {
    return null;
  }

  return (
    <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
      <div className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
        <div className="flex justify-between">
          <span>Connection:</span>
          <span className={cn(
            'font-medium',
            connectionInfo.effectiveType === '4g' ? 'text-green-600' :
            connectionInfo.effectiveType === '3g' ? 'text-yellow-600' : 'text-red-600'
          )}>
            {connectionInfo.effectiveType?.toUpperCase()}
          </span>
        </div>
        {connectionInfo.downlink && (
          <div className="flex justify-between">
            <span>Downlink:</span>
            <span className="font-medium">
              {connectionInfo.downlink} Mbps
            </span>
          </div>
        )}
        {connectionInfo.saveData && (
          <div className="flex justify-between">
            <span>Data Saver:</span>
            <span className="font-medium text-orange-600">ON</span>
          </div>
        )}
      </div>
    </div>
  );
}