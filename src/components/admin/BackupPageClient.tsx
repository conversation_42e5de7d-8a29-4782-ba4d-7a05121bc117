'use client'

import { useState } from 'react'

interface Backup {
  id: string
  name: string
  dataset: string
  size: string
  createdAt: string
  status: 'completed' | 'in_progress' | 'failed'
}

export function BackupPageClient() {
  const [isCreating, setIsCreating] = useState(false)
  const [selectedDataset, setSelectedDataset] = useState('production')
  const [backups, setBackups] = useState<Backup[]>([
    // 示例数据
    {
      id: '1',
      name: 'backup-2024-01-15-production',
      dataset: 'production',
      size: '125 MB',
      createdAt: '2024-01-15 02:00:00',
      status: 'completed'
    },
    {
      id: '2',
      name: 'backup-2024-01-14-production',
      dataset: 'production',
      size: '123 MB',
      createdAt: '2024-01-14 02:00:00',
      status: 'completed'
    }
  ])

  const handleCreateBackup = async () => {
    setIsCreating(true)
    
    try {
      const response = await fetch('/api/admin/backup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ dataset: selectedDataset })
      })
      
      if (!response.ok) {
        throw new Error('Failed to create backup')
      }
      
      const data = await response.json()
      
      // 添加新备份到列表
      const newBackup: Backup = {
        id: data.id,
        name: data.name,
        dataset: selectedDataset,
        size: 'Calculating...',
        createdAt: new Date().toLocaleString('zh-CN'),
        status: 'in_progress'
      }
      
      setBackups([newBackup, ...backups])
      
      // 模拟备份完成
      setTimeout(() => {
        setBackups(prev => prev.map(backup => 
          backup.id === newBackup.id 
            ? { ...backup, status: 'completed', size: '128 MB' }
            : backup
        ))
      }, 5000)
      
      alert('备份创建成功！')
    } catch (error) {
      console.error('Backup error:', error)
      alert('备份创建失败，请稍后重试')
    } finally {
      setIsCreating(false)
    }
  }

  const handleDownload = async (backup: Backup) => {
    try {
      const response = await fetch(`/api/admin/backup/${backup.id}/download`)
      if (!response.ok) {
        throw new Error('Download failed')
      }
      
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${backup.name}.tar.gz`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } catch (error) {
      console.error('Download error:', error)
      alert('下载失败，请稍后重试')
    }
  }

  const handleRestore = async (backup: Backup) => {
    if (!confirm(`确定要从备份 ${backup.name} 恢复数据吗？这将覆盖当前的 ${backup.dataset} 数据集。`)) {
      return
    }
    
    alert('恢复功能需要通过 Sanity CLI 执行，请联系技术支持。')
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          数据备份管理
        </h1>
        
        {/* 创建备份 */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">创建新备份</h2>
          
          <div className="flex gap-4 items-end">
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                选择数据集
              </label>
              <select
                value={selectedDataset}
                onChange={(e) => setSelectedDataset(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="production">生产环境 (production)</option>
                <option value="staging">预发布环境 (staging)</option>
              </select>
            </div>
            
            <button
              onClick={handleCreateBackup}
              disabled={isCreating}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isCreating ? '创建中...' : '创建备份'}
            </button>
          </div>
          
          <div className="mt-4 text-sm text-gray-600">
            <p>• 备份包含所有文档和资源文件</p>
            <p>• 创建过程可能需要几分钟</p>
            <p>• 建议在重要更新前创建备份</p>
          </div>
        </div>

        {/* 备份列表 */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b">
            <h2 className="text-xl font-semibold">备份历史</h2>
          </div>
          
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b bg-gray-50">
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    备份名称
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    数据集
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    大小
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    创建时间
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    状态
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody>
                {backups.map((backup) => (
                  <tr key={backup.id} className="border-b hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {backup.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <span className={`inline-flex px-2 py-1 text-xs rounded ${
                        backup.dataset === 'production' 
                          ? 'bg-red-100 text-red-800' 
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {backup.dataset}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {backup.size}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {backup.createdAt}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <span className={`inline-flex px-2 py-1 text-xs rounded ${
                        backup.status === 'completed' 
                          ? 'bg-green-100 text-green-800'
                          : backup.status === 'in_progress'
                          ? 'bg-blue-100 text-blue-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {backup.status === 'completed' ? '完成' : 
                         backup.status === 'in_progress' ? '进行中' : '失败'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex gap-2">
                        {backup.status === 'completed' && (
                          <>
                            <button
                              onClick={() => handleDownload(backup)}
                              className="text-blue-600 hover:text-blue-800"
                            >
                              下载
                            </button>
                            <span className="text-gray-400">|</span>
                            <button
                              onClick={() => handleRestore(backup)}
                              className="text-green-600 hover:text-green-800"
                            >
                              恢复
                            </button>
                          </>
                        )}
                        {backup.status === 'in_progress' && (
                          <span className="text-gray-400">处理中...</span>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* 自动备份说明 */}
        <div className="mt-8 bg-blue-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-2">自动备份说明</h3>
          <div className="text-sm text-gray-700 space-y-1">
            <p>• 系统每天凌晨 2:00 自动备份生产环境</p>
            <p>• 自动备份保留最近 7 天的数据</p>
            <p>• 重要更新前建议手动创建额外备份</p>
            <p>• 备份文件存储在安全的云存储中</p>
          </div>
        </div>

        {/* 恢复说明 */}
        <div className="mt-8 bg-yellow-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-2">数据恢复说明</h3>
          <div className="text-sm text-gray-700 space-y-1">
            <p>数据恢复需要使用 Sanity CLI 工具，步骤如下：</p>
            <ol className="list-decimal list-inside ml-4 mt-2 space-y-1">
              <li>下载备份文件到本地</li>
              <li>安装 Sanity CLI：<code className="bg-gray-200 px-1 rounded">npm install -g @sanity/cli</code></li>
              <li>运行恢复命令：<code className="bg-gray-200 px-1 rounded">sanity dataset import backup.tar.gz [dataset-name]</code></li>
              <li>确认恢复完成</li>
            </ol>
            <p className="mt-2 font-semibold">⚠️ 注意：恢复操作会覆盖目标数据集的所有数据，请谨慎操作！</p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default BackupPageClient