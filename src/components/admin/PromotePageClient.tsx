'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'

interface ChangeItem {
  type: string
  id: string
  action: 'new' | 'updated'
}

export function PromotePageClient() {
  const [isLoading, setIsLoading] = useState(false)
  const [changes, setChanges] = useState<ChangeItem[]>([])
  const [showPreview, setShowPreview] = useState(false)
  const [promotionResult, setPromotionResult] = useState<{
    success: boolean
    message: string
  } | null>(null)
  
  const router = useRouter()

  // 预览更改
  const handlePreview = async () => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/admin/preview-changes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      
      if (!response.ok) {
        throw new Error('Failed to preview changes')
      }
      
      const data = await response.json()
      setChanges(data.changes)
      setShowPreview(true)
    } catch (error) {
      console.error('Preview error:', error)
      alert('预览失败，请稍后重试')
    } finally {
      setIsLoading(false)
    }
  }

  // 执行推送
  const handlePromote = async () => {
    if (!confirm('确定要将预发布环境的内容推送到生产环境吗？')) {
      return
    }
    
    setIsLoading(true)
    try {
      const response = await fetch('/api/admin/promote', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      
      if (!response.ok) {
        throw new Error('Failed to promote changes')
      }
      
      const data = await response.json()
      setPromotionResult({
        success: true,
        message: `成功推送 ${data.successCount} 个文档到生产环境！`
      })
      
      // 3秒后跳转到首页
      setTimeout(() => {
        router.push('/')
      }, 3000)
    } catch (error) {
      console.error('Promotion error:', error)
      setPromotionResult({
        success: false,
        message: '推送失败，请联系技术支持'
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          内容发布管理
        </h1>
        
        {/* 环境信息 */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">当前环境</h2>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-600">源环境</p>
              <p className="text-lg font-medium">预发布 (staging)</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">目标环境</p>
              <p className="text-lg font-medium">生产 (production)</p>
            </div>
          </div>
        </div>

        {/* 操作按钮 */}
        {!showPreview && !promotionResult && (
          <div className="bg-white rounded-lg shadow p-6 mb-8">
            <h2 className="text-xl font-semibold mb-4">操作</h2>
            <div className="space-y-4">
              <button
                onClick={handlePreview}
                disabled={isLoading}
                className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? '加载中...' : '预览更改'}
              </button>
              
              <div className="text-sm text-gray-600">
                <p>• 点击&quot;预览更改&quot;查看待发布的内容</p>
                <p>• 确认无误后进行推送</p>
                <p>• 推送过程大约需要 1-2 分钟</p>
              </div>
            </div>
          </div>
        )}

        {/* 预览结果 */}
        {showPreview && !promotionResult && (
          <div className="bg-white rounded-lg shadow p-6 mb-8">
            <h2 className="text-xl font-semibold mb-4">
              待发布更改 ({changes.length} 项)
            </h2>
            
            {changes.length === 0 ? (
              <p className="text-gray-600">没有需要发布的更改</p>
            ) : (
              <>
                <div className="max-h-96 overflow-y-auto mb-6">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-2">类型</th>
                        <th className="text-left py-2">ID</th>
                        <th className="text-left py-2">操作</th>
                      </tr>
                    </thead>
                    <tbody>
                      {changes.map((change, index) => (
                        <tr key={index} className="border-b">
                          <td className="py-2">{change.type}</td>
                          <td className="py-2 text-sm text-gray-600">
                            {change.id}
                          </td>
                          <td className="py-2">
                            <span className={`inline-flex px-2 py-1 text-xs rounded ${
                              change.action === 'new' 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-yellow-100 text-yellow-800'
                            }`}>
                              {change.action === 'new' ? '新增' : '更新'}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                
                <div className="flex gap-4">
                  <button
                    onClick={handlePromote}
                    disabled={isLoading}
                    className="flex-1 bg-green-600 text-white py-3 px-6 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isLoading ? '推送中...' : '确认推送到生产'}
                  </button>
                  
                  <button
                    onClick={() => setShowPreview(false)}
                    disabled={isLoading}
                    className="flex-1 bg-gray-300 text-gray-700 py-3 px-6 rounded-lg hover:bg-gray-400 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    取消
                  </button>
                </div>
              </>
            )}
          </div>
        )}

        {/* 推送结果 */}
        {promotionResult && (
          <div className={`bg-white rounded-lg shadow p-6 mb-8 ${
            promotionResult.success ? 'border-green-500' : 'border-red-500'
          } border-2`}>
            <div className="text-center">
              {promotionResult.success ? (
                <svg className="w-16 h-16 text-green-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              ) : (
                <svg className="w-16 h-16 text-red-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              )}
              
              <h3 className={`text-2xl font-semibold mb-2 ${
                promotionResult.success ? 'text-green-600' : 'text-red-600'
              }`}>
                {promotionResult.success ? '推送成功！' : '推送失败'}
              </h3>
              
              <p className="text-gray-600 mb-4">{promotionResult.message}</p>
              
              {promotionResult.success && (
                <p className="text-sm text-gray-500">3秒后自动跳转到首页...</p>
              )}
            </div>
          </div>
        )}

        {/* 帮助信息 */}
        <div className="bg-blue-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-2">需要帮助？</h3>
          <p className="text-sm text-gray-700 mb-2">
            如果您在使用过程中遇到任何问题，请联系技术支持：
          </p>
          <p className="text-sm text-gray-700">
            邮箱：<EMAIL> | 电话：xxx-xxxx-xxxx
          </p>
        </div>
      </div>
    </div>
  )
}

export default PromotePageClient