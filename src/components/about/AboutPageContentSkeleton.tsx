export function AboutPageContentSkeleton() {
  return (
    <div className="container mx-auto px-4 py-16 animate-pulse">
      <div className="max-w-4xl mx-auto">
        {/* Hero Section Skeleton */}
        <div className="text-center mb-16">
          <div className="h-10 bg-gray-200 rounded-lg w-96 mx-auto mb-4" />
          <div className="h-6 bg-gray-200 rounded-lg max-w-2xl mx-auto" />
          <div className="mt-8 h-96 bg-gray-200 rounded-lg shadow-lg" />
        </div>

        {/* Content Sections Skeleton */}
        <div className="space-y-16">
          {/* Text Section */}
          <div className="prose prose-lg max-w-none">
            <div className="h-8 bg-gray-200 rounded w-48 mb-6" />
            <div className="space-y-4">
              <div className="h-4 bg-gray-200 rounded w-full" />
              <div className="h-4 bg-gray-200 rounded w-5/6" />
              <div className="h-4 bg-gray-200 rounded w-4/5" />
              <div className="h-4 bg-gray-200 rounded w-full" />
            </div>
          </div>

          {/* Image Text Section */}
          <div className="grid md:grid-cols-2 gap-8 items-center">
            <div>
              <div className="h-8 bg-gray-200 rounded w-48 mb-6" />
              <div className="space-y-4">
                <div className="h-4 bg-gray-200 rounded w-full" />
                <div className="h-4 bg-gray-200 rounded w-5/6" />
                <div className="h-4 bg-gray-200 rounded w-4/5" />
              </div>
            </div>
            <div className="h-64 bg-gray-200 rounded-lg shadow-lg" />
          </div>

          {/* Team Section Skeleton */}
          <div>
            <div className="h-8 bg-gray-200 rounded w-64 mx-auto mb-8" />
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[1, 2, 3].map((i) => (
                <div key={i} className="text-center">
                  <div className="w-32 h-32 bg-gray-200 rounded-full mx-auto mb-4" />
                  <div className="h-6 bg-gray-200 rounded w-32 mx-auto mb-2" />
                  <div className="h-5 bg-gray-200 rounded w-24 mx-auto mb-3" />
                  <div className="h-4 bg-gray-200 rounded w-full" />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}