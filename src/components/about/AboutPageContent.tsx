'use client'

import { AboutPage, LocaleString, LocaleText, LocaleBlockContent } from '@/types/sanity'
import Image from 'next/image'
import { resolveImageUrl, resolveImageAlt } from '@/lib/sanity/image'
import PortableText from '@/components/ui/PortableText'

interface AboutPageContentProps {
  data: AboutPage
  locale: string
}

// Helper function to get localized content
function getLocalizedContent(
  content: LocaleString | LocaleText | undefined, 
  locale: string
): string | undefined {
  if (!content) return undefined
  return content[locale as keyof typeof content] || content.zh || content.en
}

// Helper function for block content with string conversion support
function getLocalizedBlocks(
  content: LocaleBlockContent | LocaleString | LocaleText | undefined,
  locale: string
): any[] | undefined {
  if (!content) return undefined
  
  // Get the localized content
  const localizedContent = content[locale as keyof typeof content] || content.zh || content.en
  
  // If it's already an array (block content), return it
  if (Array.isArray(localizedContent)) {
    return localizedContent
  }
  
  // If it's a string, convert it to block content format
  if (typeof localizedContent === 'string' && localizedContent.trim()) {
    return [
      {
        _type: 'block',
        _key: `converted-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        style: 'normal',
        children: [
          {
            _type: 'span',
            text: localizedContent,
            marks: []
          }
        ]
      }
    ]
  }
  
  return undefined
}

// Helper function for secure image URLs
function getSecureImageUrl(imageData: any, width?: number, height?: number): string {
  // 中文注释：统一使用 resolveImageUrl 解析图片
  return resolveImageUrl(imageData, width, height)
}


export default function AboutPageContent({ data, locale }: AboutPageContentProps) {
  const title = getLocalizedContent(data.title, locale)
  const heroHeadlineBlocks = getLocalizedBlocks(data.heroSection?.headline, locale)
  const heroSubtitleBlocks = getLocalizedBlocks(data.heroSection?.subtitle, locale)

  return (
    <div className="container mx-auto px-4 py-16">
      <div className="max-w-4xl mx-auto">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {heroHeadlineBlocks && heroHeadlineBlocks.length > 0 ? (
              <PortableText value={heroHeadlineBlocks} />
            ) : (
              title
            )}
          </h1>
          {heroSubtitleBlocks && heroSubtitleBlocks.length > 0 && (
            <div className="text-xl text-gray-600 max-w-2xl mx-auto">
              <PortableText value={heroSubtitleBlocks} className="prose-xl" />
            </div>
          )}
          {data.heroSection?.heroImage && (
            <div className="mt-8">
              <Image
                src={getSecureImageUrl(data.heroSection.heroImage, 800, 400)}
                alt={title || 'About us'}
                width={800}
                height={400}
                className="mx-auto rounded-lg shadow-lg"
              />
            </div>
          )}
        </div>

        {/* Content Sections */}
        {data.sections && data.sections.length > 0 && (
          <div className="space-y-16">
            {data.sections.map((section, index) => (
              <div key={index}>
                {section._type === 'textSection' && (
                  <div className="prose prose-lg max-w-none">
                    {section.sectionTitle && (
                      <h2 className="text-3xl font-semibold mb-6">
                        {getLocalizedContent(section.sectionTitle, locale)}
                      </h2>
                    )}
                    {section.content && (
                      <div className="text-gray-600">
                        {(() => {
                          const blocks = getLocalizedBlocks(section.content, locale)
                          return blocks && blocks.length > 0 ? (
                            <PortableText value={blocks} className="prose-lg" />
                          ) : (
                            <p>Content not available</p>
                          )
                        })()}
                      </div>
                    )}
                  </div>
                )}

                {section._type === 'imageTextSection' && (
                  <div className={`grid md:grid-cols-2 gap-8 items-center ${
                    section.imagePosition === 'left' ? 'md:grid-flow-col-dense' : ''
                  }`}>
                    <div className={section.imagePosition === 'left' ? 'md:order-2' : ''}>
                      {section.sectionTitle && (
                        <h2 className="text-3xl font-semibold mb-6">
                          {getLocalizedContent(section.sectionTitle, locale)}
                        </h2>
                      )}
                      {section.content && (
                        <div className="text-gray-600">
                          {(() => {
                            const blocks = getLocalizedBlocks(section.content, locale)
                            return blocks && blocks.length > 0 ? (
                              <PortableText value={blocks} className="prose-lg" />
                            ) : (
                              <p>Content not available</p>
                            )
                          })()}
                        </div>
                      )}
                    </div>
                    {section.image && (
                      <div className={section.imagePosition === 'left' ? 'md:order-1' : ''}>
                        <Image
                          src={getSecureImageUrl(section.image, 600, 400)}
                          alt={getLocalizedContent(section.sectionTitle, locale) || 'Section image'}
                          width={600}
                          height={400}
                          className="rounded-lg shadow-lg w-full h-auto"
                        />
                      </div>
                    )}
                  </div>
                )}

                {section._type === 'teamSection' && (
                  <div>
                    {section.sectionTitle && (
                      <h2 className="text-3xl font-semibold mb-8 text-center">
                        {getLocalizedContent(section.sectionTitle, locale)}
                      </h2>
                    )}
                    {section.teamMembers && section.teamMembers.length > 0 && (
                      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                        {section.teamMembers.map((member, memberIndex) => (
                          <div key={memberIndex} className="text-center">
                            {member.photo && (
                              <div className="mb-4">
                                <Image
                                  src={getSecureImageUrl(member.photo, 200, 200)}
                                  alt={getLocalizedContent(member.name, locale) || 'Team member'}
                                  width={200}
                                  height={200}
                                  className="mx-auto rounded-full object-cover w-32 h-32"
                                />
                              </div>
                            )}
                            <h3 className="text-xl font-semibold mb-2">
                              {getLocalizedContent(member.name, locale)}
                            </h3>
                            {member.position && (
                              <p className="text-blue-600 font-medium mb-3">
                                {getLocalizedContent(member.position, locale)}
                              </p>
                            )}
                            {member.bio && (
                              <p className="text-gray-600 text-sm">
                                {getLocalizedContent(member.bio, locale)}
                              </p>
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}