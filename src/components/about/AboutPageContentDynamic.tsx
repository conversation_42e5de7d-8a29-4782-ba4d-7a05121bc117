'use client'

import dynamic from 'next/dynamic'
import { AboutPageContentSkeleton } from './AboutPageContentSkeleton'
import type { AboutPage } from '@/types/sanity'

// Dynamically import AboutPageContent to reduce initial bundle size
// About page content is often static and not immediately critical
const AboutPageContent = dynamic(
  () => import('./AboutPageContent'),
  {
    ssr: true, // Keep SSR for SEO as about pages should be indexed
    loading: () => <AboutPageContentSkeleton />,
  }
)

interface AboutPageContentDynamicProps {
  data: AboutPage
  locale: string
}

export function AboutPageContentDynamic(props: AboutPageContentDynamicProps) {
  return <AboutPageContent {...props} />
}

export default AboutPageContentDynamic