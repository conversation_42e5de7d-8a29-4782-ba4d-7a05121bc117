import createMiddleware from 'next-intl/middleware'
import { routing } from '@/i18n/routing'
import { NextRequest, NextResponse } from 'next/server'

const handleI18nRouting = createMiddleware(routing)

export default function middleware(request: NextRequest) {
  const response = handleI18nRouting(request)
  
  // Add debug headers for locale detection
  if (process.env.NODE_ENV !== 'production') {
    const url = request.nextUrl.clone()
    const locale = url.pathname.split('/')[1]
    response.headers.set('x-debug-locale', locale || 'none')
    response.headers.set('x-debug-pathname', url.pathname)
  }
  
  return response
}

export const config = {
  // Match only internationalized pathnames - improved pattern for better locale detection
  matcher: [
    // Match all pathnames except for
    // - api routes
    // - _next (Next.js internals)
    // - _vercel (Vercel internals)
    // - All files in the public folder
    '/((?!api|_next|_vercel|.*\\..*|favicon.ico|robots.txt|sitemap.xml).*)',
    // However, match all pathnames within `/` folder
    '/'
  ]
}