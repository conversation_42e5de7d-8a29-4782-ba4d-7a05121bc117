{"permissions": {"allow": ["Bash(rm:*)", "Bash(sanity projects:*)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(true)", "Bash(/dev/null)", "<PERSON><PERSON>(pkill:*)", "Bash(NODE_USE_ENV_PROXY=1 HTTPS_PROXY=http://127.0.0.1:6152 node test-sanity-simple.js)", "Bash(open .next/analyze/client.html)", "Bash(NO_PROXY=localhost HTTP_PROXY= HTTPS_PROXY= curl -s -I http://localhost:3000/sw.js)", "mcp__linear-server__search_documentation", "mcp__linear-server__get_issue", "mcp__linear-server__update_issue", "mcp__linear-server__list_issue_statuses", "mcp__linear-server__create_comment"], "deny": []}}