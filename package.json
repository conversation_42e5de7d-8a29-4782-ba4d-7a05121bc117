{"name": "ani-globe", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "analyze": "ANALYZE=true npm run build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "promote": "npx tsx scripts/promote-content.ts", "promote:dry-run": "npx tsx scripts/promote-content.ts --dry-run", "promote:force": "npx tsx scripts/promote-content.ts --force", "verify-datasets": "node scripts/verify-datasets.js", "migrate:tags": "node scripts/migrate-product-tags.js", "migrate:tags:dry-run": "node scripts/migrate-product-tags.js --dry-run", "fix:tags": "node scripts/fix-product-tags.js", "bundle-size": "node scripts/check-bundle-size.js", "build:check": "npm run build && npm run bundle-size", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:debug": "playwright test --debug", "test:sanity": "playwright test tests/e2e/sanity", "test:main": "playwright test tests/e2e/main-site", "test:api": "playwright test tests/e2e/api", "test:all": "npm run test && npm run test:e2e"}, "dependencies": {"@heroicons/react": "^2.2.0", "@next/bundle-analyzer": "^15.3.5", "@portabletext/react": "^3.1.0", "@sanity/client": "^6.29.1", "@sanity/image-url": "^1.1.0", "@sanity/ui": "^3.0.7", "@types/react-window": "^1.8.8", "clsx": "^2.1.1", "critters": "^0.0.25", "dotenv": "^17.1.0", "framer-motion": "^11.11.17", "global-agent": "^3.0.0", "https-proxy-agent": "^7.0.6", "next": "15.3.5", "next-intl": "^3.22.0", "proxy-agent": "^6.5.0", "react": "^18", "react-dom": "^18", "react-error-boundary": "^6.0.0", "react-window": "^1.8.11", "sanity": "^4.4.1", "sharp": "^0.33.5", "styled-components": "^6.1.19", "tailwind-merge": "^3.3.1", "undici": "^7.11.0"}, "devDependencies": {"@jridgewell/sourcemap-codec": "^1.5.0", "@netlify/next": "^1.4.9", "@netlify/plugin-nextjs": "^5.11.4", "@playwright/test": "^1.54.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.21", "eslint": "^8", "eslint-config-next": "14.2.15", "playwright": "^1.54.1", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}