import { defineConfig, devices } from '@playwright/test';

/**
 * Playwright 端到端测试配置
 * @see https://playwright.dev/docs/test-configuration
 */
export default defineConfig({
  testDir: './tests/e2e',
  /* 完全并行运行测试 */
  fullyParallel: true,
  /* 如果意外退出测试进程则失败构建 */
  forbidOnly: !!process.env.CI,
  /* 仅在 CI 上重试 */
  retries: process.env.CI ? 2 : 0,
  /* 在 CI 上选择退出并行测试 */
  workers: process.env.CI ? 1 : undefined,
  /* 报告器配置 */
  reporter: [
    ['html'],
    ['list'],
    ['json', { outputFile: 'test-results/results.json' }]
  ],
  /* 所有测试的共享设置 */
  use: {
    /* 收集跟踪当重试失败的测试时 */
    trace: 'on-first-retry',
    /* 失败时截图 */
    screenshot: 'only-on-failure',
    /* 在失败时录制视频 */
    video: 'retain-on-failure',
    /* 测试超时时间 */
    actionTimeout: 10000,
    navigationTimeout: 30000,
    /* 绕过代理设置 */
    bypassCSP: true,
    ignoreHTTPSErrors: true,
    extraHTTPHeaders: {
      'Accept-Language': 'zh-CN,zh;q=0.9',
    },
  },

  /* 配置不同的项目 */
  projects: [
    /* Sanity Studio 测试 */
    {
      name: 'sanity-studio',
      use: { 
        ...devices['Desktop Chrome'],
        baseURL: 'http://localhost:3333',
        /* Sanity Studio 特定的配置 */
        viewport: { width: 1920, height: 1080 },
      },
      testMatch: '**/sanity/**/*.spec.ts',
    },

    /* 主站点测试 - 桌面端 */
    {
      name: 'main-site-desktop',
      use: { 
        ...devices['Desktop Chrome'],
        baseURL: 'http://localhost:3000',
        locale: 'zh',
      },
      testMatch: '**/main-site/**/*.spec.ts',
    },

    /* 主站点测试 - 移动端 */
    {
      name: 'main-site-mobile',
      use: { 
        ...devices['iPhone 13'],
        baseURL: 'http://localhost:3000',
        locale: 'zh',
      },
      testMatch: '**/main-site/**/*.spec.ts',
    },

    /* API 测试 */
    {
      name: 'api-tests',
      use: { 
        baseURL: 'http://localhost:3000',
      },
      testMatch: '**/api/**/*.spec.ts',
    },

    /* 跨浏览器测试 */
    {
      name: 'firefox',
      use: { 
        ...devices['Desktop Firefox'],
        baseURL: 'http://localhost:3000',
      },
      testMatch: '**/main-site/**/*.spec.ts',
    },

    {
      name: 'webkit',
      use: { 
        ...devices['Desktop Safari'],
        baseURL: 'http://localhost:3000',
      },
      testMatch: '**/main-site/**/*.spec.ts',
    },
  ],

  /* 运行本地开发服务器在测试之前 */
  webServer: [
    {
      command: 'npm run dev',
      port: 3000,
      timeout: 120 * 1000,
      reuseExistingServer: !process.env.CI,
      env: {
        ...process.env,
        NO_PROXY: 'localhost,127.0.0.1',
        no_proxy: 'localhost,127.0.0.1',
      },
    },
    {
      command: 'cd sanity && npm run dev',
      port: 3333,
      timeout: 120 * 1000,
      reuseExistingServer: !process.env.CI,
      env: {
        ...process.env,
        NO_PROXY: 'localhost,127.0.0.1',
        no_proxy: 'localhost,127.0.0.1',
        SANITY_STUDIO_PROJECT_ID: '4za4x22i',
        SANITY_STUDIO_DATASET: 'production',
      },
    }
  ],
});