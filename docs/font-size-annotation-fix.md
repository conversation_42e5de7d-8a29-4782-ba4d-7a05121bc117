# Sanity 富文本字体大小注释修复

## 问题描述
在 Sanity Studio 的富文本编辑器中：
1. 当对文本应用字体大小时，编辑器显示的是"字体大小"和"默认 (16px)"这样的输入框，而不是实际的文本内容
2. 悬停在带字体大小格式的文本上时，工具提示显示"字号"而不是实际的字体大小值
3. 删除图标需要一个工具提示显示"清除格式"

## 问题原因
1. Sanity v3 的注释（annotation）组件需要使用 `props.renderDefault(props)` 来正确渲染被注释的内容
2. 工具提示文本硬编码为"字号: [size]px"，没有使用正确的标签
3. Sanity 不提供直接自定义注释工具栏按钮的方法

## 解决方案

### 1. 更新 FontSizeAnnotation 组件
在 `/sanity/schemas/components/FontSizePreview.tsx` 中：

- 添加了多语言字体大小标签
- 检测当前 Sanity Studio 的语言设置
- 根据语言显示相应的工具提示（如"小号 (14px)"而不是"字号: 14px"）
- 添加了悬停效果以提高交互性

### 2. 创建注释工具提示插件
在 `/sanity/plugins/annotation-tooltips.ts` 中：

创建了一个 Sanity 插件，通过全局 CSS 为删除/清除格式按钮添加工具提示。该插件：
- 为删除注释按钮添加悬停时的工具提示
- 支持多语言（中文："清除格式"，英文："Clear Formatting"，阿拉伯语："مسح التنسيق"）
- 使用 CSS 伪元素实现，不影响 Sanity Studio 的内部功能

### 3. 配置已正确使用组件
在 `/sanity/schemas/helpers/blockContentConfig.tsx` 中，注释配置已正确引用：

```typescript
components: {
  annotation: FontSizeAnnotation
}
```

## 使用说明

1. **在 Sanity Studio 中**：
   - 选中文本
   - 点击字体大小按钮（T图标）
   - 选择所需的字体大小
   - 文本将显示蓝色背景，表示已应用字体大小
   - 悬停在带格式的文本上会显示实际的字体大小（如"小号 (14px)"）
   - 悬停在删除按钮上会显示"清除格式"工具提示

2. **前端渲染**：
   - 使用 PortableText 组件自动渲染正确的字体大小
   - 支持 6 种字体大小：14px、16px、20px、24px、32px、40px

## 视觉效果
- **编辑器中**：
  - 应用字体大小的文本显示为蓝色文字，浅蓝色背景
  - 悬停时背景颜色加深，提供视觉反馈
  - 工具提示显示实际的字体大小值
- **前端显示**：文本以实际的字体大小渲染

## 注意事项
- 修改后需要重新构建 Sanity Studio：`cd sanity && npm run build`
- 确保 Sanity Studio 已刷新以应用更改
- 工具提示插件使用全局 CSS，可能需要清除浏览器缓存才能看到效果