# Schema 迁移指南

本指南提供了安全地扩展和迁移 Sanity schema 的最佳实践，确保数据完整性和系统稳定性。

## 📋 目录
- [迁移原则](#迁移原则)
- [渐进式迁移策略](#渐进式迁移策略)
- [数据备份与恢复](#数据备份与恢复)
- [迁移脚本示例](#迁移脚本示例)
- [回滚策略](#回滚策略)
- [性能考虑](#性能考虑)
- [测试验证](#测试验证)

## 迁移原则

### 1. 向前兼容性
- 新字段必须设为可选（optional）
- 不要删除现有字段，而是标记为废弃
- 保持现有字段的数据类型不变
- 确保现有查询仍然有效

### 2. 分阶段实施
- 将大型变更分解为小步骤
- 每个阶段都要可以独立测试
- 在生产环境中逐步推出
- 监控每个阶段的性能影响

### 3. 数据完整性
- 在迁移前进行完整备份
- 验证所有数据约束
- 确保引用完整性
- 提供数据验证工具

## 渐进式迁移策略

### 阶段1：Schema 扩展
```typescript
// 1. 首先添加新的schema类型（可选）
// sanity/schemas/character.ts
export default defineType({
  name: 'character',
  title: '角色',
  type: 'document',
  fields: [
    // ... 新字段定义
  ]
})

// 2. 在现有schema中添加可选字段
// sanity/schemas/product.ts
export default defineType({
  name: 'product',
  title: '产品',
  type: 'document',
  fields: [
    // ... 现有字段
    defineField({
      name: 'characters',
      title: '相关角色',
      type: 'array',
      of: [
        {
          type: 'reference',
          to: [{type: 'character'}]
        }
      ]
      // 注意：不设置 validation: Rule => Rule.required()
    }),
    defineField({
      name: 'manufacturer',
      title: '制造商',
      type: 'reference',
      to: [{type: 'manufacturer'}]
      // 可选字段
    })
  ]
})
```

### 阶段2：数据迁移
```typescript
// migration/phase2-data-migration.ts
import {createClient} from '@sanity/client'

const client = createClient({
  projectId: 'your-project-id',
  dataset: 'production',
  apiVersion: '2023-01-01',
  token: process.env.SANITY_API_TOKEN,
  useCdn: false
})

export async function migratePhase2() {
  console.log('开始阶段2数据迁移...')
  
  // 1. 创建默认制造商
  const defaultManufacturer = await client.create({
    _type: 'manufacturer',
    name: {
      zh: '未知制造商',
      en: 'Unknown Manufacturer',
      ar: 'الشركة المصنعة غير معروفة'
    },
    slug: {
      current: 'unknown-manufacturer'
    },
    country: 'OTHER',
    isActive: true,
    _createdAt: new Date().toISOString()
  })
  
  console.log(`创建默认制造商: ${defaultManufacturer._id}`)
  
  // 2. 为现有产品添加制造商引用
  const products = await client.fetch(`
    *[_type == "product" && !defined(manufacturer)]
  `)
  
  console.log(`找到 ${products.length} 个需要更新的产品`)
  
  // 批量更新（每次50个）
  const batchSize = 50
  for (let i = 0; i < products.length; i += batchSize) {
    const batch = products.slice(i, i + batchSize)
    const transaction = client.transaction()
    
    batch.forEach(product => {
      transaction.patch(product._id).set({
        manufacturer: {
          _ref: defaultManufacturer._id,
          _type: 'reference'
        }
      })
    })
    
    await transaction.commit()
    console.log(`更新了产品 ${i + 1} - ${Math.min(i + batchSize, products.length)}`)
  }
  
  console.log('阶段2数据迁移完成')
}
```

### 阶段3：查询更新
```typescript
// 3. 更新查询以使用新字段
// src/lib/sanity/queries.ts

// 旧查询（保持兼容）
export const legacyProductQuery = `
  *[_type == "product" && isPublished == true] {
    _id,
    name,
    "slug": slug.current,
    gallery, // mainImage is now gallery[0]
    price,
    currency,
    category->{
      name,
      "slug": slug.current
    }
  }
`

// 新查询（使用扩展字段）
export const enhancedProductQuery = `
  *[_type == "product" && isPublished == true] {
    _id,
    name,
    "slug": slug.current,
    gallery, // mainImage is now gallery[0]
    price,
    currency,
    category->{
      name,
      "slug": slug.current
    },
    // 安全访问新字段
    characters[]->{
      name,
      "slug": slug.current
    },
    manufacturer->{
      name,
      "slug": slug.current
    }
  }
`

// 兼容性查询（自动回退）
export const compatibleProductQuery = `
  *[_type == "product" && isPublished == true] {
    _id,
    name,
    "slug": slug.current,
    gallery, // mainImage is now gallery[0]
    price,
    currency,
    category->{
      name,
      "slug": slug.current
    },
    // 使用 coalesce 提供默认值
    "characters": coalesce(characters[]->{
      name,
      "slug": slug.current
    }, []),
    "manufacturer": coalesce(manufacturer->{
      name,
      "slug": slug.current
    }, {
      "name": {"zh": "未知制造商", "en": "Unknown"},
      "slug": {"current": "unknown"}
    })
  }
`
```

## 数据备份与恢复

### 1. 完整备份
```bash
#!/bin/bash
# scripts/backup-dataset.sh

# 设置环境变量
export SANITY_PROJECT_ID="your-project-id"
export SANITY_DATASET="production"
export BACKUP_DIR="./backups/$(date +%Y%m%d_%H%M%S)"

# 创建备份目录
mkdir -p "$BACKUP_DIR"

# 导出数据
echo "开始备份数据集..."
sanity dataset export "$SANITY_DATASET" "$BACKUP_DIR/dataset.tar.gz"

# 导出schema
echo "备份schema..."
cp -r sanity/schemas "$BACKUP_DIR/schemas"

# 创建备份清单
echo "创建备份清单..."
cat > "$BACKUP_DIR/manifest.json" << EOF
{
  "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "projectId": "$SANITY_PROJECT_ID",
  "dataset": "$SANITY_DATASET",
  "version": "$(git rev-parse HEAD)",
  "files": [
    "dataset.tar.gz",
    "schemas/"
  ]
}
EOF

echo "备份完成: $BACKUP_DIR"
```

### 2. 增量备份
```typescript
// scripts/incremental-backup.ts
import {createClient} from '@sanity/client'
import {writeFileSync} from 'fs'

const client = createClient({
  projectId: process.env.SANITY_PROJECT_ID!,
  dataset: process.env.SANITY_DATASET!,
  apiVersion: '2023-01-01',
  token: process.env.SANITY_API_TOKEN!,
  useCdn: false
})

export async function incrementalBackup(since: string) {
  const timestamp = new Date().toISOString()
  
  // 获取自上次备份以来的变更
  const changes = await client.fetch(`
    *[_updatedAt >= $since] {
      _id,
      _type,
      _updatedAt,
      _createdAt,
      _rev
    }
  `, {since})
  
  // 获取完整文档
  const documents = await client.fetch(`
    *[_id in $ids]
  `, {ids: changes.map(c => c._id)})
  
  // 保存增量备份
  const backup = {
    timestamp,
    since,
    changes: changes.length,
    documents
  }
  
  writeFileSync(
    `./backups/incremental-${timestamp.replace(/:/g, '-')}.json`,
    JSON.stringify(backup, null, 2)
  )
  
  console.log(`增量备份完成: ${changes.length} 个变更`)
}
```

## 迁移脚本示例

### 1. 分类层级迁移
```typescript
// migration/category-hierarchy.ts
import {createClient, type SanityDocument} from '@sanity/client'

interface OldCategory extends SanityDocument {
  name: {zh: string, en: string}
  slug: {current: string}
  parentSlug?: string // 旧的平面结构
}

interface NewCategory extends SanityDocument {
  name: {zh: string, en: string}
  slug: {current: string}
  parent?: {_ref: string, _type: 'reference'}
  level: number
  path: string
}

export async function migrateCategoryHierarchy() {
  const client = createClient({
    projectId: process.env.SANITY_PROJECT_ID!,
    dataset: process.env.SANITY_DATASET!,
    apiVersion: '2023-01-01',
    token: process.env.SANITY_API_TOKEN!,
    useCdn: false
  })
  
  console.log('开始分类层级迁移...')
  
  // 1. 获取所有旧分类
  const oldCategories = await client.fetch(`
    *[_type == "category"] | order(name.zh asc)
  `) as OldCategory[]
  
  // 2. 创建slug到ID的映射
  const slugToIdMap = new Map<string, string>()
  oldCategories.forEach(cat => {
    slugToIdMap.set(cat.slug.current, cat._id)
  })
  
  // 3. 构建层级结构
  const rootCategories: NewCategory[] = []
  const childCategories: NewCategory[] = []
  
  oldCategories.forEach(cat => {
    const newCat: NewCategory = {
      ...cat,
      level: 0,
      path: cat.slug.current
    }
    
    if (cat.parentSlug) {
      const parentId = slugToIdMap.get(cat.parentSlug)
      if (parentId) {
        newCat.parent = {_ref: parentId, _type: 'reference'}
        newCat.level = 1
        newCat.path = `${cat.parentSlug}/${cat.slug.current}`
        childCategories.push(newCat)
      }
    } else {
      rootCategories.push(newCat)
    }
  })
  
  // 4. 批量更新
  const transaction = client.transaction()
  
  [...rootCategories, ...childCategories].forEach(cat => {
    transaction.patch(cat._id).set({
      level: cat.level,
      path: cat.path,
      ...(cat.parent ? {parent: cat.parent} : {})
    })
  })
  
  await transaction.commit()
  
  console.log(`迁移完成: ${rootCategories.length} 个根分类, ${childCategories.length} 个子分类`)
}
```

### 2. 产品规格迁移
```typescript
// migration/product-specifications.ts
export async function migrateProductSpecifications() {
  const client = createClient({
    projectId: process.env.SANITY_PROJECT_ID!,
    dataset: process.env.SANITY_DATASET!,
    apiVersion: '2023-01-01',
    token: process.env.SANITY_API_TOKEN!,
    useCdn: false
  })
  
  console.log('开始产品规格迁移...')
  
  // 1. 获取所有产品
  const products = await client.fetch(`
    *[_type == "product"] {
      _id,
      tags,
      name,
      "slug": slug.current
    }
  `)
  
  // 2. 从标签中提取规格信息
  const transaction = client.transaction()
  
  products.forEach(product => {
    const specifications: any = {}
    
    // 从标签中提取比例信息
    const scaleTag = product.tags?.find((tag: string) => tag.match(/^1\/\d+$/))
    if (scaleTag) {
      specifications.scale = scaleTag
    }
    
    // 从标签中提取材质信息
    const materialTags = product.tags?.filter((tag: string) => 
      ['PVC', 'ABS', 'resin', 'metal', 'fabric'].includes(tag)
    )
    if (materialTags?.length > 0) {
      specifications.material = materialTags
    }
    
    // 从名称中提取高度信息（如果有）
    const heightMatch = product.name.zh?.match(/(\d+)mm|(\d+)cm/)
    if (heightMatch) {
      const height = heightMatch[1] ? parseInt(heightMatch[1]) : parseInt(heightMatch[2]) * 10
      specifications.height = height
    }
    
    // 只有当有规格信息时才更新
    if (Object.keys(specifications).length > 0) {
      transaction.patch(product._id).set({
        specifications
      })
    }
  })
  
  await transaction.commit()
  
  console.log(`产品规格迁移完成: ${products.length} 个产品`)
}
```

### 3. 多语言内容迁移
```typescript
// migration/multilingual-content.ts
export async function migrateMultilingualContent() {
  const client = createClient({
    projectId: process.env.SANITY_PROJECT_ID!,
    dataset: process.env.SANITY_DATASET!,
    apiVersion: '2023-01-01',
    token: process.env.SANITY_API_TOKEN!,
    useCdn: false
  })
  
  console.log('开始多语言内容迁移...')
  
  // 1. 获取所有需要迁移的产品
  const products = await client.fetch(`
    *[_type == "product" && (!defined(name.en) || !defined(name.ar))] {
      _id,
      name,
      shortDescription,
      "slug": slug.current
    }
  `)
  
  console.log(`找到 ${products.length} 个需要多语言迁移的产品`)
  
  // 2. 批量处理
  const batchSize = 10
  for (let i = 0; i < products.length; i += batchSize) {
    const batch = products.slice(i, i + batchSize)
    const transaction = client.transaction()
    
    for (const product of batch) {
      const updates: any = {}
      
      // 如果缺少英文名称，使用中文名称作为后备
      if (!product.name?.en && product.name?.zh) {
        updates.name = {
          ...product.name,
          en: product.name.zh // 临时使用中文，之后可以人工翻译
        }
      }
      
      // 如果缺少阿拉伯文名称，使用中文名称作为后备
      if (!product.name?.ar && product.name?.zh) {
        updates.name = {
          ...updates.name || product.name,
          ar: product.name.zh // 临时使用中文，之后可以人工翻译
        }
      }
      
      // 类似处理描述
      if (!product.shortDescription?.en && product.shortDescription?.zh) {
        updates.shortDescription = {
          ...product.shortDescription,
          en: product.shortDescription.zh
        }
      }
      
      if (!product.shortDescription?.ar && product.shortDescription?.zh) {
        updates.shortDescription = {
          ...updates.shortDescription || product.shortDescription,
          ar: product.shortDescription.zh
        }
      }
      
      if (Object.keys(updates).length > 0) {
        transaction.patch(product._id).set(updates)
      }
    }
    
    await transaction.commit()
    console.log(`处理了产品 ${i + 1} - ${Math.min(i + batchSize, products.length)}`)
  }
  
  console.log('多语言内容迁移完成')
}
```

## 回滚策略

### 1. 自动回滚脚本
```typescript
// migration/rollback.ts
interface MigrationRecord {
  id: string
  timestamp: string
  changes: Array<{
    documentId: string
    action: 'create' | 'update' | 'delete'
    previousState?: any
    newState?: any
  }>
}

export class MigrationManager {
  private client: SanityClient
  private migrationHistory: MigrationRecord[] = []
  
  constructor(client: SanityClient) {
    this.client = client
  }
  
  async recordMigration(migrationId: string, changes: MigrationRecord['changes']) {
    const record: MigrationRecord = {
      id: migrationId,
      timestamp: new Date().toISOString(),
      changes
    }
    
    this.migrationHistory.push(record)
    
    // 保存到Sanity
    await this.client.create({
      _type: 'migrationRecord',
      _id: `migration.${migrationId}`,
      ...record
    })
  }
  
  async rollback(migrationId: string) {
    const record = this.migrationHistory.find(r => r.id === migrationId)
    if (!record) {
      throw new Error(`Migration ${migrationId} not found`)
    }
    
    console.log(`开始回滚迁移: ${migrationId}`)
    
    const transaction = this.client.transaction()
    
    // 反向应用变更
    for (const change of record.changes.reverse()) {
      switch (change.action) {
        case 'create':
          // 创建操作 -> 删除
          transaction.delete(change.documentId)
          break
          
        case 'update':
          // 更新操作 -> 恢复到之前状态
          if (change.previousState) {
            transaction.createOrReplace({
              _id: change.documentId,
              ...change.previousState
            })
          }
          break
          
        case 'delete':
          // 删除操作 -> 重新创建
          if (change.previousState) {
            transaction.create(change.previousState)
          }
          break
      }
    }
    
    await transaction.commit()
    
    // 记录回滚
    await this.recordMigration(`rollback-${migrationId}`, 
      record.changes.map(c => ({
        documentId: c.documentId,
        action: c.action === 'create' ? 'delete' : 
                c.action === 'delete' ? 'create' : 'update',
        previousState: c.newState,
        newState: c.previousState
      }))
    )
    
    console.log(`回滚完成: ${migrationId}`)
  }
}
```

### 2. 数据恢复脚本
```bash
#!/bin/bash
# scripts/restore-dataset.sh

BACKUP_DIR="$1"
SANITY_PROJECT_ID="$2"
SANITY_DATASET="$3"

if [ -z "$BACKUP_DIR" ] || [ -z "$SANITY_PROJECT_ID" ] || [ -z "$SANITY_DATASET" ]; then
    echo "用法: $0 <backup_dir> <project_id> <dataset>"
    exit 1
fi

# 验证备份文件
if [ ! -f "$BACKUP_DIR/dataset.tar.gz" ]; then
    echo "错误: 备份文件不存在: $BACKUP_DIR/dataset.tar.gz"
    exit 1
fi

# 确认恢复操作
echo "警告: 这将完全替换数据集 $SANITY_DATASET 中的所有数据"
echo "备份位置: $BACKUP_DIR"
read -p "确认继续? (yes/no): " confirm

if [ "$confirm" != "yes" ]; then
    echo "操作已取消"
    exit 0
fi

# 恢复数据
echo "开始恢复数据..."
sanity dataset import "$BACKUP_DIR/dataset.tar.gz" "$SANITY_DATASET" --replace

echo "数据恢复完成"
```

## 性能考虑

### 1. 大批量迁移优化
```typescript
// 优化大批量操作
export async function optimizedBatchMigration() {
  const client = createClient({
    projectId: process.env.SANITY_PROJECT_ID!,
    dataset: process.env.SANITY_DATASET!,
    apiVersion: '2023-01-01',
    token: process.env.SANITY_API_TOKEN!,
    useCdn: false
  })
  
  const BATCH_SIZE = 100
  const CONCURRENT_BATCHES = 3
  
  // 获取总数
  const totalCount = await client.fetch(`count(*[_type == "product"])`)
  console.log(`总共需要处理 ${totalCount} 个产品`)
  
  // 分批处理
  const batches = []
  for (let i = 0; i < totalCount; i += BATCH_SIZE) {
    batches.push({
      start: i,
      end: Math.min(i + BATCH_SIZE, totalCount)
    })
  }
  
  // 并发处理批次
  const processBatch = async (batch: {start: number, end: number}) => {
    const products = await client.fetch(`
      *[_type == "product"] | order(_id)[${batch.start}..${batch.end - 1}]
    `)
    
    const transaction = client.transaction()
    
    products.forEach(product => {
      // 执行迁移逻辑
      transaction.patch(product._id).set({
        // ... 更新字段
      })
    })
    
    await transaction.commit()
    console.log(`处理了批次 ${batch.start}-${batch.end}`)
  }
  
  // 分组并发处理
  for (let i = 0; i < batches.length; i += CONCURRENT_BATCHES) {
    const currentBatches = batches.slice(i, i + CONCURRENT_BATCHES)
    await Promise.all(currentBatches.map(processBatch))
  }
  
  console.log('批量迁移完成')
}
```

### 2. 内存使用优化
```typescript
// 流式处理大数据集
export async function streamMigration() {
  const client = createClient({
    projectId: process.env.SANITY_PROJECT_ID!,
    dataset: process.env.SANITY_DATASET!,
    apiVersion: '2023-01-01',
    token: process.env.SANITY_API_TOKEN!,
    useCdn: false
  })
  
  let lastId = ''
  const batchSize = 50
  
  while (true) {
    // 使用游标分页，减少内存使用
    const query = lastId 
      ? `*[_type == "product" && _id > $lastId] | order(_id)[0..${batchSize - 1}]`
      : `*[_type == "product"] | order(_id)[0..${batchSize - 1}]`
    
    const products = await client.fetch(query, {lastId})
    
    if (products.length === 0) break
    
    // 处理当前批次
    const transaction = client.transaction()
    
    products.forEach(product => {
      // 迁移逻辑
      transaction.patch(product._id).set({
        // ... 更新字段
      })
    })
    
    await transaction.commit()
    
    lastId = products[products.length - 1]._id
    console.log(`处理到产品 ID: ${lastId}`)
    
    // 短暂休息，避免API限制
    await new Promise(resolve => setTimeout(resolve, 100))
  }
  
  console.log('流式迁移完成')
}
```

## 测试验证

### 1. 迁移前测试
```typescript
// tests/migration.test.ts
describe('Migration Tests', () => {
  beforeEach(async () => {
    // 在测试数据集上运行
    process.env.SANITY_DATASET = 'test'
  })
  
  test('schema compatibility', async () => {
    // 测试新schema是否兼容现有数据
    const client = createClient({
      projectId: process.env.SANITY_PROJECT_ID!,
      dataset: 'test',
      apiVersion: '2023-01-01',
      token: process.env.SANITY_API_TOKEN!,
      useCdn: false
    })
    
    // 创建测试数据
    const testProduct = await client.create({
      _type: 'product',
      name: {zh: '测试产品', en: 'Test Product'},
      slug: {current: 'test-product'},
      isPublished: true,
      stockStatus: 'in-stock'
    })
    
    // 测试查询是否正常工作
    const result = await client.fetch(`
      *[_type == "product" && _id == $id][0] {
        name,
        "slug": slug.current,
        // 测试新字段的安全访问
        characters[]->{name},
        manufacturer->{name}
      }
    `, {id: testProduct._id})
    
    expect(result).toBeDefined()
    expect(result.name.zh).toBe('测试产品')
    expect(result.characters).toBeDefined() // 应该是空数组
    expect(result.manufacturer).toBeDefined() // 应该是null
  })
  
  test('migration script dry run', async () => {
    // 测试迁移脚本（不实际修改数据）
    const client = createClient({
      projectId: process.env.SANITY_PROJECT_ID!,
      dataset: 'test',
      apiVersion: '2023-01-01',
      token: process.env.SANITY_API_TOKEN!,
      useCdn: false
    })
    
    // 创建测试数据
    const testProducts = await Promise.all([
      client.create({
        _type: 'product',
        name: {zh: '手办A', en: 'Figure A'},
        slug: {current: 'figure-a'},
        tags: ['1/8', 'PVC', 'anime']
      }),
      client.create({
        _type: 'product',
        name: {zh: '手办B', en: 'Figure B'},
        slug: {current: 'figure-b'},
        tags: ['1/7', 'ABS', 'manga']
      })
    ])
    
    // 模拟迁移脚本
    const products = await client.fetch(`
      *[_type == "product"] {
        _id,
        tags,
        name
      }
    `)
    
    // 验证迁移逻辑
    products.forEach(product => {
      const specifications: any = {}
      
      const scaleTag = product.tags?.find((tag: string) => tag.match(/^1\/\d+$/))
      if (scaleTag) {
        specifications.scale = scaleTag
      }
      
      const materialTags = product.tags?.filter((tag: string) => 
        ['PVC', 'ABS', 'resin', 'metal'].includes(tag)
      )
      if (materialTags?.length > 0) {
        specifications.material = materialTags
      }
      
      // 验证提取结果
      expect(specifications.scale).toBeDefined()
      expect(specifications.material).toBeDefined()
      expect(specifications.material.length).toBeGreaterThan(0)
    })
  })
})
```

### 2. 迁移后验证
```typescript
// scripts/validate-migration.ts
export async function validateMigration() {
  const client = createClient({
    projectId: process.env.SANITY_PROJECT_ID!,
    dataset: process.env.SANITY_DATASET!,
    apiVersion: '2023-01-01',
    token: process.env.SANITY_API_TOKEN!,
    useCdn: false
  })
  
  console.log('开始验证迁移结果...')
  
  // 1. 数据完整性检查
  const integrityChecks = await client.fetch(`
    {
      "orphanedReferences": *[_type == "product" && defined(manufacturer) && !defined(manufacturer->)],
      "missingRequiredFields": *[_type == "product" && (!defined(name.zh) || !defined(slug.current))],
      "duplicateSlugs": *[_type == "product"] | group(slug.current) | [count(.) > 1],
      "invalidPrices": *[_type == "product" && (price < 0 || price > 100000)]
    }
  `)
  
  // 2. 关系一致性检查
  const relationshipChecks = await client.fetch(`
    {
      "productCount": count(*[_type == "product"]),
      "categoryCount": count(*[_type == "category"]),
      "manufacturerCount": count(*[_type == "manufacturer"]),
      "characterCount": count(*[_type == "character"]),
      "productsWithCategories": count(*[_type == "product" && defined(category->)]),
      "productsWithManufacturers": count(*[_type == "product" && defined(manufacturer->)])
    }
  `)
  
  // 3. 查询性能检查
  const performanceStart = Date.now()
  await client.fetch(`
    *[_type == "product" && isPublished == true] | order(_createdAt desc)[0..49] {
      name,
      "slug": slug.current,
      category->{name},
      manufacturer->{name},
      characters[]->{name}
    }
  `)
  const performanceEnd = Date.now()
  
  // 4. 生成验证报告
  const report = {
    timestamp: new Date().toISOString(),
    integrity: integrityChecks,
    relationships: relationshipChecks,
    performance: {
      queryTime: performanceEnd - performanceStart,
      isAcceptable: (performanceEnd - performanceStart) < 5000 // 5秒内
    },
    issues: []
  }
  
  // 检查问题
  if (integrityChecks.orphanedReferences?.length > 0) {
    report.issues.push('发现孤立的引用')
  }
  
  if (integrityChecks.missingRequiredFields?.length > 0) {
    report.issues.push('发现缺少必需字段的文档')
  }
  
  if (integrityChecks.duplicateSlugs?.length > 0) {
    report.issues.push('发现重复的slug')
  }
  
  if (!report.performance.isAcceptable) {
    report.issues.push('查询性能不达标')
  }
  
  console.log('验证报告:', JSON.stringify(report, null, 2))
  
  if (report.issues.length > 0) {
    console.error('❌ 发现问题:', report.issues)
    process.exit(1)
  } else {
    console.log('✅ 迁移验证通过')
  }
}
```

## 最佳实践总结

### 1. 规划阶段
- 详细分析现有数据结构
- 制定详细的迁移计划
- 评估性能影响
- 准备回滚策略

### 2. 实施阶段
- 在测试环境中充分测试
- 使用小批量试运行
- 监控系统性能
- 保持详细的变更记录

### 3. 验证阶段
- 数据完整性检查
- 功能测试
- 性能测试
- 用户验收测试

### 4. 维护阶段
- 监控系统稳定性
- 收集用户反馈
- 必要时进行调整
- 更新文档

通过遵循这些最佳实践，可以确保schema迁移的安全性和成功率，最大化减少对生产系统的影响。