# Sanity Vision URL 查询示例

本文档提供了在 Sanity Vision 中使用的 GROQ 查询示例，帮助你快速测试和调试数据。

## 基础查询

### 1. 获取所有产品（包括未发布的）

```groq
*[_type == "product"] | order(_createdAt desc)
```

### 2. 获取已发布的产品

```groq
*[_type == "product" && isPublished == true] | order(publishedAt desc)
```

### 3. 获取特定产品

```groq
*[_type == "product" && slug.current == "your-product-slug"][0]
```

## 多语言查询

### 查看产品的多语言名称

```groq
*[_type == "product"]{
  "name_zh": name.zh,
  "name_en": name.en,
  "name_ar": name.ar,
  "slug": slug.current,
  isPublished
}
```

### 查看产品的完整多语言内容

```groq
*[_type == "product" && isPublished == true]{
  name,
  shortDescription,
  "slug": slug.current,
  gallery, // mainImage is now gallery[0]
  price,
  currency
}
```

## 关联查询

### 获取产品及其分类信息

```groq
*[_type == "product"]{
  name,
  "slug": slug.current,
  category->{
    name,
    "slug": slug.current,
    description
  },
  price,
  stockStatus
}
```

### 获取产品的完整关联信息

```groq
*[_type == "product" && slug.current == "your-product-slug"][0]{
  ...,
  category->,
  ipSeries->
}
```

## 图片查询（flexibleImage 格式）

### 获取产品图片URL

```groq
*[_type == "product"]{
  name,
  gallery[] {
    imageType,
    imageType == "upload" => {
      "url": uploadedImage.asset->url,
      "alt": uploadedImage.alt
    },
    imageType == "external" => {
      "url": externalUrl,
      "alt": alt
    }
  },
  "gallery": gallery[]{
    imageType,
    imageType == "upload" => {
      "url": uploadedImage.asset->url,
      "alt": uploadedImage.alt
    },
    imageType == "external" => {
      "url": externalUrl,
      "alt": alt
    }
  }
}
```

### 获取产品的详细图片信息

```groq
*[_type == "product" && slug.current == "your-product-slug"][0]{
  gallery[] {
    imageType,
    imageType == "upload" => {
      "url": uploadedImage.asset->url,
      "alt": uploadedImage.alt,
      "hotspot": uploadedImage.hotspot,
      "crop": uploadedImage.crop
    },
    imageType == "external" => {
      "url": externalUrl,
      "alt": alt,
      "fallbackImage": fallbackImage.asset->url
    }
  },
  "gallery": gallery[]{
    imageType,
    imageType == "upload" => {
      "url": uploadedImage.asset->url,
      "alt": uploadedImage.alt,
      "hotspot": uploadedImage.hotspot,
      "crop": uploadedImage.crop
    },
    imageType == "external" => {
      "url": externalUrl,
      "alt": alt,
      "fallbackImage": fallbackImage.asset->url
    }
  }
}
```

## 筛选查询

### 按价格范围筛选

```groq
*[_type == "product" && price >= 100 && price <= 1000 && isPublished == true] | order(price asc)
```

### 按库存状态筛选

```groq
*[_type == "product" && stockStatus == "in-stock" && isPublished == true]
```

### 按分类筛选

```groq
*[_type == "product" && category->slug.current == "your-category-slug" && isPublished == true]
```

### 按标签筛选

```groq
*[_type == "product" && "anime" in tags && isPublished == true]
```

## 搜索查询

### 按名称搜索

```groq
*[_type == "product" && (
  name.zh match "手办*" ||
  name.en match "figure*"
) && isPublished == true]
```

### 按描述搜索

```groq
*[_type == "product" && (
  shortDescription.zh match "限量*" ||
  shortDescription.en match "limited*"
) && isPublished == true]
```

## 统计查询

### 获取产品统计信息

```groq
{
  "total": count(*[_type == "product"]),
  "published": count(*[_type == "product" && isPublished == true]),
  "draft": count(*[_type == "product" && isPublished == false]),
  "inStock": count(*[_type == "product" && stockStatus == "in-stock"]),
  "preOrder": count(*[_type == "product" && stockStatus == "pre-order"]),
  "soldOut": count(*[_type == "product" && stockStatus == "sold-out"])
}
```

### 按分类统计产品

```groq
*[_type == "category"]{
  name,
  "productCount": count(*[_type == "product" && category._ref == ^._id && isPublished == true])
}
```

## 排序查询

### 按发布时间排序

```groq
*[_type == "product" && isPublished == true] | order(publishedAt desc)
```

### 按价格排序

```groq
*[_type == "product" && isPublished == true] | order(price asc)
```

### 按创建时间排序

```groq
*[_type == "product"] | order(_createdAt desc)
```

## 限制结果数量

### 获取最新的5个产品

```groq
*[_type == "product" && isPublished == true] | order(publishedAt desc)[0..4]
```

### 分页查询

```groq
*[_type == "product" && isPublished == true] | order(publishedAt desc)[10..19]
```

## 调试查询

### 查看产品的所有字段

```groq
*[_type == "product"][0]{
  *
}
```

### 查看最近创建的产品

```groq
*[_type == "product"] | order(_createdAt desc)[0..2]{
  name,
  "slug": slug.current,
  isPublished,
  _createdAt
}
```

### 查看产品的内部字段

```groq
*[_type == "product"][0]{
  _id,
  _type,
  _createdAt,
  _updatedAt,
  _rev,
  name,
  slug
}
```

## 测试你的新产品

如果你刚刚手动创建了产品，可以使用以下查询来测试：

### 查看最新创建的产品

```groq
*[_type == "product"] | order(_createdAt desc)[0]
```

### 检查产品是否已发布

```groq
*[_type == "product" && slug.current == "your-product-slug"][0]{
  name,
  isPublished,
  publishedAt,
  stockStatus
}
```

### 查看产品的完整信息

```groq
*[_type == "product" && slug.current == "your-product-slug"][0]{
  ...,
  category->{
    name,
    "slug": slug.current
  },
  ipSeries->{
    name,
    "slug": slug.current
  }
}
```

## 使用参数的查询

在 Vision 中，你可以使用参数来动态查询：

### 设置参数

在 Vision 的 "Params" 标签中添加参数：

```json
{
  "slug": "your-product-slug",
  "category": "your-category-slug",
  "minPrice": 100,
  "maxPrice": 1000,
  "stockStatus": "in-stock",
  "searchTerm": "手办",
  "tag": "anime",
  "limit": 10
}
```

### 使用参数的查询示例

```groq
*[_type == "product" && slug.current == $slug][0]
```

```groq
*[_type == "product" && category->slug.current == $category && isPublished == true]
```

```groq
*[_type == "product" && price >= $minPrice && price <= $maxPrice && isPublished == true]
```

## 注意事项

1. **性能考虑**：避免在大型数据集上使用 `match` 操作符，考虑使用索引
2. **参数使用**：在生产环境中，建议使用参数化查询以提高安全性
3. **字段投影**：只获取需要的字段以提高查询性能
4. **关联查询**：使用 `->` 操作符获取引用的完整对象
5. **错误处理**：在前端代码中添加适当的错误处理机制

## 相关资源

- [GROQ 官方文档](https://www.sanity.io/docs/groq)
- [Sanity Vision 工具](https://www.sanity.io/docs/vision)
- [GROQ 备忘单](https://www.sanity.io/docs/groq-syntax)