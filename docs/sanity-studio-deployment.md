# Sanity Studio 部署指南

## 概述

本指南说明如何部署和配置 Sanity Studio 以支持生产和预发布两个环境。

## 方案一：使用 URL 参数切换（推荐）

### 1. 部署单个 Studio

```bash
cd sanity
sanity deploy --studio-host myngapop
```

部署后访问：
- 生产环境：`https://myngapop.sanity.studio/`
- 预发布环境：`https://myngapop.sanity.studio/?dataset=staging`

### 2. 配置说明

Studio 会根据 URL 参数自动切换数据集：
- 无参数时默认使用 `production` 数据集
- `?dataset=staging` 使用 `staging` 数据集

## 方案二：部署两个独立的 Studio

### 1. 创建两个 Studio 配置

#### 生产环境配置
创建 `sanity.config.production.ts`：

```typescript
import {defineConfig} from 'sanity'
import {deskTool} from 'sanity/desk'
import {visionTool} from '@sanity/vision'
import {schemaTypes} from './schemas'

export default defineConfig({
  name: 'production',
  title: '动漫周边品牌 CMS',
  projectId: '4za4x22i',
  dataset: 'production',
  plugins: [deskTool(), visionTool()],
  schema: {
    types: schemaTypes,
  },
})
```

#### 预发布环境配置
创建 `sanity.config.staging.ts`：

```typescript
import {defineConfig} from 'sanity'
import {deskTool} from 'sanity/desk'
import {visionTool} from '@sanity/vision'
import {schemaTypes} from './schemas'

export default defineConfig({
  name: 'staging',
  title: '动漫周边品牌 CMS (Staging)',
  projectId: '4za4x22i',
  dataset: 'staging',
  plugins: [deskTool(), visionTool()],
  schema: {
    types: schemaTypes,
  },
  // 添加视觉标识
  studio: {
    components: {
      navbar: (props) => {
        return (
          <>
            {props.renderDefault(props)}
            <div style={{
              background: '#fbbf24',
              color: '#000',
              padding: '8px 16px',
              fontWeight: 'bold'
            }}>
              ⚠️ 预发布环境
            </div>
          </>
        )
      }
    }
  }
})
```

### 2. 部署命令

```bash
# 部署生产环境 Studio
sanity deploy --studio-host myngapop-production --config sanity.config.production.ts

# 部署预发布环境 Studio  
sanity deploy --studio-host myngapop-staging --config sanity.config.staging.ts
```

### 3. 访问地址

- 生产环境：`https://myngapop-production.sanity.studio/`
- 预发布环境：`https://myngapop-staging.sanity.studio/`

## 方案三：使用环境变量（适合 CI/CD）

### 1. 修改配置文件

```typescript
// sanity.config.ts
const dataset = process.env.SANITY_STUDIO_DATASET || 'production'
const isStaging = dataset !== 'production'

export default defineConfig({
  name: 'default',
  title: `动漫周边品牌 CMS${isStaging ? ' (Staging)' : ''}`,
  projectId: '4za4x22i',
  dataset,
  // ... 其他配置
})
```

### 2. GitHub Actions 部署示例

```yaml
name: Deploy Sanity Studio

on:
  push:
    branches: [main]
    paths:
      - 'sanity/**'

jobs:
  deploy-production:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - name: Install dependencies
        run: |
          cd sanity
          npm install
      - name: Deploy Production Studio
        env:
          SANITY_AUTH_TOKEN: ${{ secrets.SANITY_AUTH_TOKEN }}
          SANITY_STUDIO_DATASET: production
        run: |
          cd sanity
          npx sanity deploy --studio-host myngapop-prod
          
  deploy-staging:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - name: Install dependencies
        run: |
          cd sanity
          npm install
      - name: Deploy Staging Studio
        env:
          SANITY_AUTH_TOKEN: ${{ secrets.SANITY_AUTH_TOKEN }}
          SANITY_STUDIO_DATASET: staging
        run: |
          cd sanity
          npx sanity deploy --studio-host myngapop-staging
```

## 配置访问权限

### 1. 在 Sanity 管理面板设置

1. 访问 https://www.sanity.io/manage
2. 选择您的项目
3. 进入 "API" → "CORS Origins"
4. 添加 Studio URLs：
   - `https://myngapop.sanity.studio`
   - `https://myngapop-staging.sanity.studio`（如果使用方案二）

### 2. 设置用户权限

在项目设置中为不同用户组设置权限：

```javascript
// 示例：限制某些用户只能访问预发布环境
export default {
  name: 'customAccess',
  title: 'Custom Access Control',
  type: 'document',
  initialValue: async ({currentUser}) => {
    const userEmail = currentUser?.email
    
    // 只允许特定用户访问生产环境
    if (dataset === 'production' && !productionUsers.includes(userEmail)) {
      throw new Error('您没有权限访问生产环境')
    }
    
    return {}
  }
}
```

## 最佳实践

### 1. 明确的环境标识

- 在 Studio 标题中显示环境名称
- 使用不同的颜色主题
- 添加警告横幅

### 2. 权限管理

- 限制生产环境的访问权限
- 使用不同的 API Token
- 记录所有操作日志

### 3. 部署流程

1. 先部署到预发布环境测试
2. 确认无误后部署到生产环境
3. 保持两个环境的 schema 同步

### 4. 监控和告警

- 设置部署失败告警
- 监控 Studio 访问日志
- 定期审查用户权限

## 故障排除

### 常见问题

1. **部署失败：权限不足**
   ```bash
   Error: User does not have deploy permissions
   ```
   解决：确保使用有部署权限的 token

2. **Dataset 不存在**
   ```bash
   Error: Dataset 'staging' not found
   ```
   解决：先创建 dataset
   ```bash
   sanity dataset create staging
   ```

3. **CORS 错误**
   解决：在 Sanity 管理面板添加 Studio URL 到 CORS origins

### 调试命令

```bash
# 检查当前配置
sanity debug

# 验证部署配置
sanity deploy --dry-run

# 查看部署历史
sanity deployments list
```

## 总结

推荐使用方案一（URL 参数切换），因为：
- 维护简单，只需要一个 Studio
- 用户体验好，可以快速切换环境
- 部署成本低

对于需要严格权限控制的场景，可以选择方案二（独立 Studio）。
