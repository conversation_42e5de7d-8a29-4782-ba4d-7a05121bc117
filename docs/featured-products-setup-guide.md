# 首页精选产品配置指南

## 概述

本指南将帮助运营人员在Sanity CMS中配置首页的精选产品展示区域。

## 前置准备

1. 确保Sanity Studio已启动：http://localhost:3333
2. 确保已有至少3个已发布的产品

## 配置步骤

### 方式一：通过Sanity Studio UI创建（推荐）

1. **访问Sanity Studio**
   - 打开浏览器访问：http://localhost:3333
   - 登录你的Sanity账号

2. **找到精选产品管理页面**
   - 在左侧菜单中找到：**🌟 首页精选产品**
   - 点击进入精选产品配置列表页面
   - URL：http://localhost:3333/desk/featured-products

3. **创建新的精选产品配置**
   - 点击右上角的"+ Create"按钮
   - 填写以下信息：

   **基础信息**
   - **模块标题**：
     - 中文：精选产品
     - 英文：Featured Products
     - 阿拉伯文：المنتجات المميزة
   
   - **模块副标题**：
     - 中文：探索我们精心挑选的热门动漫周边产品
     - 英文：Explore our carefully selected popular anime merchandise
     - 阿拉伯文：استكشف منتجاتنا المختارة بعناية

4. **选择要展示的产品**
   - 在"特色产品列表"字段中
   - 点击"Add item"按钮
   - 从产品列表中选择3-6个产品
   - 可以通过拖拽调整产品顺序

5. **配置显示选项**
   - **显示价格**：✅ 开启
   - **显示评分**：✅ 开启
   - **显示分类标签**：✅ 开启
   - **显示商品标识**：✅ 开启（新品、热门、限量等标识）

6. **布局设置**（可选）
   - **桌面端列数**：3列
   - **平板端列数**：2列
   - **手机端列数**：1列

7. **启用模块**
   - **启用特色产品模块**：✅ 必须勾选

8. **保存并发布**
   - 点击右下角的"Publish"按钮
   - 等待发布成功提示

### 方式二：通过API创建（需要开发权限）

如果你有API访问权限，可以：

1. 在`.env`文件中设置`SANITY_API_TOKEN`
2. 运行脚本：`node scripts/create-featured-products.js`

## 验证配置

### 在Sanity Studio中验证
1. 返回精选产品列表页面
2. 确认新创建的配置显示在列表中
3. 状态应该显示为"Published"

### 在网站前端验证
1. 访问网站首页：http://localhost:3000
2. 向下滚动到精选产品区域
3. 应该能看到你配置的：
   - 标题和副标题
   - 选中的3-6个产品
   - 产品的价格、评分、分类等信息

## 更新配置

1. 在Sanity Studio中找到现有的精选产品配置
2. 点击进入编辑页面
3. 修改需要更新的内容：
   - 更换产品：删除现有产品，添加新产品
   - 修改文案：更新标题或副标题
   - 调整显示：开启或关闭某些显示选项
4. 点击"Publish"保存更改

## 常见问题

### Q: 为什么前端没有显示我配置的产品？
A: 请检查：
- 是否勾选了"启用特色产品模块"
- 所选产品是否为"已发布"状态
- 是否点击了"Publish"按钮

### Q: 可以创建多个精选产品配置吗？
A: 可以创建多个，但系统只会使用`isActive=true`的第一个配置

### Q: 如何调整产品显示顺序？
A: 在产品列表中，通过拖拽产品项来调整顺序

### Q: 为什么有些产品选不了？
A: 只有`isPublished=true`的产品才能被选择作为精选产品

### Q: 如何设置产品徽章（新品、热门等）？
A: 在产品编辑页面的"标签"字段中选择或输入标签：
- `new` - 显示"新品"徽章
- `popular` - 显示"热门"徽章
- `limited` - 显示"限定"徽章
- `preorder` - 显示"预售"徽章
- `exclusive` - 显示"独家"徽章
- `sale` - 显示"促销"徽章

## 技术细节

- **数据模型**：`featuredProducts`
- **GROQ查询**：`*[_type == "featuredProducts" && isActive == true][0]`
- **前端组件**：`src/components/home/<USER>
- **Schema定义**：`sanity/schemas/featuredProducts.ts`

## 高级配置

### 批量迁移产品标签

如果你有很多现有产品需要添加标签，可以使用迁移脚本：

```bash
# 查看哪些产品会被更新（干跑模式）
node scripts/migrate-product-tags.js --dry-run

# 执行迁移
node scripts/migrate-product-tags.js
```

迁移脚本会根据以下规则自动添加标签：
- 发布时间在30天内的产品 → `new`
- 库存状态为预售的产品 → `preorder` 和 `limited`
- 同分类下超过5个产品的分类 → `popular`

如需更复杂的配置（如按时间自动切换、A/B测试等），请联系开发团队。