# 高级GROQ查询示例

本文档提供了基于扩展schema的高级GROQ查询示例，展示如何处理复杂的关系查询和数据检索。

## 📋 目录
- [基础关系查询](#基础关系查询)
- [多层级查询](#多层级查询)
- [聚合查询](#聚合查询)
- [搜索查询](#搜索查询)
- [推荐系统查询](#推荐系统查询)
- [统计分析查询](#统计分析查询)
- [性能优化查询](#性能优化查询)

## 基础关系查询

### 1. 产品与角色关系
```groq
// 获取产品的所有关联角色
*[_type == "product" && slug.current == $slug][0] {
  name,
  "slug": slug.current,
  characters[]->{
    name,
    "slug": slug.current,
    characterType,
    popularity,
    series->{
      name,
      "slug": slug.current
    }
  }
}
```

### 2. 角色相关产品
```groq
// 获取某个角色的所有相关产品
*[_type == "character" && slug.current == $characterSlug][0] {
  name,
  characterType,
  popularity,
  series->{
    name,
    "slug": slug.current
  },
  "relatedProducts": *[_type == "product" && references(^._id)] {
    name,
    "slug": slug.current,
    gallery, // mainImage is now gallery[0]
    price,
    currency,
    stockStatus,
    averageRating,
    reviewCount
  }
}
```

### 3. 制造商产品线
```groq
// 获取制造商的所有品牌和产品
*[_type == "manufacturer" && slug.current == $manufacturerSlug][0] {
  name,
  country,
  description,
  logo,
  "brands": *[_type == "brand" && manufacturer._ref == ^._id] {
    name,
    "slug": slug.current,
    logo,
    productLines,
    "productCount": count(*[_type == "product" && brand._ref == ^._id])
  },
  "products": *[_type == "product" && manufacturer._ref == ^._id] {
    name,
    "slug": slug.current,
    gallery, // mainImage is now gallery[0]
    price,
    currency,
    stockStatus,
    brand->{
      name,
      "slug": slug.current
    }
  }
}
```

## 多层级查询

### 1. 分类树结构
```groq
// 获取完整的分类层级结构
*[_type == "category" && !defined(parent)] | order(sortOrder asc) {
  name,
  "slug": slug.current,
  description,
  image,
  level,
  "children": *[_type == "category" && parent._ref == ^._id] | order(sortOrder asc) {
    name,
    "slug": slug.current,
    description,
    image,
    level,
    "children": *[_type == "category" && parent._ref == ^._id] | order(sortOrder asc) {
      name,
      "slug": slug.current,
      description,
      image,
      level,
      "productCount": count(*[_type == "product" && category._ref == ^._id && isPublished == true])
    },
    "productCount": count(*[_type == "product" && category._ref == ^._id && isPublished == true])
  },
  "productCount": count(*[_type == "product" && category._ref == ^._id && isPublished == true])
}
```

### 2. 分类面包屑导航
```groq
// 获取分类的完整路径
*[_type == "category" && slug.current == $categorySlug][0] {
  name,
  "slug": slug.current,
  level,
  "breadcrumbs": [
    parent->{
      name,
      "slug": slug.current,
      level,
      parent->{
        name,
        "slug": slug.current,
        level,
        parent->{
          name,
          "slug": slug.current,
          level
        }
      }
    },
    {
      name,
      "slug": slug.current,
      level
    }
  ]
}
```

### 3. 产品变体层级
```groq
// 获取产品的所有变体信息
*[_type == "product" && slug.current == $slug][0] {
  name,
  "slug": slug.current,
  gallery, // mainImage is now gallery[0]
  price,
  currency,
  specifications,
  "variants": *[_type == "productVariant" && parentProduct._ref == ^._id] {
    name,
    variantType,
    attributes,
    price,
    currency,
    stockQuantity,
    images,
    "priceComparison": {
      "difference": price - ^.price,
      "percentage": ((price - ^.price) / ^.price) * 100
    }
  }
}
```

## 聚合查询

### 1. 分类统计
```groq
// 获取所有分类的产品统计
*[_type == "category"] {
  name,
  "slug": slug.current,
  "stats": {
    "totalProducts": count(*[_type == "product" && category._ref == ^._id]),
    "publishedProducts": count(*[_type == "product" && category._ref == ^._id && isPublished == true]),
    "inStockProducts": count(*[_type == "product" && category._ref == ^._id && stockStatus == "in-stock"]),
    "averagePrice": math::avg(*[_type == "product" && category._ref == ^._id && defined(price)].price),
    "priceRange": {
      "min": math::min(*[_type == "product" && category._ref == ^._id && defined(price)].price),
      "max": math::max(*[_type == "product" && category._ref == ^._id && defined(price)].price)
    },
    "topRatedProducts": *[_type == "product" && category._ref == ^._id && isPublished == true] | order(averageRating desc, reviewCount desc)[0..2] {
      name,
      "slug": slug.current,
      averageRating,
      reviewCount
    }
  }
}
```

### 2. 制造商业绩统计
```groq
// 获取制造商的综合统计
*[_type == "manufacturer"] {
  name,
  "slug": slug.current,
  country,
  "performance": {
    "totalProducts": count(*[_type == "product" && manufacturer._ref == ^._id]),
    "totalBrands": count(*[_type == "brand" && manufacturer._ref == ^._id]),
    "averageRating": math::avg(*[_type == "product" && manufacturer._ref == ^._id && defined(averageRating)].averageRating),
    "totalReviews": math::sum(*[_type == "product" && manufacturer._ref == ^._id && defined(reviewCount)].reviewCount),
    "categoryDistribution": *[_type == "category"] {
      name,
      "productCount": count(*[_type == "product" && manufacturer._ref == ^._id && category._ref == ^._id])
    }[productCount > 0],
    "priceDistribution": {
      "under100": count(*[_type == "product" && manufacturer._ref == ^._id && price < 100]),
      "100to500": count(*[_type == "product" && manufacturer._ref == ^._id && price >= 100 && price < 500]),
      "500to1000": count(*[_type == "product" && manufacturer._ref == ^._id && price >= 500 && price < 1000]),
      "over1000": count(*[_type == "product" && manufacturer._ref == ^._id && price >= 1000])
    }
  }
}
```

### 3. 用户评价统计
```groq
// 获取产品的评价统计详情
*[_type == "product" && slug.current == $slug][0] {
  name,
  "slug": slug.current,
  averageRating,
  reviewCount,
  "reviewStats": {
    "ratingDistribution": {
      "5star": count(*[_type == "review" && product._ref == ^._id && rating == 5 && isApproved == true]),
      "4star": count(*[_type == "review" && product._ref == ^._id && rating == 4 && isApproved == true]),
      "3star": count(*[_type == "review" && product._ref == ^._id && rating == 3 && isApproved == true]),
      "2star": count(*[_type == "review" && product._ref == ^._id && rating == 2 && isApproved == true]),
      "1star": count(*[_type == "review" && product._ref == ^._id && rating == 1 && isApproved == true])
    },
    "verificationStats": {
      "verified": count(*[_type == "review" && product._ref == ^._id && isVerified == true]),
      "purchaseVerified": count(*[_type == "review" && product._ref == ^._id && purchaseVerified == true]),
      "withImages": count(*[_type == "review" && product._ref == ^._id && count(images) > 0])
    },
    "mostHelpfulReviews": *[_type == "review" && product._ref == ^._id && isApproved == true] | order(helpful desc, _createdAt desc)[0..2] {
      customerName,
      rating,
      title,
      content,
      helpful,
      _createdAt
    }
  }
}
```

## 搜索查询

### 1. 全文搜索
```groq
// 多字段全文搜索
*[_type == "product" && (
  // 产品名称
  name.zh match $searchTerm + "*" ||
  name.en match $searchTerm + "*" ||
  // 产品描述
  shortDescription.zh match $searchTerm + "*" ||
  shortDescription.en match $searchTerm + "*" ||
  // 分类名称
  category->name.zh match $searchTerm + "*" ||
  category->name.en match $searchTerm + "*" ||
  // 角色名称
  characters[]->name.zh match $searchTerm + "*" ||
  characters[]->name.en match $searchTerm + "*" ||
  // 制造商名称
  manufacturer->name.zh match $searchTerm + "*" ||
  manufacturer->name.en match $searchTerm + "*" ||
  // 品牌名称
  brand->name.zh match $searchTerm + "*" ||
  brand->name.en match $searchTerm + "*" ||
  // 标签
  $searchTerm in tags
) && isPublished == true] | order(
  // 相关性排序
  select(
    name.zh match $searchTerm + "*" => 10,
    name.en match $searchTerm + "*" => 9,
    characters[]->name.zh match $searchTerm + "*" => 8,
    category->name.zh match $searchTerm + "*" => 7,
    shortDescription.zh match $searchTerm + "*" => 6,
    $searchTerm in tags => 5,
    true => 0
  ) desc,
  averageRating desc,
  reviewCount desc
) {
  name,
  "slug": slug.current,
  shortDescription,
  gallery, // mainImage is now gallery[0]
  price,
  currency,
  stockStatus,
  averageRating,
  reviewCount,
  category->{
    name,
    "slug": slug.current
  },
  "highlightedFields": {
    "nameMatch": name.zh match $searchTerm + "*" || name.en match $searchTerm + "*",
    "categoryMatch": category->name.zh match $searchTerm + "*" || category->name.en match $searchTerm + "*",
    "characterMatch": characters[]->name.zh match $searchTerm + "*" || characters[]->name.en match $searchTerm + "*",
    "tagMatch": $searchTerm in tags
  }
}
```

### 2. 高级筛选搜索
```groq
// 多条件筛选搜索
*[_type == "product" && 
  // 基础条件
  isPublished == true &&
  // 分类筛选
  (!defined($categorySlug) || category->slug.current == $categorySlug) &&
  // 价格范围
  (!defined($minPrice) || price >= $minPrice) &&
  (!defined($maxPrice) || price <= $maxPrice) &&
  // 库存状态
  (!defined($stockStatus) || stockStatus == $stockStatus) &&
  // 制造商筛选
  (!defined($manufacturerSlug) || manufacturer->slug.current == $manufacturerSlug) &&
  // 品牌筛选
  (!defined($brandSlug) || brand->slug.current == $brandSlug) &&
  // 评分筛选
  (!defined($minRating) || averageRating >= $minRating) &&
  // 角色筛选
  (!defined($characterSlug) || characters[]->slug.current match $characterSlug) &&
  // 标签筛选
  (!defined($tags) || count(tags[@ in $tags]) > 0) &&
  // 比例筛选
  (!defined($scale) || specifications.scale == $scale) &&
  // 材质筛选
  (!defined($materials) || count(specifications.material[@ in $materials]) > 0)
] | order(
  select(
    $sortBy == "price-asc" => price asc,
    $sortBy == "price-desc" => price desc,
    $sortBy == "rating-desc" => averageRating desc,
    $sortBy == "reviews-desc" => reviewCount desc,
    $sortBy == "newest" => _createdAt desc,
    $sortBy == "oldest" => _createdAt asc,
    true => averageRating desc
  )
)[($page - 1) * $pageSize..($page * $pageSize - 1)] {
  name,
  "slug": slug.current,
  shortDescription,
  gallery, // mainImage is now gallery[0]
  price,
  currency,
  stockStatus,
  averageRating,
  reviewCount,
  specifications,
  category->{
    name,
    "slug": slug.current
  },
  manufacturer->{
    name,
    "slug": slug.current
  },
  brand->{
    name,
    "slug": slug.current
  },
  characters[]->{
    name,
    "slug": slug.current,
    characterType
  }
}
```

### 3. 搜索建议
```groq
// 搜索自动完成建议
{
  "products": *[_type == "product" && (
    name.zh match $searchTerm + "*" ||
    name.en match $searchTerm + "*"
  ) && isPublished == true] | order(averageRating desc)[0..4] {
    name,
    "slug": slug.current,
    gallery, // mainImage is now gallery[0]
    "type": "product"
  },
  "characters": *[_type == "character" && (
    name.zh match $searchTerm + "*" ||
    name.en match $searchTerm + "*"
  ) && isActive == true] | order(popularity desc)[0..4] {
    name,
    "slug": slug.current,
    "image": images[0],
    "type": "character"
  },
  "categories": *[_type == "category" && (
    name.zh match $searchTerm + "*" ||
    name.en match $searchTerm + "*"
  ) && isActive == true] | order(sortOrder asc)[0..4] {
    name,
    "slug": slug.current,
    image,
    "type": "category"
  },
  "manufacturers": *[_type == "manufacturer" && (
    name.zh match $searchTerm + "*" ||
    name.en match $searchTerm + "*"
  ) && isActive == true][0..2] {
    name,
    "slug": slug.current,
    logo,
    "type": "manufacturer"
  }
}
```

## 推荐系统查询

### 1. 基于产品的推荐
```groq
// 获取当前产品的推荐列表
*[_type == "product" && slug.current == $slug][0] {
  "currentProduct": {
    name,
    category,
    characters,
    manufacturer,
    brand,
    tags,
    price,
    specifications
  },
  "recommendations": {
    // 同分类推荐
    "sameCategory": *[_type == "product" && 
      category._ref == ^.category._ref && 
      _id != ^._id && 
      isPublished == true
    ] | order(averageRating desc, reviewCount desc)[0..3] {
      name,
      "slug": slug.current,
      gallery, // mainImage is now gallery[0]
      price,
      currency,
      averageRating,
      reviewCount
    },
    
    // 同角色推荐
    "sameCharacter": *[_type == "product" && 
      count(characters[@._ref in ^.characters[]._ref]) > 0 && 
      _id != ^._id && 
      isPublished == true
    ] | order(averageRating desc, reviewCount desc)[0..3] {
      name,
      "slug": slug.current,
      gallery, // mainImage is now gallery[0]
      price,
      currency,
      averageRating,
      "sharedCharacters": characters[_ref in ^.characters[]._ref]->{
        name,
        "slug": slug.current
      }
    },
    
    // 同制造商推荐
    "sameManufacturer": *[_type == "product" && 
      manufacturer._ref == ^.manufacturer._ref && 
      _id != ^._id && 
      isPublished == true
    ] | order(averageRating desc, reviewCount desc)[0..3] {
      name,
      "slug": slug.current,
      gallery, // mainImage is now gallery[0]
      price,
      currency,
      averageRating,
      reviewCount
    },
    
    // 价格区间推荐
    "similarPrice": *[_type == "product" && 
      price >= (^.price * 0.8) && 
      price <= (^.price * 1.2) && 
      _id != ^._id && 
      isPublished == true
    ] | order(averageRating desc, reviewCount desc)[0..3] {
      name,
      "slug": slug.current,
      gallery, // mainImage is now gallery[0]
      price,
      currency,
      averageRating,
      reviewCount
    },
    
    // 标签相似推荐
    "similarTags": *[_type == "product" && 
      count(tags[@ in ^.tags]) > 0 && 
      _id != ^._id && 
      isPublished == true
    ] | order(
      count(tags[@ in ^.tags]) desc,
      averageRating desc
    )[0..3] {
      name,
      "slug": slug.current,
      gallery, // mainImage is now gallery[0]
      price,
      currency,
      averageRating,
      "sharedTags": tags[@ in ^.tags],
      "tagScore": count(tags[@ in ^.tags])
    }
  }
}
```

### 2. 基于用户行为的推荐
```groq
// 基于浏览历史的推荐（需要客户端传入浏览历史）
{
  "viewedProducts": *[_type == "product" && _id in $viewedIds] {
    _id,
    category,
    characters,
    manufacturer,
    brand,
    tags,
    price
  },
  "recommendations": *[_type == "product" && 
    _id != $currentId && 
    !(_id in $viewedIds) && 
    isPublished == true && 
    (
      // 基于分类相似性
      category._ref in ^.viewedProducts[].category._ref ||
      // 基于角色相似性
      count(characters[@._ref in ^.viewedProducts[].characters[]._ref]) > 0 ||
      // 基于制造商相似性
      manufacturer._ref in ^.viewedProducts[].manufacturer._ref ||
      // 基于标签相似性
      count(tags[@ in ^.viewedProducts[].tags[]]) > 0
    )
  ] | order(
    // 计算相似度得分
    (
      // 分类匹配得分
      select(category._ref in ^.viewedProducts[].category._ref => 3, true => 0) +
      // 角色匹配得分
      count(characters[@._ref in ^.viewedProducts[].characters[]._ref]) * 2 +
      // 制造商匹配得分
      select(manufacturer._ref in ^.viewedProducts[].manufacturer._ref => 2, true => 0) +
      // 标签匹配得分
      count(tags[@ in ^.viewedProducts[].tags[]]) * 1
    ) desc,
    averageRating desc,
    reviewCount desc
  )[0..9] {
    name,
    "slug": slug.current,
    gallery, // mainImage is now gallery[0]
    price,
    currency,
    averageRating,
    reviewCount,
    "matchScore": (
      select(category._ref in ^.viewedProducts[].category._ref => 3, true => 0) +
      count(characters[@._ref in ^.viewedProducts[].characters[]._ref]) * 2 +
      select(manufacturer._ref in ^.viewedProducts[].manufacturer._ref => 2, true => 0) +
      count(tags[@ in ^.viewedProducts[].tags[]]) * 1
    )
  }
}
```

## 统计分析查询

### 1. 销售分析
```groq
// 产品销售统计分析
{
  "overview": {
    "totalProducts": count(*[_type == "product"]),
    "publishedProducts": count(*[_type == "product" && isPublished == true]),
    "averagePrice": math::avg(*[_type == "product" && defined(price) && isPublished == true].price),
    "averageRating": math::avg(*[_type == "product" && defined(averageRating) && isPublished == true].averageRating),
    "totalReviews": math::sum(*[_type == "product" && defined(reviewCount) && isPublished == true].reviewCount)
  },
  "categoryAnalysis": *[_type == "category"] {
    name,
    "productCount": count(*[_type == "product" && category._ref == ^._id && isPublished == true]),
    "averagePrice": math::avg(*[_type == "product" && category._ref == ^._id && defined(price) && isPublished == true].price),
    "averageRating": math::avg(*[_type == "product" && category._ref == ^._id && defined(averageRating) && isPublished == true].averageRating),
    "stockStatus": {
      "inStock": count(*[_type == "product" && category._ref == ^._id && stockStatus == "in-stock"]),
      "preOrder": count(*[_type == "product" && category._ref == ^._id && stockStatus == "pre-order"]),
      "soldOut": count(*[_type == "product" && category._ref == ^._id && stockStatus == "sold-out"])
    }
  }[productCount > 0] | order(productCount desc),
  "priceDistribution": {
    "under100": count(*[_type == "product" && price < 100 && isPublished == true]),
    "100to300": count(*[_type == "product" && price >= 100 && price < 300 && isPublished == true]),
    "300to500": count(*[_type == "product" && price >= 300 && price < 500 && isPublished == true]),
    "500to1000": count(*[_type == "product" && price >= 500 && price < 1000 && isPublished == true]),
    "over1000": count(*[_type == "product" && price >= 1000 && isPublished == true])
  },
  "topPerformers": {
    "highestRated": *[_type == "product" && isPublished == true] | order(averageRating desc, reviewCount desc)[0..4] {
      name,
      "slug": slug.current,
      averageRating,
      reviewCount,
      price
    },
    "mostReviewed": *[_type == "product" && isPublished == true] | order(reviewCount desc, averageRating desc)[0..4] {
      name,
      "slug": slug.current,
      averageRating,
      reviewCount,
      price
    }
  }
}
```

### 2. 内容质量分析
```groq
// 内容质量和完整性分析
{
  "contentQuality": {
    "productsWithDescription": count(*[_type == "product" && defined(description.zh) && description.zh != ""]),
    "productsWithImages": count(*[_type == "product" && defined(gallery) && count(gallery) > 0]),
    "productsWithSEO": count(*[_type == "product" && defined(seo.title.zh) && seo.title.zh != ""]),
    "productsWithCharacters": count(*[_type == "product" && count(characters) > 0]),
    "productsWithSpecs": count(*[_type == "product" && defined(specifications.height)]),
    "productsWithReviews": count(*[_type == "product" && reviewCount > 0])
  },
  "missingContent": {
    "noDescription": *[_type == "product" && (!defined(description.zh) || description.zh == "")] {
      name,
      "slug": slug.current,
      _createdAt
    }[0..4],
    "noImages": *[_type == "product" && (!defined(gallery) || count(gallery) == 0)] {
      name,
      "slug": slug.current,
      _createdAt
    }[0..4],
    "noSEO": *[_type == "product" && (!defined(seo.title.zh) || seo.title.zh == "")] {
      name,
      "slug": slug.current,
      _createdAt
    }[0..4]
  },
  "languageCompleteness": {
    "chineseComplete": count(*[_type == "product" && defined(name.zh) && name.zh != ""]),
    "englishComplete": count(*[_type == "product" && defined(name.en) && name.en != ""]),
    "arabicComplete": count(*[_type == "product" && defined(name.ar) && name.ar != ""])
  }
}
```

## 性能优化查询

### 1. 分页查询优化
```groq
// 高效的分页查询
*[_type == "product" && isPublished == true] | order(_createdAt desc)[($page - 1) * $pageSize..$page * $pageSize - 1] {
  // 只获取必要的字段
  _id,
  name,
  "slug": slug.current,
  gallery, // mainImage is now gallery[0]
  price,
  currency,
  stockStatus,
  averageRating,
  reviewCount,
  // 最小化关联查询
  "categoryName": category->name.zh,
  "categorySlug": category->slug.current,
  "manufacturerName": manufacturer->name.zh
}
```

### 2. 缓存友好查询
```groq
// 设计用于缓存的查询
{
  "metadata": {
    "lastUpdated": *[_type == "product"] | order(_updatedAt desc)[0]._updatedAt,
    "totalCount": count(*[_type == "product" && isPublished == true]),
    "cacheKey": "products-" + string(now())
  },
  "data": *[_type == "product" && isPublished == true] | order(_createdAt desc) {
    _id,
    _updatedAt,
    name,
    "slug": slug.current,
    gallery, // mainImage is now gallery[0]
    price,
    currency,
    stockStatus,
    averageRating,
    reviewCount,
    category->{
      _id,
      name,
      "slug": slug.current
    }
  }
}
```

### 3. 索引优化查询
```groq
// 利用索引的查询模式
{
  // 使用单字段索引
  "byCategory": *[_type == "product" && category._ref == $categoryId && isPublished == true] | order(_createdAt desc),
  
  // 使用复合索引
  "byCategoryAndStatus": *[_type == "product" && category._ref == $categoryId && stockStatus == $stockStatus && isPublished == true] | order(_createdAt desc),
  
  // 避免全表扫描
  "recentProducts": *[_type == "product" && _createdAt >= $sinceDate && isPublished == true] | order(_createdAt desc)
}
```

## 实用工具查询

### 1. 数据验证查询
```groq
// 数据完整性检查
{
  "orphanedVariants": *[_type == "productVariant" && !defined(parentProduct->)],
  "invalidReferences": *[_type == "product" && (!defined(category->) || !defined(manufacturer->))],
  "duplicateSlugs": *[_type == "product"] | group(slug.current) | [count(.) > 1] {
    "slug": key,
    "count": count(.),
    "products": [].{name, _id}
  },
  "priceInconsistencies": *[_type == "product" && price < 0 || price > 10000],
  "missingImages": *[_type == "product" && (!defined(gallery) || count(gallery) == 0)],
  "unpublishedOldProducts": *[_type == "product" && !isPublished && _createdAt < (now() - 60*60*24*30)] {
    name,
    "slug": slug.current,
    _createdAt
  }
}
```

### 2. 管理员仪表板查询
```groq
// 管理员概览仪表板
{
  "dailyStats": {
    "today": {
      "newProducts": count(*[_type == "product" && _createdAt >= $todayStart]),
      "newReviews": count(*[_type == "review" && _createdAt >= $todayStart]),
      "publishedProducts": count(*[_type == "product" && publishedAt >= $todayStart])
    },
    "thisWeek": {
      "newProducts": count(*[_type == "product" && _createdAt >= $weekStart]),
      "newReviews": count(*[_type == "review" && _createdAt >= $weekStart]),
      "publishedProducts": count(*[_type == "product" && publishedAt >= $weekStart])
    }
  },
  "pendingActions": {
    "draftProducts": count(*[_type == "product" && !isPublished]),
    "pendingReviews": count(*[_type == "review" && !isApproved]),
    "lowStockProducts": count(*[_type == "product" && stockStatus == "sold-out"]),
    "missingContent": count(*[_type == "product" && (!defined(description.zh) || !defined(gallery) || count(gallery) == 0)])
  },
  "recentActivity": {
    "recentProducts": *[_type == "product"] | order(_createdAt desc)[0..4] {
      name,
      "slug": slug.current,
      isPublished,
      _createdAt
    },
    "recentReviews": *[_type == "review"] | order(_createdAt desc)[0..4] {
      customerName,
      rating,
      product->{
        name,
        "slug": slug.current
      },
      _createdAt
    }
  }
}
```

这些高级查询示例展示了GROQ的强大功能，可以处理复杂的业务需求和数据分析任务。在实际使用中，请根据你的具体需求调整查询，并注意性能优化。