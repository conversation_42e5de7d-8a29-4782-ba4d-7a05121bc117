# MyNgaPop CMS 运营操作指南

## 概述

本指南将帮助您理解如何使用 MyNgaPop 的内容管理系统（CMS）进行内容发布。我们采用了"预发布-生产"两级发布流程，确保内容质量和网站稳定性。

## 重要概念

### 什么是数据集？
- **生产数据集（Production）**：正式网站显示的内容，所有访客都能看到
- **预发布数据集（Staging）**：测试环境的内容，仅内部人员可见

### 为什么需要两个环境？
1. **安全性**：避免错误内容直接发布到正式网站
2. **预览功能**：可以提前查看内容效果
3. **团队协作**：多人可以同时在预发布环境工作，不影响正式网站

---

## 一、访问 CMS 后台

### 1.1 访问地址

- **生产环境 CMS**：`https://myngapop.sanity.studio/`
- **预发布环境 CMS**：`https://myngapop.sanity.studio/?dataset=staging`

> 💡 提示：请将以上地址加入浏览器书签，方便日常使用

### 1.2 如何判断当前环境

登录后台后，请注意以下标识：

1. **页面标题**
   - 生产环境：`动漫周边品牌 CMS`
   - 预发布环境：`动漫周边品牌 CMS (Staging)`

2. **环境提示**
   - 预发布环境会在左侧菜单显示黄色提示框
   - 提示内容：`📍 当前数据集: staging`

---

## 二、内容发布流程

### 2.1 标准发布流程

```mermaid
graph LR
    A[在预发布环境编辑内容] --> B[预览确认]
    B --> C[推送到生产环境]
    C --> D[生产网站更新]
```

### 2.2 详细操作步骤

#### 第一步：在预发布环境编辑内容

1. 访问预发布环境：`https://myngapop.sanity.studio/?dataset=staging`
2. 登录您的账号
3. 进行内容编辑：
   - 添加新产品
   - 修改页面内容
   - 上传图片
   - 更新文案
4. 点击"发布"按钮保存更改

#### 第二步：预览内容

1. 访问预发布网站：`https://staging.myngapop.com`
2. 检查您刚才的更改是否正确显示
3. 测试所有功能是否正常
4. 如需修改，返回第一步

#### 第三步：推送到生产环境

确认内容无误后，有两种方式推送到生产环境：

**方式一：自助推送（推荐）**

1. 打开推送工具页面：`https://myngapop.com/admin/promote`
2. 点击"预览更改"查看将要推送的内容
3. 确认无误后，点击"推送到生产"
4. 等待推送完成（通常需要 1-2 分钟）

**方式二：联系技术人员**

如果自助推送遇到问题，请联系技术支持协助推送。

---

## 三、内容备份操作

### 3.1 为什么要备份？

- 防止误操作导致内容丢失
- 可以恢复到之前的版本
- 符合数据安全规范

### 3.2 手动备份步骤

1. **访问备份工具**
   - 打开：`https://myngapop.com/admin/backup`
   
2. **创建备份**
   - 选择要备份的环境（生产/预发布）
   - 点击"创建备份"
   - 系统会自动生成带时间戳的备份文件

3. **下载备份**
   - 备份完成后，点击"下载"按钮
   - 将文件保存到安全位置
   - 建议文件名格式：`backup-2024-01-15-production.tar.gz`

### 3.3 自动备份

系统每天凌晨 2:00 会自动备份生产环境数据，保留最近 7 天的备份。

---

## 四、常见操作场景

### 场景 1：发布新产品

```
1. 登录预发布环境 CMS
2. 点击"产品管理" → "产品" → "创建新产品"
3. 填写产品信息（名称、描述、价格、图片等）
4. 点击"发布"保存
5. 在预发布网站查看效果
6. 确认无误后，使用推送工具发布到生产
```

### 场景 2：修改首页内容

```
1. 登录预发布环境 CMS
2. 点击"页面管理" → "首页"
3. 修改需要更新的内容
4. 点击"发布"保存
5. 预览确认
6. 推送到生产环境
```

### 场景 3：批量更新产品信息

```
1. 先在预发布环境完成所有更新
2. 逐个检查修改的产品
3. 使用推送工具一次性发布所有更改
```

---

## 五、注意事项

### 5.1 操作规范

1. **永远先在预发布环境操作**
   - 不要直接在生产环境修改内容
   - 紧急情况除外（需要主管批准）

2. **发布前必须预览**
   - 检查文字是否有错别字
   - 确认图片显示正常
   - 测试链接是否有效

3. **重要更新前先备份**
   - 大批量更新前创建备份
   - 删除内容前创建备份

### 5.2 权限说明

- **编辑权限**：可以创建和修改内容
- **发布权限**：可以将内容发布到预发布环境
- **推送权限**：可以将内容从预发布推送到生产

### 5.3 紧急情况处理

如果发现生产环境有严重问题：

1. 立即联系技术支持
2. 记录问题详情（截图、错误信息）
3. 等待技术人员处理，不要自行尝试修复

---

## 六、常见问题解答

### Q1：我不小心在生产环境修改了内容怎么办？

A：不用担心，按以下步骤处理：
1. 记录您修改的内容
2. 在预发布环境重新进行相同的修改
3. 通过正常流程推送到生产
4. 这样可以保持两个环境的一致性

### Q2：推送失败了怎么办？

A：请按以下步骤排查：
1. 检查网络连接是否正常
2. 确认您有推送权限
3. 查看错误提示信息
4. 如果问题持续，联系技术支持

### Q3：如何知道上次推送的时间？

A：可以通过以下方式查看：
1. 在推送工具页面查看"最后推送时间"
2. 查看推送历史记录

### Q4：可以只推送部分内容吗？

A：是的，推送工具支持：
1. 选择特定类型的内容（如只推送产品）
2. 查看详细的更改列表
3. 但建议完整推送以保持一致性

### Q5：备份文件应该保存多久？

A：建议：
- 日常备份保留 7 天
- 重要版本备份保留 30 天
- 大版本更新前的备份永久保留

---

## 七、快速参考

### 常用地址

| 用途 | 地址 | 说明 |
|------|------|------|
| 生产环境 CMS | https://myngapop.sanity.studio/ | 正式内容管理 |
| 预发布环境 CMS | https://myngapop.sanity.studio/?dataset=staging | 测试内容管理 |
| 生产网站 | https://myngapop.com | 正式网站 |
| 预发布网站 | https://staging.myngapop.com | 测试网站 |
| 推送工具 | https://myngapop.com/admin/promote | 内容发布 |
| 备份工具 | https://myngapop.com/admin/backup | 数据备份 |

### 操作检查清单

发布前检查：
- [ ] 内容无错别字
- [ ] 图片正常显示
- [ ] 链接可以点击
- [ ] 在预发布环境测试通过
- [ ] 已创建备份（重要更新）

### 联系方式

- 技术支持邮箱：<EMAIL>
- 紧急联系电话：xxx-xxxx-xxxx
- 工作时间：周一至周五 9:00-18:00

---

## 八、视频教程

为了帮助您更好地理解操作流程，我们准备了以下视频教程：

1. [基础操作：如何登录和识别环境](https://xxx)
2. [内容编辑：创建和修改产品](https://xxx)
3. [发布流程：从预发布到生产](https://xxx)
4. [备份操作：如何创建和管理备份](https://xxx)

---

*本文档最后更新时间：2024年1月*

*如有任何疑问，请随时联系技术支持团队*