# 跨境动漫周边品牌静态官网开发计划

> 本文档面向开发者/AGI，详细说明技术架构、开发步骤和实施细节。

## 项目概述

### 技术栈

- **前端框架**: Next.js 14+ (App Router)
- **样式方案**: Tailwind CSS + CSS Modules
- **动画库**: Framer Motion
- **CMS**: Sanity v3
- **部署平台**: Vercel
- **多语言**: next-intl
- **状态管理**: Zustand (轻量级需求)
- **图片优化**: Next.js Image + Vercel Image Optimization

### 架构特点

- 静态生成 + ISR (增量静态再生)
- 多语言支持（中文为默认）
- Headless CMS 内容管理
- 全球 CDN 分发
- 移动端优先响应式设计

## 项目初始化

### 1. 环境准备

```bash
# 确保 Node.js >= 18.17.0
node --version

# 安装 pnpm (推荐) 或使用 npm/yarn
npm install -g pnpm

# 安装 Sanity CLI
npm install -g @sanity/cli
```

### 2. 创建 Next.js 项目

```bash
# 创建项目 (使用 App Router)
pnpm create next-app@latest anime-brand-site --typescript --tailwind --app --src-dir

# 进入项目目录
cd anime-brand-site

# 安装核心依赖
pnpm add @sanity/client @sanity/image-url sanity@latest
pnpm add framer-motion next-intl sharp
pnpm add -D @types/node
```

### 3. 项目结构规划

```
anime-brand-site/
├── src/
│   ├── app/                    # App Router 目录
│   │   ├── [locale]/          # 多语言动态路由
│   │   │   ├── layout.tsx     # 多语言布局
│   │   │   ├── page.tsx       # 首页
│   │   │   ├── products/      # 产品相关页面
│   │   │   │   ├── page.tsx   # 产品列表
│   │   │   │   └── [slug]/    # 产品详情
│   │   │   └── (cms)/         # CMS 预览路由组
│   │   ├── api/               # API 路由
│   │   │   ├── revalidate/    # ISR 重验证端点
│   │   │   └── preview/       # 预览模式端点
│   │   └── globals.css        # 全局样式
│   ├── components/            # 组件目录
│   │   ├── ui/               # 基础 UI 组件
│   │   ├── product/          # 产品相关组件
│   │   └── layout/           # 布局组件
│   ├── lib/                  # 工具库
│   │   ├── sanity/          # Sanity 相关
│   │   │   ├── client.ts    # Sanity 客户端
│   │   │   ├── queries.ts   # GROQ 查询
│   │   │   └── schemas/     # 内容模型
│   │   └── utils/           # 工具函数
│   ├── hooks/               # 自定义 Hooks
│   ├── types/               # TypeScript 类型
│   └── messages/           # 多语言翻译文件
│       ├── zh.json         # 中文
│       ├── en.json         # 英文
│       └── ar.json         # 阿拉伯语
├── sanity/                 # Sanity Studio 项目
├── public/                 # 静态资源
└── ...配置文件
```

## Sanity CMS 配置

### 1. 初始化 Sanity 项目

```bash
# 在项目根目录创建 sanity 文件夹
mkdir sanity && cd sanity

# 初始化 Sanity 项目
sanity init --bare

# 选择配置
# - Project name: anime-brand-cms
# - Use TypeScript: Yes
# - Package manager: pnpm
```

### 2. 定义内容模型 (Schema)

```typescript
// sanity/schemas/product.ts
import { defineType, defineField } from 'sanity'

export default defineType({
  name: 'product',
  title: '产品',
  type: 'document',
  fields: [
    defineField({
      name: 'name',
      title: '产品名称',
      type: 'localeString', // 多语言字符串
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'slug',
      title: 'URL 标识',
      type: 'slug',
      options: {
        source: 'name.zh',
        maxLength: 96,
      },
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'shortDescription',
      title: '简短描述',
      type: 'localeText',
    }),
    defineField({
      name: 'description',
      title: '详细描述',
      type: 'localeBlockContent', // 多语言富文本
    }),
    // mainImage has been migrated to gallery[0]
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'gallery',
      title: '产品图册',
      type: 'array',
      of: [{ type: 'image', options: { hotspot: true } }]
    }),
    defineField({
      name: 'category',
      title: '产品分类',
      type: 'reference',
      to: [{ type: 'category' }],
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'ipSeries',
      title: 'IP 系列',
      type: 'reference',
      to: [{ type: 'ipSeries' }]
    }),
    defineField({
      name: 'tags',
      title: '标签',
      type: 'array',
      of: [{ type: 'string' }],
      options: {
        layout: 'tags'
      }
    }),
    defineField({
      name: 'price',
      title: '展示价格',
      type: 'number',
    }),
    defineField({
      name: 'currency',
      title: '货币单位',
      type: 'string',
      options: {
        list: [
          { title: '人民币', value: 'CNY' },
          { title: '美元', value: 'USD' },
          { title: '阿联酋迪拉姆', value: 'AED' }
        ]
      },
      initialValue: 'CNY'
    }),
    defineField({
      name: 'stockStatus',
      title: '库存状态',
      type: 'string',
      options: {
        list: [
          { title: '现货', value: 'in-stock' },
          { title: '预售', value: 'pre-order' },
          { title: '售罄', value: 'sold-out' }
        ]
      },
      initialValue: 'in-stock'
    }),
    defineField({
      name: 'isPublished',
      title: '上架状态',
      type: 'boolean',
      initialValue: false
    }),
    defineField({
      name: 'publishedAt',
      title: '发布时间',
      type: 'datetime',
    }),
    defineField({
      name: 'seo',
      title: 'SEO 设置',
      type: 'seo', // 自定义 SEO 对象类型
    })
  ],
  preview: {
    select: {
      title: 'name.zh',
      subtitle: 'category.name.zh',
      media: 'gallery', // Use first image from gallery
      published: 'isPublished'
    },
    prepare({ title, subtitle, media, published }) {
      return {
        title: title || '未命名产品',
        subtitle: `${subtitle || '未分类'} ${published ? '✅' : '📝'}`,
        media
      }
    }
  }
})
```

```typescript
// sanity/schemas/localeString.ts - 多语言字符串类型
import { defineType } from 'sanity'

export default defineType({
  name: 'localeString',
  title: '多语言文本',
  type: 'object',
  fields: [
    { name: 'zh', title: '中文', type: 'string' },
    { name: 'en', title: 'English', type: 'string' },
    { name: 'ar', title: 'العربية', type: 'string' }
  ]
})
```

### 3. 配置 Sanity Studio

```typescript
// sanity/sanity.config.ts
import { defineConfig } from 'sanity'
import { deskTool } from 'sanity/desk'
import { visionTool } from '@sanity/vision'
import { media } from 'sanity-plugin-media'
import schemas from './schemas'

export default defineConfig({
  name: 'default',
  title: '动漫周边品牌 CMS',
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID!,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET!,
  
  plugins: [
    deskTool({
      structure: (S) =>
        S.list()
          .title('内容管理')
          .items([
            S.listItem()
              .title('产品')
              .icon(PackageIcon)
              .child(
                S.documentList()
                  .title('所有产品')
                  .filter('_type == "product"')
              ),
            S.divider(),
            S.listItem()
              .title('分类管理')
              .child(
                S.documentList()
                  .title('产品分类')
                  .filter('_type == "category"')
              ),
            // ... 其他内容类型
          ])
    }),
    media(), // 媒体库插件
    visionTool() // GROQ 查询工具
  ],
  
  schema: {
    types: schemas,
  },
  
  // 自定义 Studio 界面
  studio: {
    components: {
      // 自定义 Logo 等
    }
  }
})
```

### 4. 创建 Sanity 客户端

```typescript
// src/lib/sanity/client.ts
import { createClient } from '@sanity/client'
import imageUrlBuilder from '@sanity/image-url'

export const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID!,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET!,
  apiVersion: '2024-01-01',
  useCdn: false, // ISR 模式下设为 false
  token: process.env.SANITY_API_TOKEN, // 用于 webhook
})

const builder = imageUrlBuilder(client)

export function urlFor(source: any) {
  return builder.image(source)
}

// 预览模式客户端
export const previewClient = createClient({
  ...client.config(),
  useCdn: false,
  token: process.env.SANITY_API_TOKEN,
  perspective: 'previewDrafts',
})
```

## Next.js 应用开发

### 1. 配置多语言支持

```typescript
// src/app/i18n.ts
import { notFound } from 'next/navigation'
import { getRequestConfig } from 'next-intl/server'

export const locales = ['zh', 'en', 'ar'] as const
export const defaultLocale = 'zh' as const

export default getRequestConfig(async ({ locale }) => {
  if (!locales.includes(locale as any)) notFound()

  return {
    messages: (await import(`@/messages/${locale}.json`)).default
  }
})
```

```typescript
// src/middleware.ts
import createMiddleware from 'next-intl/middleware'
import { locales, defaultLocale } from '@/app/i18n'

export default createMiddleware({
  locales,
  defaultLocale,
  localePrefix: 'as-needed' // 中文不显示前缀
})

export const config = {
  matcher: ['/((?!api|_next|_vercel|.*\\..*).*)']
}
```

### 2. 实现 ISR 重验证 API

```typescript
// src/app/api/revalidate/route.ts
import { revalidatePath, revalidateTag } from 'next/cache'
import { headers } from 'next/headers'
import { NextResponse } from 'next/server'

// 验证 Webhook 签名
function isValidSignature(body: string, signature: string) {
  const secret = process.env.SANITY_WEBHOOK_SECRET!
  // 实现 HMAC 验证逻辑
  return true // 简化示例
}

export async function POST(request: Request) {
  try {
    const body = await request.text()
    const signature = headers().get('sanity-webhook-signature')
    
    if (!isValidSignature(body, signature!)) {
      return NextResponse.json({ error: 'Invalid signature' }, { status: 401 })
    }

    const data = JSON.parse(body)
    const { _type, slug } = data

    // 根据内容类型重验证不同路径
    switch (_type) {
      case 'product':
        // 重验证产品详情页（所有语言版本）
        for (const locale of ['zh', 'en', 'ar']) {
          const paths = [
            `/${locale}/products/${slug.current}`,
            `/${locale}/products`, // 产品列表页
          ]
          
          // 如果是中文，还需要重验证无前缀路径
          if (locale === 'zh') {
            paths.push(`/products/${slug.current}`)
            paths.push('/products')
          }
          
          for (const path of paths) {
            await revalidatePath(path)
          }
        }
        
        // 重验证首页
        await revalidatePath('/')
        
        // 基于标签的重验证
        await revalidateTag('products')
        break
        
      case 'category':
        await revalidateTag('categories')
        await revalidatePath('/products')
        break
        
      // ... 其他内容类型
    }

    return NextResponse.json({
      revalidated: true,
      paths: data.paths,
      timestamp: new Date().toISOString()
    })
    
  } catch (error) {
    console.error('Revalidation error:', error)
    return NextResponse.json(
      { error: 'Revalidation failed' },
      { status: 500 }
    )
  }
}
```

### 3. 实现产品列表页

```typescript
// src/app/[locale]/products/page.tsx
import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { getTranslations } from 'next-intl/server'
import { client } from '@/lib/sanity/client'
import { productsQuery } from '@/lib/sanity/queries'
import ProductGrid from '@/components/product/ProductGrid'
import FilterBar from '@/components/product/FilterBar'

interface Props {
  params: { locale: string }
  searchParams: { category?: string; tag?: string }
}

// 生成元数据
export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const t = await getTranslations({ locale: params.locale, namespace: 'Products' })
  
  return {
    title: t('page.title'),
    description: t('page.description'),
  }
}

// 页面组件
export default async function ProductsPage({ params, searchParams }: Props) {
  const { locale } = params
  const { category, tag } = searchParams
  
  // 构建查询条件
  const filter = `_type == "product" && isPublished == true`
  const categoryFilter = category ? ` && category->slug.current == "${category}"` : ''
  const tagFilter = tag ? ` && "${tag}" in tags` : ''
  
  const products = await client.fetch(
    productsQuery,
    { 
      filter: filter + categoryFilter + tagFilter,
      locale 
    },
    { 
      next: { 
        revalidate: 60, // 60秒缓存
        tags: ['products'] 
      } 
    }
  )

  if (!products) notFound()

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">
        {/* 使用服务端翻译 */}
        <GetTranslation namespace="Products" id="title" />
      </h1>
      
      <FilterBar 
        categories={await getCategories()} 
        currentCategory={category}
        currentTag={tag}
      />
      
      <ProductGrid products={products} locale={locale} />
    </div>
  )
}

// ISR: 预生成常用路径
export async function generateStaticParams() {
  return [
    { locale: 'zh' },
    { locale: 'en' },
    { locale: 'ar' },
  ]
}
```

### 4. 实现产品详情页

```typescript
// src/app/[locale]/products/[slug]/page.tsx
import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { client } from '@/lib/sanity/client'
import { productBySlugQuery } from '@/lib/sanity/queries'
import ProductDetail from '@/components/product/ProductDetail'
import { urlFor } from '@/lib/sanity/client'

interface Props {
  params: { 
    locale: string
    slug: string 
  }
}

// 动态生成元数据
export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const product = await client.fetch(productBySlugQuery, {
    slug: params.slug,
    locale: params.locale
  })

  if (!product) return {}

  const title = product.name[params.locale] || product.name.zh
  const description = product.shortDescription?.[params.locale] || product.shortDescription?.zh
  const image = product.gallery?.[0] ? urlFor(product.gallery[0]).width(1200).url() : undefined

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      images: image ? [image] : [],
    },
    alternates: {
      languages: {
        'zh': `/products/${params.slug}`,
        'en': `/en/products/${params.slug}`,
        'ar': `/ar/products/${params.slug}`,
      }
    }
  }
}

export default async function ProductPage({ params }: Props) {
  const { locale, slug } = params
  
  const product = await client.fetch(
    productBySlugQuery, 
    { slug, locale },
    { 
      next: { 
        revalidate: false, // 依赖 ISR webhook
        tags: [`product-${slug}`] 
      } 
    }
  )

  if (!product || !product.isPublished) {
    notFound()
  }

  return <ProductDetail product={product} locale={locale} />
}

// 预生成热门产品页面
export async function generateStaticParams() {
  const products = await client.fetch(`
    *[_type == "product" && isPublished == true] | order(publishedAt desc) [0...20] {
      "slug": slug.current
    }
  `)

  const paths = []
  const locales = ['zh', 'en', 'ar']
  
  for (const product of products) {
    for (const locale of locales) {
      paths.push({
        locale,
        slug: product.slug
      })
    }
  }

  return paths
}
```

### 5. 实现关键组件

```typescript
// src/components/product/ProductCard.tsx
'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'
import Link from 'next/link'
import { urlFor } from '@/lib/sanity/client'
import { Product } from '@/types/sanity'

interface Props {
  product: Product
  locale: string
  index: number
}

export default function ProductCard({ product, locale, index }: Props) {
  const name = product.name[locale] || product.name.zh
  const shortDesc = product.shortDescription?.[locale] || product.shortDescription?.zh
  
  return (
    <motion.article
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, delay: index * 0.1 }}
      whileHover={{ y: -5 }}
      className="group cursor-pointer"
    >
      <Link href={`/products/${product.slug.current}`}>
        <div className="relative aspect-square overflow-hidden rounded-lg bg-gray-100">
          {product.gallery?.[0] && (
            <Image
              src={urlFor(product.gallery[0]).width(400).height(400).url()}
              alt={name}
              fill
              sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
              className="object-cover transition-transform duration-300 group-hover:scale-105"
              loading="lazy"
            />
          )}
          
          {/* 标签 */}
          {product.tags?.includes('new') && (
            <span className="absolute top-2 left-2 bg-red-500 text-white text-xs px-2 py-1 rounded">
              NEW
            </span>
          )}
          
          {/* 库存状态 */}
          {product.stockStatus === 'sold-out' && (
            <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
              <span className="text-white font-semibold">售罄</span>
            </div>
          )}
        </div>
        
        <div className="mt-4 space-y-1">
          <h3 className="font-medium text-gray-900 group-hover:text-blue-600 transition-colors">
            {name}
          </h3>
          {shortDesc && (
            <p className="text-sm text-gray-500 line-clamp-2">{shortDesc}</p>
          )}
          {product.price && (
            <p className="text-lg font-semibold">
              {product.currency === 'CNY' && '¥'}
              {product.price}
            </p>
          )}
        </div>
      </Link>
    </motion.article>
  )
}
```

```typescript
// src/components/product/ProductGallery.tsx
'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import Image from 'next/image'
import { urlFor } from '@/lib/sanity/client'
import { SanityImage } from '@/types/sanity'

interface Props {
  gallery: SanityImage[] // mainImage is now gallery[0]
  gallery?: SanityImage[]
  productName: string
}

export default function ProductGallery({ gallery, productName }: Props) {
  const [selectedImage, setSelectedImage] = useState(0)
  const images = gallery || []
  
  return (
    <div className="space-y-4">
      {/* 主图展示 */}
      <div className="relative aspect-square overflow-hidden rounded-lg bg-gray-100">
        <AnimatePresence mode="wait">
          <motion.div
            key={selectedImage}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="relative h-full"
          >
            <Image
              src={urlFor(images[selectedImage]).width(800).height(800).url()}
              alt={`${productName} - ${selectedImage + 1}`}
              fill
              sizes="(max-width: 768px) 100vw, 50vw"
              className="object-cover"
              priority={selectedImage === 0}
            />
          </motion.div>
        </AnimatePresence>
      </div>
      
      {/* 缩略图列表 */}
      {images.length > 1 && (
        <div className="grid grid-cols-4 gap-2 sm:grid-cols-6">
          {images.map((image, index) => (
            <motion.button
              key={index}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setSelectedImage(index)}
              className={`
                relative aspect-square overflow-hidden rounded-md
                ${selectedImage === index ? 'ring-2 ring-blue-500' : 'ring-1 ring-gray-200'}
              `}
            >
              <Image
                src={urlFor(image).width(150).height(150).url()}
                alt={`${productName} 缩略图 ${index + 1}`}
                fill
                sizes="150px"
                className="object-cover"
              />
            </motion.button>
          ))}
        </div>
      )}
    </div>
  )
}
```

### 6. 性能优化配置

```javascript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: ['cdn.sanity.io'],
    formats: ['image/avif', 'image/webp'],
  },
  
  // 启用 SWC 压缩
  swcMinify: true,
  
  // 生产构建优化
  productionBrowserSourceMaps: false,
  
  // 国际化配置
  i18n: {
    locales: ['zh', 'en', 'ar'],
    defaultLocale: 'zh',
  },
  
  // 实验性功能
  experimental: {
    // 优化包导入
    optimizePackageImports: ['framer-motion', '@sanity/client'],
  },
  
  // 自定义 Webpack 配置
  webpack: (config, { isServer }) => {
    // 优化打包体积
    config.optimization.splitChunks = {
      chunks: 'all',
      cacheGroups: {
        default: false,
        vendors: false,
        vendor: {
          name: 'vendor',
          chunks: 'all',
          test: /node_modules/,
        },
        common: {
          name: 'common',
          minChunks: 2,
          chunks: 'all',
          priority: 10,
          reuseExistingChunk: true,
          enforce: true,
        },
      },
    }
    
    return config
  },
  
  // 环境变量类型检查
  env: {
    NEXT_PUBLIC_SANITY_PROJECT_ID: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
    NEXT_PUBLIC_SANITY_DATASET: process.env.NEXT_PUBLIC_SANITY_DATASET,
  },
}

module.exports = nextConfig
```

## 部署配置

### 1. 环境变量设置

```bash
# .env.local
NEXT_PUBLIC_SANITY_PROJECT_ID=your-project-id
NEXT_PUBLIC_SANITY_DATASET=production
SANITY_API_TOKEN=your-read-token
SANITY_WEBHOOK_SECRET=your-webhook-secret

# Vercel 环境变量（通过 Dashboard 设置）
# 同上，但 SANITY_API_TOKEN 和 SANITY_WEBHOOK_SECRET 设为敏感变量
```

### 2. Vercel 部署配置

```json
// vercel.json
{
  "buildCommand": "pnpm build",
  "outputDirectory": ".next",
  "framework": "nextjs",
  "regions": ["sin1", "hnd1", "iad1", "cdg1"],
  "functions": {
    "app/api/revalidate/route.ts": {
      "maxDuration": 10
    }
  }
}
```

### 3. Sanity Webhook 配置

在 Sanity 管理面板中配置 Webhook：

1. 进入 manage.sanity.io
2. 选择项目 → API → Webhooks
3. 创建新 Webhook：
   - URL: `https://your-domain.vercel.app/api/revalidate`
   - Trigger on: Create, Update, Delete
   - Filter: `_type in ["product", "category", "homepage"]`
   - Secret: 与环境变量中的 `SANITY_WEBHOOK_SECRET` 一致

## 监控与维护

### 1. 性能监控

```typescript
// src/lib/monitoring.ts
export function reportWebVitals(metric: any) {
  // 发送到分析服务
  console.log(metric)
  
  // 可集成 Vercel Analytics
  if (metric.label === 'web-vital') {
    // window.gtag('event', metric.name, {
    //   value: Math.round(metric.value),
    //   metric_id: metric.id,
    //   metric_value: metric.value,
    //   metric_delta: metric.delta,
    // })
  }
}
```

### 2. 错误处理

```typescript
// src/app/[locale]/error.tsx
'use client'

import { useEffect } from 'react'
import { useTranslations } from 'next-intl'

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  const t = useTranslations('Error')

  useEffect(() => {
    // 记录错误到监控服务
    console.error(error)
  }, [error])

  return (
    <div className="flex min-h-screen flex-col items-center justify-center">
      <h2 className="text-2xl font-bold mb-4">{t('title')}</h2>
      <p className="text-gray-600 mb-8">{t('description')}</p>
      <button
        onClick={reset}
        className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
      >
        {t('retry')}
      </button>
    </div>
  )
}
```

## 开发工作流

### 1. 本地开发

```bash
# 启动 Next.js 开发服务器
pnpm dev

# 新终端：启动 Sanity Studio
cd sanity && pnpm dev

# 访问
# - Next.js: http://localhost:3000
# - Sanity Studio: http://localhost:3333
```

### 2. 构建测试

```bash
# 类型检查
pnpm type-check

# ESLint
pnpm lint

# 构建测试
pnpm build

# 本地预览生产构建
pnpm start
```

### 3. 部署流程

```bash
# 推送到 Git
git add .
git commit -m "feat: implement product pages"
git push

# Vercel 自动部署
# 查看部署状态: https://vercel.com/dashboard
```

## 待办事项与扩展

### Phase 1 (当前)
- [x] 项目初始化
- [x] Sanity CMS 配置
- [x] 多语言架构
- [x] 产品页面实现
- [x] ISR 配置
- [ ] 首页设计实现
- [ ] 搜索功能
- [ ] 分类筛选
- [ ] 移动端优化

### Phase 2
- [ ] 用户评论系统
- [ ] 产品推荐算法
- [ ] 高级搜索过滤
- [ ] PWA 支持
- [ ] 动画效果增强

### Phase 3
- [ ] 电商功能集成
- [ ] 用户账户系统
- [ ] 订单管理
- [ ] 支付集成
- [ ] 物流跟踪

## 技术债务记录

1. **图片优化**: 当前使用 Sanity 内置 CDN，后续可考虑 Cloudinary 等专业方案
2. **缓存策略**: ISR 重验证可进一步优化，考虑引入 Redis 缓存
3. **SEO 增强**: 添加结构化数据、站点地图自动生成
4. **性能监控**: 集成 Sentry 等 APM 工具
5. **A/B 测试**: 预留实验框架接口

---

*本文档将持续更新，请关注版本变化。*