# 产品标签使用指南

## 概述

产品标签系统允许运营人员为产品设置预定义的标签，这些标签会在产品卡片上以徽章形式显示，支持多语言。

## 如何设置产品标签

### 1. 进入产品编辑页面

1. 访问 Sanity Studio: http://localhost:3333
2. 在左侧菜单选择"产品"
3. 选择要编辑的产品

### 2. 设置标签

在产品编辑页面中找到"标签"字段：

- **点击标签输入框**：会出现下拉菜单显示预定义的标签选项
- **选择标签**：从下拉菜单中选择需要的标签
- **多个标签**：可以为一个产品添加多个标签
- **自定义标签**：如果需要，也可以输入自定义标签（但建议使用预定义标签以保证多语言支持）

### 3. 预定义标签说明

| 标签值 | 中文显示 | 英文显示 | 阿拉伯文显示 | 使用场景 |
|--------|----------|----------|--------------|----------|
| `new` | 新品 | NEW | جديد | 新上架的产品（建议30天内） |
| `popular` | 热门 | POPULAR | شائع | 热销或受欢迎的产品 |
| `limited` | 限定 | LIMITED | محدود | 限量版产品 |
| `preorder` | 预售 | PRE-ORDER | طلب مسبق | 预售产品 |
| `exclusive` | 独家 | EXCLUSIVE | حصري | 独家销售产品 |
| `sale` | 促销 | SALE | تخفيض | 促销产品 |

### 4. 标签显示效果

标签会在以下位置显示：
- 首页精选产品区域
- 品牌故事产品展示区
- 产品列表页
- 产品搜索结果

每个标签都有独特的颜色：
- 🟢 新品 - 绿色
- 🟣 热门 - 紫色
- 🔴 限定 - 红色
- 🟡 预售 - 黄色
- 🔵 独家 - 靛蓝色
- 🟠 促销 - 橙色

## 批量设置标签

如果需要为多个产品批量设置标签，可以使用迁移脚本：

```bash
# 查看哪些产品会被自动添加标签（预览模式）
npm run migrate:tags:dry-run

# 执行自动标签迁移
npm run migrate:tags
```

自动迁移规则：
- 30天内发布的产品 → 自动添加 `new` 标签
- 预售状态的产品 → 自动添加 `preorder` 和 `limited` 标签
- 分类下有5个以上产品的 → 自动添加 `popular` 标签

## 最佳实践

1. **标签数量**：建议每个产品不超过3个标签，避免视觉混乱
2. **标签组合**：
   - 预售产品通常同时使用 `preorder` + `limited`
   - 新品促销可以同时使用 `new` + `sale`
3. **定期更新**：
   - 新品标签建议30天后移除
   - 促销标签在活动结束后及时移除
4. **一致性**：同类产品使用相似的标签策略

## 常见问题

### Q: 为什么点击标签字段没有下拉选项？
A: 请确保：
1. Sanity Studio 已重新启动（修改 schema 后需要重启）
2. 清除浏览器缓存并刷新页面
3. 使用最新版本的 Sanity Studio

### Q: 可以添加自定义标签吗？
A: 可以，但自定义标签不会有预设的多语言翻译，需要前端额外配置才能正确显示。

### Q: 如何移除标签？
A: 在标签字段中，点击已添加标签旁边的 × 按钮即可移除。

### Q: 标签会影响产品排序吗？
A: 不会，标签仅用于视觉展示。如需按标签筛选产品，可以使用产品筛选功能。

## 技术说明

- Schema 定义：`sanity/schemas/product.ts`
- 前端显示：`src/components/home/<USER>
- 标签样式：使用 Tailwind CSS 类定义
- 多语言支持：硬编码在组件中，确保快速渲染