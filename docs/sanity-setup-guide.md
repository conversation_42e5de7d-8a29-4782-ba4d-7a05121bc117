# Sanity CMS 设置指南

本指南将帮助你从零开始设置 Sanity CMS，以便在开发环境中正常使用内容管理功能。

## 前置条件

- Node.js >= 18.17.0
- npm 或 pnpm
- 互联网连接

## 第一步：注册 Sanity 账户

### 1.1 创建账户

1. 访问 [sanity.io](https://sanity.io)
2. 点击右上角 "Get started" 或 "Sign up"
3. 选择注册方式：
   - **GitHub**: 推荐，快速授权
   - **Google**: 使用 Google 账号
   - **Email/Password**: 传统邮箱注册

### 1.2 验证邮箱

如果使用邮箱注册，需要验证邮箱地址。

## 第二步：创建新项目

### 2.1 项目设置

1. 登录后，点击 "Create new project"
2. 填写项目信息：
   ```
   Project name: MyNgaPop-cms
   Dataset: production (保持默认)
   Region: 选择最近的区域 (如 Singapore, Tokyo, US-East)
   ```
3. 点击 "Create project"

### 2.2 记录项目信息

创建成功后，记录以下信息：
- **Project ID**: 类似 `abc123def456` 的字符串
- **Dataset**: 通常是 `production`
- **Project URL**: 项目管理地址

## 第三步：获取 API Token

### 3.1 创建 API Token

1. 在项目面板中点击 "Settings"
2. 选择左侧菜单的 "API" 标签
3. 点击 "Add API token"
4. 配置 Token：
   ```
   Name: Development Token
   Permissions: Editor
   ```
5. 点击 "Create token"
6. **重要**: 复制生成的 token，刷新页面后将无法再次查看

### 3.2 Token 权限说明

- **Viewer**: 只读权限
- **Editor**: 读写权限（推荐用于开发）
- **Admin**: 完全权限（谨慎使用）

## 第四步：配置项目环境变量

### 4.1 更新 .env.local

将获取的信息更新到 `.env.local` 文件：

```bash
# Sanity 配置
NEXT_PUBLIC_SANITY_PROJECT_ID=your-actual-project-id
NEXT_PUBLIC_SANITY_DATASET=production
SANITY_API_TOKEN=your-actual-api-token
SANITY_WEBHOOK_SECRET=your-webhook-secret-123

# Next.js 配置
NEXT_PUBLIC_BASE_URL=http://localhost:3000
```

### 4.2 配置说明

- `NEXT_PUBLIC_SANITY_PROJECT_ID`: 从 Sanity 项目获取的 Project ID
- `NEXT_PUBLIC_SANITY_DATASET`: 数据集名称，通常是 `production`
- `SANITY_API_TOKEN`: 创建的 API Token
- `SANITY_WEBHOOK_SECRET`: 自定义的 webhook 密钥（用于 ISR）

## 第五步：配置 Sanity Studio

### 5.1 创建 Sanity 环境文件

在 `sanity/` 目录创建 `.env` 文件：

```bash
# sanity/.env
SANITY_STUDIO_PROJECT_ID=your-actual-project-id
SANITY_STUDIO_DATASET=production
```

### 5.2 验证配置

检查 `sanity/sanity.config.ts` 文件是否正确读取环境变量：

```typescript
export default defineConfig({
  name: 'default',
  title: '动漫周边品牌 CMS',
  
  projectId: process.env.SANITY_STUDIO_PROJECT_ID || 'your-project-id',
  dataset: process.env.SANITY_STUDIO_DATASET || 'production',
  
  // ... 其他配置
})
```

## 第六步：启动和测试

### 6.1 启动服务

```bash
# 启动 Next.js 开发服务器
pnpm dev

# 新终端：启动 Sanity Studio
cd sanity && pnpm dev
```

### 6.2 访问验证

- **Next.js 应用**: http://localhost:3000
- **Sanity Studio**: http://localhost:3333

### 6.3 测试内容管理

1. 访问 Sanity Studio (http://localhost:3333)
2. 使用 Sanity 账户登录
3. 尝试创建一个测试产品
4. 检查 Next.js 应用是否能正常显示

## 第七步：配置 Webhook（生产环境）

### 7.1 创建 Webhook

1. 在 Sanity 项目设置中选择 "Webhooks"
2. 点击 "Create webhook"
3. 配置：
   ```
   Name: Next.js ISR Webhook
   URL: https://your-domain.vercel.app/api/revalidate
   Dataset: production
   Trigger on: Create, Update, Delete
   Filter: _type in ["product", "category", "ipSeries"]
   Secret: your-webhook-secret-123
   ```

### 7.2 测试 Webhook

发布内容后，检查网站是否自动更新。

## 故障排除

### 常见问题

#### 1. Sanity Studio 无法启动

**错误**: `Project ID not found`

**解决方案**:
```bash
# 检查环境变量
echo $SANITY_STUDIO_PROJECT_ID

# 重新配置
cd sanity
sanity login
sanity projects list
```

#### 2. 无法连接到 Sanity

**错误**: `Unauthorized`

**解决方案**:
- 检查 API Token 是否正确
- 确认 Token 权限足够
- 重新创建 API Token

#### 3. 内容无法加载

**错误**: `GROQ query failed`

**解决方案**:
- 检查 Project ID 和 Dataset 是否正确
- 确认网络连接正常
- 检查 Sanity 项目状态

#### 4. 多语言内容显示问题

**解决方案**:
- 检查 Schema 中的 `localeString` 类型
- 确认内容已填写对应语言版本
- 重启开发服务器

### 有用的命令

```bash
# 检查 Sanity 状态
sanity status

# 列出所有项目
sanity projects list

# 检查当前用户
sanity users me

# 重新登录
sanity logout
sanity login

# 检查数据集
sanity dataset list

# 导出数据（备份）
sanity dataset export production backup.tar.gz
```

## 下一步

完成 Sanity 设置后，你可以：

1. 创建测试内容
2. 配置自定义 Schema
3. 设置 Webhook 自动更新
4. 部署到生产环境

## 参考资源

- [Sanity 官方文档](https://www.sanity.io/docs)
- [Next.js + Sanity 集成指南](https://www.sanity.io/docs/nextjs)
- [GROQ 查询语言](https://www.sanity.io/docs/groq)

---

如果遇到问题，请查阅 [FAQ](../README.md#故障排除) 或提交 Issue。