# Sanity Schema 扩展指南

本指南展示了如何在现有的 MyNgaPop 项目基础上扩展 Sanity schema，支持更多属性和复杂的多层关系。

## 📋 目录
- [当前架构分析](#当前架构分析)
- [扩展Schema示例](#扩展schema示例)
- [复杂关系结构](#复杂关系结构)
- [高级查询示例](#高级查询示例)
- [迁移策略](#迁移策略)
- [性能优化](#性能优化)

## 当前架构分析

### 现有实体关系
```
Product (产品)
├── category (必需) → Category (分类)
├── ipSeries (可选) → IP Series (IP系列)
└── seo (可选) → SEO (SEO设置)

Category (分类): 独立存在，支持排序
IP Series (IP系列): 独立存在，支持启用/禁用
```

### 现有优势
- ✅ 完整的多语言支持 (中英阿三语言)
- ✅ 富媒体支持 (主图 + 画册)
- ✅ 电商基础功能 (价格、库存、发布状态)
- ✅ SEO优化
- ✅ 灵活的标签系统

### 扩展空间
- 🔄 层次化分类系统
- 🔄 产品变体管理
- 🔄 角色和制造商关联
- 🔄 用户生成内容
- 🔄 营销活动管理

## 扩展Schema示例

### 1. 角色管理系统

```typescript
// sanity/schemas/character.ts
import {defineType, defineField} from 'sanity'

export default defineType({
  name: 'character',
  title: '角色',
  type: 'document',
  fields: [
    defineField({
      name: 'name',
      title: '角色名称',
      type: 'localeString',
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'slug',
      title: 'URL 标识',
      type: 'slug',
      options: {
        source: 'name.zh',
        maxLength: 96,
      },
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'series',
      title: '所属系列',
      type: 'reference',
      to: [{type: 'ipSeries'}],
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'description',
      title: '角色介绍',
      type: 'localeBlockContent',
    }),
    defineField({
      name: 'characterType',
      title: '角色类型',
      type: 'string',
      options: {
        list: [
          {title: '主角', value: 'protagonist'},
          {title: '反派', value: 'antagonist'},
          {title: '配角', value: 'supporting'},
          {title: '次要角色', value: 'minor'}
        ]
      },
      initialValue: 'supporting'
    }),
    defineField({
      name: 'images',
      title: '角色图片',
      type: 'array',
      of: [
        {
          type: 'image',
          options: {hotspot: true}
        }
      ],
      options: {
        layout: 'grid'
      }
    }),
    defineField({
      name: 'popularity',
      title: '人气值',
      type: 'number',
      validation: Rule => Rule.min(0).max(100)
    }),
    defineField({
      name: 'tags',
      title: '标签',
      type: 'array',
      of: [{type: 'string'}],
      options: {
        layout: 'tags'
      }
    }),
    defineField({
      name: 'isActive',
      title: '是否启用',
      type: 'boolean',
      initialValue: true
    })
  ],
  preview: {
    select: {
      title: 'name.zh',
      subtitle: 'series.name.zh',
      media: 'images.0',
      characterType: 'characterType'
    },
    prepare({title, subtitle, media, characterType}) {
      const typeIcon = {
        'protagonist': '👑',
        'antagonist': '😈',
        'supporting': '👥',
        'minor': '👤'
      }[characterType] || '❓'
      
      return {
        title: title || '未命名角色',
        subtitle: `${subtitle || '未知系列'} ${typeIcon}`,
        media
      }
    }
  }
})
```

### 2. 制造商和品牌系统

```typescript
// sanity/schemas/manufacturer.ts
import {defineType, defineField} from 'sanity'

export default defineType({
  name: 'manufacturer',
  title: '制造商',
  type: 'document',
  fields: [
    defineField({
      name: 'name',
      title: '制造商名称',
      type: 'localeString',
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'slug',
      title: 'URL 标识',
      type: 'slug',
      options: {
        source: 'name.zh',
        maxLength: 96,
      },
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'description',
      title: '制造商介绍',
      type: 'localeText',
    }),
    defineField({
      name: 'logo',
      title: '制造商标志',
      type: 'image',
      options: {
        hotspot: true,
      }
    }),
    defineField({
      name: 'website',
      title: '官方网站',
      type: 'url',
    }),
    defineField({
      name: 'country',
      title: '国家/地区',
      type: 'string',
      options: {
        list: [
          {title: '日本', value: 'JP'},
          {title: '中国', value: 'CN'},
          {title: '美国', value: 'US'},
          {title: '韩国', value: 'KR'},
          {title: '其他', value: 'OTHER'}
        ]
      }
    }),
    defineField({
      name: 'established',
      title: '成立时间',
      type: 'date',
    }),
    defineField({
      name: 'specialties',
      title: '专业领域',
      type: 'array',
      of: [{type: 'string'}],
      options: {
        list: [
          {title: '手办', value: 'figures'},
          {title: 'Nendoroid', value: 'nendoroids'},
          {title: 'Figma', value: 'figma'},
          {title: '比例手办', value: 'scale-figures'},
          {title: '模型套件', value: 'model-kits'},
          {title: '毛绒玩具', value: 'plushies'}
        ]
      }
    }),
    defineField({
      name: 'isVerified',
      title: '官方认证',
      type: 'boolean',
      initialValue: false
    }),
    defineField({
      name: 'isActive',
      title: '是否启用',
      type: 'boolean',
      initialValue: true
    })
  ],
  preview: {
    select: {
      title: 'name.zh',
      subtitle: 'country',
      media: 'logo',
      isVerified: 'isVerified'
    },
    prepare({title, subtitle, media, isVerified}) {
      const verifiedIcon = isVerified ? '✅' : '📝'
      const countryName = {
        'JP': '日本',
        'CN': '中国',
        'US': '美国',
        'KR': '韩国',
        'OTHER': '其他'
      }[subtitle] || subtitle
      
      return {
        title: title || '未命名制造商',
        subtitle: `${countryName} ${verifiedIcon}`,
        media
      }
    }
  }
})
```

```typescript
// sanity/schemas/brand.ts
import {defineType, defineField} from 'sanity'

export default defineType({
  name: 'brand',
  title: '品牌',
  type: 'document',
  fields: [
    defineField({
      name: 'name',
      title: '品牌名称',
      type: 'localeString',
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'slug',
      title: 'URL 标识',
      type: 'slug',
      options: {
        source: 'name.zh',
        maxLength: 96,
      },
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'manufacturer',
      title: '制造商',
      type: 'reference',
      to: [{type: 'manufacturer'}],
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'logo',
      title: '品牌标志',
      type: 'image',
      options: {
        hotspot: true,
      }
    }),
    defineField({
      name: 'description',
      title: '品牌介绍',
      type: 'localeText',
    }),
    defineField({
      name: 'productLines',
      title: '产品线',
      type: 'array',
      of: [{type: 'string'}],
      options: {
        list: [
          {title: 'Nendoroid', value: 'nendoroid'},
          {title: 'figma', value: 'figma'},
          {title: 'Pop Up Parade', value: 'pop-up-parade'},
          {title: 'Scale Figure', value: 'scale-figure'},
          {title: 'Prize Figure', value: 'prize-figure'},
          {title: 'Garage Kit', value: 'garage-kit'}
        ]
      }
    }),
    defineField({
      name: 'isActive',
      title: '是否启用',
      type: 'boolean',
      initialValue: true
    })
  ],
  preview: {
    select: {
      title: 'name.zh',
      subtitle: 'manufacturer.name.zh',
      media: 'logo'
    },
    prepare({title, subtitle, media}) {
      return {
        title: title || '未命名品牌',
        subtitle: subtitle || '未知制造商',
        media
      }
    }
  }
})
```

### 3. 产品变体系统

```typescript
// sanity/schemas/productVariant.ts
import {defineType, defineField} from 'sanity'

export default defineType({
  name: 'productVariant',
  title: '产品变体',
  type: 'document',
  fields: [
    defineField({
      name: 'name',
      title: '变体名称',
      type: 'localeString',
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'parentProduct',
      title: '主产品',
      type: 'reference',
      to: [{type: 'product'}],
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'variantType',
      title: '变体类型',
      type: 'string',
      options: {
        list: [
          {title: '比例', value: 'scale'},
          {title: '颜色', value: 'color'},
          {title: '版本', value: 'edition'},
          {title: '尺寸', value: 'size'},
          {title: '材质', value: 'material'}
        ]
      },
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'attributes',
      title: '变体属性',
      type: 'object',
      fields: [
        defineField({
          name: 'scale',
          title: '比例',
          type: 'string',
          options: {
            list: [
              {title: '1/4', value: '1/4'},
              {title: '1/6', value: '1/6'},
              {title: '1/7', value: '1/7'},
              {title: '1/8', value: '1/8'},
              {title: '1/12', value: '1/12'}
            ]
          }
        }),
        defineField({
          name: 'color',
          title: '颜色',
          type: 'string'
        }),
        defineField({
          name: 'material',
          title: '材质',
          type: 'array',
          of: [{type: 'string'}],
          options: {
            list: [
              {title: 'PVC', value: 'PVC'},
              {title: 'ABS', value: 'ABS'},
              {title: '树脂', value: 'resin'},
              {title: '金属', value: 'metal'},
              {title: '布料', value: 'fabric'}
            ]
          }
        }),
        defineField({
          name: 'edition',
          title: '版本',
          type: 'string',
          options: {
            list: [
              {title: '标准版', value: 'standard'},
              {title: '限定版', value: 'limited'},
              {title: '特别版', value: 'special'},
              {title: '初回版', value: 'first-edition'}
            ]
          }
        })
      ]
    }),
    defineField({
      name: 'sku',
      title: 'SKU',
      type: 'string',
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'price',
      title: '价格',
      type: 'number',
      validation: Rule => Rule.required().min(0)
    }),
    defineField({
      name: 'currency',
      title: '货币',
      type: 'string',
      options: {
        list: [
          {title: '人民币', value: 'CNY'},
          {title: '美元', value: 'USD'},
          {title: '日元', value: 'JPY'},
          {title: '阿联酋迪拉姆', value: 'AED'}
        ]
      },
      initialValue: 'CNY'
    }),
    defineField({
      name: 'stockQuantity',
      title: '库存数量',
      type: 'number',
      validation: Rule => Rule.min(0)
    }),
    defineField({
      name: 'images',
      title: '变体图片',
      type: 'array',
      of: [
        {
          type: 'image',
          options: {hotspot: true}
        }
      ]
    }),
    defineField({
      name: 'isActive',
      title: '是否启用',
      type: 'boolean',
      initialValue: true
    })
  ],
  preview: {
    select: {
      title: 'name.zh',
      subtitle: 'parentProduct.name.zh',
      media: 'images.0',
      variantType: 'variantType',
      price: 'price',
      currency: 'currency'
    },
    prepare({title, subtitle, media, variantType, price, currency}) {
      const typeIcon = {
        'scale': '📏',
        'color': '🎨',
        'edition': '✨',
        'size': '📐',
        'material': '🧱'
      }[variantType] || '❓'
      
      return {
        title: title || '未命名变体',
        subtitle: `${subtitle || '未知产品'} ${typeIcon} ¥${price || 0}`,
        media
      }
    }
  }
})
```

### 4. 层次化分类系统

```typescript
// sanity/schemas/categoryEnhanced.ts
import {defineType, defineField} from 'sanity'

export default defineType({
  name: 'categoryEnhanced',
  title: '增强分类',
  type: 'document',
  fields: [
    defineField({
      name: 'name',
      title: '分类名称',
      type: 'localeString',
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'slug',
      title: 'URL 标识',
      type: 'slug',
      options: {
        source: 'name.zh',
        maxLength: 96,
      },
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'parent',
      title: '父分类',
      type: 'reference',
      to: [{type: 'categoryEnhanced'}],
      options: {
        filter: ({document}) => ({
          filter: '_id != $id',
          params: {id: document._id}
        })
      }
    }),
    defineField({
      name: 'description',
      title: '分类描述',
      type: 'localeText',
    }),
    defineField({
      name: 'image',
      title: '分类图片',
      type: 'image',
      options: {
        hotspot: true,
      }
    }),
    defineField({
      name: 'level',
      title: '层级',
      type: 'number',
      readOnly: true,
      initialValue: 0
    }),
    defineField({
      name: 'path',
      title: '分类路径',
      type: 'string',
      readOnly: true
    }),
    defineField({
      name: 'sortOrder',
      title: '排序',
      type: 'number',
      initialValue: 0
    }),
    defineField({
      name: 'isActive',
      title: '是否启用',
      type: 'boolean',
      initialValue: true
    }),
    defineField({
      name: 'seo',
      title: 'SEO 设置',
      type: 'seo',
    })
  ],
  preview: {
    select: {
      title: 'name.zh',
      subtitle: 'parent.name.zh',
      media: 'image',
      level: 'level'
    },
    prepare({title, subtitle, media, level}) {
      const levelPrefix = '　'.repeat(level || 0)
      const levelIcon = level === 0 ? '📂' : level === 1 ? '📁' : '📄'
      
      return {
        title: `${levelPrefix}${levelIcon} ${title || '未命名分类'}`,
        subtitle: subtitle ? `父分类: ${subtitle}` : '顶级分类',
        media
      }
    }
  }
})
```

### 5. 评价系统

```typescript
// sanity/schemas/review.ts
import {defineType, defineField} from 'sanity'

export default defineType({
  name: 'review',
  title: '用户评价',
  type: 'document',
  fields: [
    defineField({
      name: 'product',
      title: '产品',
      type: 'reference',
      to: [{type: 'product'}],
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'customerName',
      title: '用户名称',
      type: 'string',
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'rating',
      title: '评分',
      type: 'number',
      validation: Rule => Rule.required().min(1).max(5)
    }),
    defineField({
      name: 'title',
      title: '评价标题',
      type: 'localeString',
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'content',
      title: '评价内容',
      type: 'localeBlockContent',
      validation: Rule => Rule.required()
    }),
    defineField({
      name: 'images',
      title: '评价图片',
      type: 'array',
      of: [
        {
          type: 'image',
          options: {hotspot: true}
        }
      ]
    }),
    defineField({
      name: 'isVerified',
      title: '已验证评价',
      type: 'boolean',
      initialValue: false
    }),
    defineField({
      name: 'purchaseVerified',
      title: '购买验证',
      type: 'boolean',
      initialValue: false
    }),
    defineField({
      name: 'helpful',
      title: '有用投票',
      type: 'number',
      initialValue: 0
    }),
    defineField({
      name: 'isApproved',
      title: '审核通过',
      type: 'boolean',
      initialValue: false
    }),
    defineField({
      name: 'moderatorNotes',
      title: '管理员备注',
      type: 'text'
    })
  ],
  preview: {
    select: {
      title: 'title.zh',
      subtitle: 'product.name.zh',
      media: 'images.0',
      rating: 'rating',
      customerName: 'customerName'
    },
    prepare({title, subtitle, media, rating, customerName}) {
      const stars = '⭐'.repeat(rating || 0)
      
      return {
        title: title || '未命名评价',
        subtitle: `${subtitle || '未知产品'} | ${customerName} ${stars}`,
        media
      }
    }
  }
})
```

### 6. 扩展产品Schema

```typescript
// 为现有产品添加新字段
// sanity/schemas/productEnhanced.ts
import {defineType, defineField} from 'sanity'

export default defineType({
  name: 'productEnhanced',
  title: '增强产品',
  type: 'document',
  fields: [
    // ... 现有字段 ...
    
    // 新增字段
    defineField({
      name: 'characters',
      title: '相关角色',
      type: 'array',
      of: [
        {
          type: 'reference',
          to: [{type: 'character'}]
        }
      ]
    }),
    defineField({
      name: 'manufacturer',
      title: '制造商',
      type: 'reference',
      to: [{type: 'manufacturer'}]
    }),
    defineField({
      name: 'brand',
      title: '品牌',
      type: 'reference',
      to: [{type: 'brand'}]
    }),
    defineField({
      name: 'variants',
      title: '产品变体',
      type: 'array',
      of: [
        {
          type: 'reference',
          to: [{type: 'productVariant'}]
        }
      ]
    }),
    defineField({
      name: 'specifications',
      title: '产品规格',
      type: 'object',
      fields: [
        defineField({
          name: 'height',
          title: '高度 (mm)',
          type: 'number'
        }),
        defineField({
          name: 'width',
          title: '宽度 (mm)',
          type: 'number'
        }),
        defineField({
          name: 'depth',
          title: '深度 (mm)',
          type: 'number'
        }),
        defineField({
          name: 'weight',
          title: '重量 (g)',
          type: 'number'
        }),
        defineField({
          name: 'scale',
          title: '比例',
          type: 'string',
          options: {
            list: [
              {title: '1/4', value: '1/4'},
              {title: '1/6', value: '1/6'},
              {title: '1/7', value: '1/7'},
              {title: '1/8', value: '1/8'},
              {title: '1/12', value: '1/12'},
              {title: '非比例', value: 'non-scale'}
            ]
          }
        }),
        defineField({
          name: 'material',
          title: '材质',
          type: 'array',
          of: [{type: 'string'}],
          options: {
            list: [
              {title: 'PVC', value: 'PVC'},
              {title: 'ABS', value: 'ABS'},
              {title: '树脂', value: 'resin'},
              {title: '金属', value: 'metal'},
              {title: '布料', value: 'fabric'}
            ]
          }
        }),
        defineField({
          name: 'releaseDate',
          title: '发售日期',
          type: 'date'
        }),
        defineField({
          name: 'preorderDate',
          title: '预售开始日期',
          type: 'date'
        })
      ]
    }),
    defineField({
      name: 'collectibles',
      title: '收藏品信息',
      type: 'object',
      fields: [
        defineField({
          name: 'series',
          title: '系列',
          type: 'string'
        }),
        defineField({
          name: 'rarity',
          title: '稀有度',
          type: 'string',
          options: {
            list: [
              {title: '普通', value: 'common'},
              {title: '稀有', value: 'rare'},
              {title: '超稀有', value: 'ultra-rare'},
              {title: '限定', value: 'limited'},
              {title: '特别版', value: 'special'}
            ]
          }
        }),
        defineField({
          name: 'collectibleNumber',
          title: '收藏编号',
          type: 'string'
        }),
        defineField({
          name: 'limitedEdition',
          title: '限定版',
          type: 'boolean',
          initialValue: false
        }),
        defineField({
          name: 'editionSize',
          title: '限定数量',
          type: 'number'
        })
      ]
    }),
    defineField({
      name: 'reviews',
      title: '用户评价',
      type: 'array',
      of: [
        {
          type: 'reference',
          to: [{type: 'review'}]
        }
      ]
    }),
    defineField({
      name: 'averageRating',
      title: '平均评分',
      type: 'number',
      readOnly: true,
      validation: Rule => Rule.min(0).max(5)
    }),
    defineField({
      name: 'reviewCount',
      title: '评价数量',
      type: 'number',
      readOnly: true,
      initialValue: 0
    })
  ],
  preview: {
    select: {
      title: 'name.zh',
      subtitle: 'category.name.zh',
      media: 'gallery', // Use first image from gallery
      published: 'isPublished',
      stockStatus: 'stockStatus',
      rating: 'averageRating'
    },
    prepare({title, subtitle, media, published, stockStatus, rating}) {
      const statusIcon = published ? '✅' : '📝'
      const stockIcon = {
        'in-stock': '📦',
        'pre-order': '⏰',
        'sold-out': '❌'
      }[stockStatus] || '❓'
      const ratingStars = rating ? `⭐${rating.toFixed(1)}` : ''
      
      return {
        title: title || '未命名产品',
        subtitle: `${subtitle || '未分类'} ${statusIcon} ${stockIcon} ${ratingStars}`,
        media
      }
    }
  }
})
```

## 复杂关系结构

### 关系图谱
```
ProductEnhanced (增强产品)
├── categoryEnhanced (层次化分类)
│   └── parent → categoryEnhanced (父分类)
├── ipSeries (IP系列)
├── characters[] (角色数组)
│   └── series → ipSeries (角色所属系列)
├── manufacturer (制造商)
├── brand (品牌)
│   └── manufacturer → manufacturer (品牌制造商)
├── variants[] (产品变体)
│   └── parentProduct → productEnhanced (主产品)
├── reviews[] (用户评价)
│   └── product → productEnhanced (评价产品)
└── seo (SEO设置)
```

### 数据流向
```
用户浏览 → 分类 → 产品 → 变体选择 → 评价查看
     ↓         ↓       ↓         ↓          ↓
  层次导航   产品列表   产品详情   规格对比    用户反馈
```

## 高级查询示例

### 1. 多层级分类查询
```groq
// 获取分类树结构
*[_type == "categoryEnhanced" && !defined(parent)] | order(sortOrder asc) {
  name,
  "slug": slug.current,
  level,
  "children": *[_type == "categoryEnhanced" && parent._ref == ^._id] | order(sortOrder asc) {
    name,
    "slug": slug.current,
    level,
    "children": *[_type == "categoryEnhanced" && parent._ref == ^._id] | order(sortOrder asc) {
      name,
      "slug": slug.current,
      level
    }
  }
}
```

### 2. 产品完整信息查询
```groq
// 获取产品的所有关联信息
*[_type == "productEnhanced" && slug.current == $slug][0] {
  ...,
  category->{
    name,
    "slug": slug.current,
    parent->{
      name,
      "slug": slug.current
    }
  },
  characters[]->{
    name,
    "slug": slug.current,
    characterType,
    series->{
      name,
      "slug": slug.current
    }
  },
  manufacturer->{
    name,
    "slug": slug.current,
    country,
    logo
  },
  brand->{
    name,
    "slug": slug.current,
    logo,
    manufacturer->{
      name,
      country
    }
  },
  variants[]->{
    name,
    variantType,
    attributes,
    price,
    currency,
    images
  },
  reviews[]->{
    customerName,
    rating,
    title,
    content,
    images,
    helpful,
    _createdAt
  }
}
```

### 3. 角色相关产品查询
```groq
// 获取某个角色的所有相关产品
*[_type == "character" && slug.current == $characterSlug][0] {
  name,
  description,
  images,
  series->{
    name,
    "slug": slug.current
  },
  "products": *[_type == "productEnhanced" && references(^._id)] {
    name,
    "slug": slug.current,
    gallery, // mainImage is now gallery[0]
    price,
    currency,
    stockStatus,
    category->{
      name,
      "slug": slug.current
    }
  }
}
```

### 4. 制造商产品统计
```groq
// 获取制造商的产品统计
*[_type == "manufacturer"] {
  name,
  "slug": slug.current,
  country,
  logo,
  "productCount": count(*[_type == "productEnhanced" && manufacturer._ref == ^._id]),
  "brandCount": count(*[_type == "brand" && manufacturer._ref == ^._id]),
  "avgRating": math::avg(*[_type == "productEnhanced" && manufacturer._ref == ^._id].averageRating),
  "popularProducts": *[_type == "productEnhanced" && manufacturer._ref == ^._id] | order(averageRating desc, reviewCount desc)[0..2] {
    name,
    "slug": slug.current,
    gallery, // mainImage is now gallery[0]
    averageRating,
    reviewCount
  }
}
```

### 5. 复杂搜索查询
```groq
// 多条件搜索产品
*[_type == "productEnhanced" && (
  // 产品名称搜索
  name.zh match $searchTerm + "*" ||
  name.en match $searchTerm + "*" ||
  // 角色名称搜索
  characters[]->name.zh match $searchTerm + "*" ||
  characters[]->name.en match $searchTerm + "*" ||
  // 制造商名称搜索
  manufacturer->name.zh match $searchTerm + "*" ||
  manufacturer->name.en match $searchTerm + "*" ||
  // 品牌名称搜索
  brand->name.zh match $searchTerm + "*" ||
  brand->name.en match $searchTerm + "*"
) && isPublished == true] | order(averageRating desc, reviewCount desc) {
  name,
  "slug": slug.current,
  shortDescription,
  gallery, // mainImage is now gallery[0]
  price,
  currency,
  stockStatus,
  averageRating,
  reviewCount,
  category->{
    name,
    "slug": slug.current
  },
  characters[]->{
    name,
    "slug": slug.current
  },
  manufacturer->{
    name,
    "slug": slug.current
  },
  brand->{
    name,
    "slug": slug.current
  }
}
```

### 6. 推荐系统查询
```groq
// 基于当前产品推荐相关产品
*[_type == "productEnhanced" && slug.current == $slug][0] {
  "currentProduct": {
    name,
    category,
    characters,
    manufacturer,
    brand,
    tags
  },
  "recommendations": {
    "sameCategory": *[_type == "productEnhanced" && category._ref == ^.category._ref && _id != ^._id && isPublished == true] | order(averageRating desc)[0..3] {
      name,
      "slug": slug.current,
      gallery, // mainImage is now gallery[0]
      price,
      currency,
      averageRating
    },
    "sameCharacter": *[_type == "productEnhanced" && count(characters[@._ref in ^.characters[]._ref]) > 0 && _id != ^._id && isPublished == true] | order(averageRating desc)[0..3] {
      name,
      "slug": slug.current,
      gallery, // mainImage is now gallery[0]
      price,
      currency,
      averageRating
    },
    "sameManufacturer": *[_type == "productEnhanced" && manufacturer._ref == ^.manufacturer._ref && _id != ^._id && isPublished == true] | order(averageRating desc)[0..3] {
      name,
      "slug": slug.current,
      gallery, // mainImage is now gallery[0]
      price,
      currency,
      averageRating
    }
  }
}
```

## 迁移策略

### 1. 渐进式迁移
```typescript
// 阶段1：添加新字段（可选）
export const migrationPhase1 = {
  // 为现有产品添加新的可选字段
  characters: {type: 'array', of: [{type: 'reference', to: [{type: 'character'}]}]},
  manufacturer: {type: 'reference', to: [{type: 'manufacturer'}]},
  brand: {type: 'reference', to: [{type: 'brand'}]}
}

// 阶段2：数据迁移脚本
export const migrationPhase2 = `
// 为现有产品创建默认制造商
const defaultManufacturer = await client.create({
  _type: 'manufacturer',
  name: {zh: '未知制造商', en: 'Unknown Manufacturer'},
  slug: {current: 'unknown-manufacturer'},
  country: 'OTHER',
  isActive: true
})

// 更新现有产品
const products = await client.fetch('*[_type == "product"]')
for (const product of products) {
  await client.patch(product._id).set({
    manufacturer: {_ref: defaultManufacturer._id}
  }).commit()
}
`
```

### 2. 向前兼容
```typescript
// 查询兼容性处理
export const compatibleProductQuery = `
*[_type == "product" || _type == "productEnhanced"] {
  name,
  "slug": slug.current,
  gallery, // mainImage is now gallery[0]
  price,
  currency,
  // 安全访问新字段
  characters[]->{
    name,
    "slug": slug.current
  },
  manufacturer->{
    name,
    "slug": slug.current
  },
  // 回退到旧字段
  category->{
    name,
    "slug": slug.current
  }
}
`
```

### 3. 数据验证
```typescript
// 数据完整性检查
export const dataValidationQueries = {
  // 检查孤立的产品变体
  orphanedVariants: `
    *[_type == "productVariant" && !defined(parentProduct->)]
  `,
  
  // 检查无效的角色引用
  invalidCharacterRefs: `
    *[_type == "productEnhanced" && count(characters[!defined(@->)]) > 0]
  `,
  
  // 检查分类层级一致性
  categoryLevelInconsistency: `
    *[_type == "categoryEnhanced" && defined(parent) && parent->level >= level]
  `
}
```

## 性能优化

### 1. 索引策略
```typescript
// 推荐的索引配置
export const indexConfigurations = [
  // 产品搜索索引
  {
    name: 'product-search',
    fields: ['name.zh', 'name.en', 'shortDescription.zh', 'shortDescription.en'],
    type: 'text'
  },
  
  // 分类层级索引
  {
    name: 'category-hierarchy',
    fields: ['parent._ref', 'level', 'sortOrder'],
    type: 'compound'
  },
  
  // 产品关联索引
  {
    name: 'product-relations',
    fields: ['category._ref', 'manufacturer._ref', 'brand._ref'],
    type: 'compound'
  },
  
  // 评价统计索引
  {
    name: 'review-stats',
    fields: ['product._ref', 'rating', 'isApproved'],
    type: 'compound'
  }
]
```

### 2. 查询优化
```typescript
// 查询性能优化技巧
export const optimizedQueries = {
  // 使用投影减少数据传输
  productListing: `
    *[_type == "productEnhanced" && isPublished == true] {
      _id,
      name,
      "slug": slug.current,
      gallery, // mainImage is now gallery[0]
      price,
      currency,
      stockStatus,
      averageRating,
      reviewCount,
      "categoryName": category->name,
      "categorySlug": category->slug.current
    }
  `,
  
  // 使用参数化查询提高缓存效率
  productByCategory: `
    *[_type == "productEnhanced" && category->slug.current == $categorySlug && isPublished == true] | order(averageRating desc, _createdAt desc)[0...$limit] {
      // 只获取必要字段
    }
  `,
  
  // 批量获取关联数据
  productWithRelations: `
    *[_type == "productEnhanced" && slug.current == $slug][0] {
      ...,
      "categoryPath": category->{
        name,
        "slug": slug.current,
        parent->{
          name,
          "slug": slug.current
        }
      },
      "characterDetails": characters[]->{
        name,
        "slug": slug.current,
        characterType,
        "seriesName": series->name
      }
    }
  `
}
```

### 3. 缓存策略
```typescript
// 缓存配置建议
export const cacheStrategies = {
  // 静态数据长期缓存
  categories: {
    ttl: 3600, // 1小时
    tags: ['categories']
  },
  
  // 动态数据短期缓存
  products: {
    ttl: 300, // 5分钟
    tags: ['products']
  },
  
  // 用户相关数据实时
  reviews: {
    ttl: 60, // 1分钟
    tags: ['reviews']
  }
}
```

### 4. 批处理操作
```typescript
// 批量更新示例
export const batchOperations = {
  // 批量更新平均评分
  updateAverageRatings: async (client: SanityClient) => {
    const products = await client.fetch(`
      *[_type == "productEnhanced"] {
        _id,
        "avgRating": math::avg(*[_type == "review" && product._ref == ^._id && isApproved == true].rating),
        "reviewCount": count(*[_type == "review" && product._ref == ^._id && isApproved == true])
      }
    `)
    
    const transaction = client.transaction()
    products.forEach(product => {
      transaction.patch(product._id).set({
        averageRating: product.avgRating || 0,
        reviewCount: product.reviewCount || 0
      })
    })
    
    await transaction.commit()
  }
}
```

## 最佳实践

### 1. Schema设计原则
- **渐进式扩展**：新字段设为可选，保持向前兼容
- **数据规范化**：避免重复数据，使用引用关系
- **性能考虑**：合理使用嵌套vs引用，考虑查询模式
- **用户体验**：提供清晰的预览和编辑体验

### 2. 关系设计指南
- **一对多关系**：使用引用数组（如产品的多个变体）
- **多对多关系**：使用双向引用（如产品和角色）
- **层次关系**：使用自引用（如分类树）
- **弱关联**：使用标签或字符串匹配

### 3. 数据治理
- **版本控制**：为重要变更创建迁移脚本
- **数据验证**：定期运行数据完整性检查
- **监控告警**：监控查询性能和错误率
- **文档维护**：保持Schema文档更新

这个扩展指南展示了Sanity schema的强大扩展能力，可以支持复杂的电商业务场景。通过合理的设计和实施，可以构建一个功能丰富、性能优异的内容管理系统。