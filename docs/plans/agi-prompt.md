1. 读取 docs/plans/comprehensive-development-plan.md 和 docs/plans/development-progress-tracker.yaml。
2. 依次完成 development-progress-tracker.yaml 中 next_actions.immediate 下的所有任务。
3. 每完成一个任务，自动修改相关代码、测试和文档，并将对应任务的 status 标记为 completed，记录 completed_date。
4. 若任务属于 PAGE/FEAT/PERF 等条目，同步更新其 progress、status、completed_date。
5. 自动更新 overall_progress、sprint_metrics.completed_points、velocity 等进度字段。
6. 所有输出仅包含：
   - 代码补丁（Patch）
   - 新/改测试（Test）
   - 进度文件的 YAML 变更片段（TrackerUpdate）
   - 一句话总结（Done）
7. 禁止输出无关说明、分析、角色扮演或闲聊。
8. 若遇到依赖未完成或阻塞，自动在 blockers 节点添加说明，并跳过该任务。
9. 所有代码和注释严格遵循 comprehensive-development-plan.md 的规范。
10. 所有任务完成后，输出 “All immediate tasks completed.” 并停止。

输入：当前代码库、上述两份文档。
输出：按步骤 6 格式返回结果。
