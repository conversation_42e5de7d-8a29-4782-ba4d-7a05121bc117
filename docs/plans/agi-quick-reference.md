# MyNgaPop AGI 快速开发指南

> 供 AI 助手快速上手项目开发的精简指南  
> 版本: 1.0.0 | 更新: 2025-01-10

## 🚀 快速启动

```bash
# 1. 启动开发环境
pnpm dev                    # Next.js 开发服务器 (端口 3000)
cd sanity && pnpm dev      # Sanity Studio (端口 3333)

# 2. 常用命令
pnpm type-check           # TypeScript 检查
pnpm lint                 # ESLint 检查
pnpm build               # 构建生产版本
```

## 📁 关键目录结构

```
src/
├── app/[locale]/        # 页面文件（App Router）
│   ├── page.tsx        # 首页
│   ├── products/       # 产品相关页面
│   └── layout.tsx      # 布局组件
├── components/         # React 组件
│   ├── ui/            # 基础 UI 组件
│   ├── product/       # 产品组件
│   └── layout/        # 布局组件
├── lib/               # 工具库
│   └── sanity/        # Sanity 客户端和查询
├── messages/          # 多语言翻译文件
└── types/            # TypeScript 类型定义
```

## 🔧 核心技术配置

### Sanity CMS 连接
```typescript
// src/lib/sanity/client.ts
const client = createClient({
  projectId: '4za4x22i',
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
  apiVersion: '2024-01-01',
  useCdn: false
})
```

### 多语言路由
```typescript
// 支持语言: zh (默认), en, ar
// 路由示例:
// /products (中文)
// /en/products (英文)
// /ar/products (阿拉伯语)
```

## 📝 当前待开发任务

### 🔴 P0 - 紧急任务
1. **首页完善** `[当前任务]`
   - 文件: `/src/app/[locale]/page.tsx`
   - 组件: `/src/components/home/<USER>
   - 需求: Hero Section, 特色产品, 品牌故事

2. **搜索功能**
   - API: `/src/app/api/search/route.ts` (待创建)
   - 组件: `/src/components/search/` (待创建)
   - 查询: 使用 Sanity GROQ 全文搜索

3. **产品筛选**
   - 组件: `/src/components/product/FilterBar.tsx` (待创建)
   - 逻辑: URL 参数管理，多条件组合

### 🟡 P1 - 重要任务
- Framer Motion 动画集成
- 图片懒加载优化
- 响应式设计完善

## 🛠️ 开发模式

### 创建新页面
```typescript
// src/app/[locale]/新页面/page.tsx
import { getTranslations } from 'next-intl/server'

export default async function NewPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params
  const t = await getTranslations('PageName')
  
  return (
    <>
      <Header />
      <main>{/* 页面内容 */}</main>
      <Footer />
    </>
  )
}
```

### 创建新组件
```typescript
// src/components/category/ComponentName.tsx
'use client'  // 如果需要客户端功能

import { useTranslations } from 'next-intl'

interface Props {
  // 属性定义
}

export function ComponentName({ ...props }: Props) {
  const t = useTranslations('Component')
  
  return <div>{/* 组件内容 */}</div>
}
```

### Sanity 查询示例
```typescript
// src/lib/sanity/queries.ts
export const newQuery = `
  *[_type == "product" && isPublished == true] {
    _id,
    name,
    slug,
    gallery, // mainImage is now gallery[0]
    price
  }
`

// 使用查询
import { client } from '@/lib/sanity/client'
const data = await client.fetch(newQuery)
```

## ⚡ 性能优化要点

1. **图片优化**
   ```typescript
   import Image from 'next/image'
   <Image 
     src={urlFor(image).width(800).url()} 
     alt=""
     width={800} 
     height={600}
     loading="lazy"
   />
   ```

2. **动态导入**
   ```typescript
   const HeavyComponent = dynamic(() => import('./HeavyComponent'), {
     loading: () => <Skeleton />,
     ssr: false
   })
   ```

3. **缓存策略**
   ```typescript
   // ISR 配置
   export const revalidate = 60 // 60秒重新生成
   ```

## 🐛 常见问题

### Sanity 连接失败
- 检查环境变量配置
- 确认网络代理设置
- 查看 `fetchWithErrorHandling` 错误日志

### 多语言不生效
- 检查 `messages/[locale].json` 文件
- 确认翻译 key 正确
- 重启开发服务器

### 构建失败
- 运行 `pnpm type-check` 修复类型错误
- 检查 `import` 路径是否正确
- 清理缓存: `rm -rf .next`

## 📊 项目状态

- **整体进度**: 30%
- **已完成**: 基础架构、CMS配置、产品页面
- **进行中**: 首页开发
- **待开始**: 搜索、筛选、购物车

## 🔗 快速链接

- [完整开发计划](./comprehensive-development-plan.md)
- [进度跟踪表](./development-progress-tracker.yaml)
- [Sanity 查询示例](/docs/vision-queries.md)
- [架构设计文档](/docs/basic-report.md)

## 💡 开发建议

1. **先完成 P0 任务**，确保核心功能可用
2. **每完成一个功能就更新进度跟踪表**
3. **遇到问题先查看文档**，再考虑修改架构
4. **保持代码风格一致**，使用 ESLint 和 Prettier
5. **注重性能**，每个功能都要考虑加载速度