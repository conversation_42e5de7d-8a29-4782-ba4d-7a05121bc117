# 代码分割最佳实践指南

## 概述

本指南定义了 MyNgaPop 项目中代码分割的架构决策和实施标准。

## 核心原则

### 1. 分层策略

```
优先级从高到低：
├── 永不分割层 (Critical Path)
│   ├── Error Boundaries
│   ├── Layout Components
│   └── Core Navigation
├── 路由级分割 (Route-based)
│   ├── Page Components
│   └── Route Handlers
├── 功能级分割 (Feature-based)
│   ├── Heavy Components
│   ├── Modals/Drawers
│   └── Complex Forms
└── 库级分割 (Library-based)
    ├── Animation Libraries
    ├── Chart Libraries
    └── Editor Components
```

### 2. 国际化兼容性

#### 问题场景
- Next-intl 需要 Provider Context
- 某些组件可能在 Context 外渲染（如 Error Boundaries）
- 动态导入可能导致 Context 丢失

#### 解决方案
使用 `useSafeTranslations` hook：
```typescript
import { useSafeTranslations } from '@/hooks/useSafeTranslations'

function MyComponent() {
  const { t, isReady } = useSafeTranslations('namespace')
  // t 函数始终可用，即使 context 不存在
}
```

### 3. 动态导入模式

#### 基础模式
```typescript
// ❌ 避免：直接使用 dynamic
const Component = dynamic(() => import('./Component'))

// ✅ 推荐：使用 DynamicComponent 包装器
import { DynamicComponent } from '@/lib/code-splitting/DynamicComponent'

const Component = () => (
  <DynamicComponent
    loader={() => import('./HeavyComponent')}
    loading={<ComponentSkeleton />}
    ssr={false}
    preload="visible"
  />
)
```

#### 路由级分割
```typescript
// app/[locale]/products/page.tsx
import { createDynamicComponent } from '@/lib/code-splitting/DynamicComponent'

const ProductsPageContent = createDynamicComponent(
  () => import('@/components/products/ProductsPageContent'),
  { ssr: true }
)

export default function ProductsPage() {
  return <ProductsPageContent />
}
```

### 4. 性能监控

#### 加载时间追踪
```typescript
const DynamicComponentWithMetrics = () => (
  <DynamicComponent
    loader={async () => {
      const start = performance.now()
      const module = await import('./Component')
      console.log(`Component loaded in ${performance.now() - start}ms`)
      return module
    }}
  />
)
```

## 实施清单

### 第一阶段：基础设施
- [x] 创建 `useSafeTranslations` hook
- [x] 创建 `DynamicComponent` 包装器
- [x] 定义代码分割配置

### 第二阶段：页面级优化
- [ ] 产品页面动态加载
- [ ] 关于页面动态加载
- [ ] 联系页面动态加载

### 第三阶段：组件级优化
- [ ] 产品筛选器动态加载
- [ ] 图片画廊动态加载
- [ ] 搜索模态框动态加载

### 第四阶段：第三方库优化
- [x] Framer Motion 条件加载
- [ ] 图表库按需加载
- [ ] 富文本编辑器延迟加载

## 测试策略

### 单元测试
```typescript
// 测试动态加载组件
it('should load component dynamically', async () => {
  render(<DynamicComponent loader={() => import('./TestComponent')} />)
  
  // 初始显示加载状态
  expect(screen.getByText('Loading...')).toBeInTheDocument()
  
  // 等待组件加载
  await waitFor(() => {
    expect(screen.getByText('Test Component')).toBeInTheDocument()
  })
})
```

### E2E 测试
```typescript
// 测试页面性能
test('page should load within performance budget', async ({ page }) => {
  const metrics = await page.evaluate(() => performance.getEntriesByType('navigation'))
  expect(metrics[0].loadEventEnd).toBeLessThan(3000) // 3秒内加载完成
})
```

## 故障排除

### 常见问题

1. **Hook 在条件语句中调用**
   - 使用 `useSafeTranslations` 替代条件调用
   - 或使用组件级条件渲染

2. **SSR/SSG 兼容性问题**
   - 设置 `ssr: true` 用于需要 SSR 的组件
   - 使用 `typeof window !== 'undefined'` 检查

3. **Bundle 大小增加**
   - 检查是否有重复的动态导入
   - 使用 webpack-bundle-analyzer 分析

## 性能指标

### 目标
- 首屏加载时间 (FCP): < 1.5s
- 可交互时间 (TTI): < 3s
- 总阻塞时间 (TBT): < 300ms
- 累积布局偏移 (CLS): < 0.1

### 监控
使用 Next.js Analytics 或自定义性能监控追踪：
- 组件加载时间
- Bundle 大小变化
- 用户体验指标