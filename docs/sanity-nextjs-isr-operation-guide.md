# Sanity + Next.js + ISR 架构操作更新指南

> 本指南面向内容运营人员，详细说明如何使用 Sanity CMS 管理网站内容，实现产品信息的快速更新。

## 目录

1. [系统概述](#系统概述)
2. [登录 Sanity Studio](#登录-sanity-studio)
3. [产品管理操作](#产品管理操作)
4. [多语言内容管理](#多语言内容管理)
5. [图片和媒体管理](#图片和媒体管理)
6. [发布与预览](#发布与预览)
7. [常见问题处理](#常见问题处理)

## 系统概述

### 工作流程简图

```
运营人员 → Sanity Studio（后台） → 发布内容 → 自动触发网站更新 → 用户看到新内容
```

### 关键概念

- **Sanity Studio**：您操作的后台管理界面，中文化界面，可视化编辑
- **发布（Publish）**：将编辑的内容推送到网站的操作
- **草稿（Draft）**：未发布的内容，仅在后台可见
- **ISR 更新**：发布后网站自动更新的技术（无需了解细节，只需知道发布即生效）

## 登录 Sanity Studio

### 访问地址

```
https://[您的项目名称].sanity.studio
```

### 登录步骤

1. 打开浏览器，输入上述地址
2. 输入您的邮箱和密码
3. 点击"登录"按钮
4. 首次登录可能需要邮箱验证

### 界面说明

登录后，您会看到：
- **左侧菜单**：内容类型列表（产品、分类、首页配置等）
- **中间区域**：内容列表
- **右侧区域**：编辑表单

## 产品管理操作

### 新增产品

1. **进入产品管理**
   - 点击左侧菜单的"产品"
   - 点击右上角"+ 创建新产品"按钮

2. **填写产品信息**

   **基础信息**
   - **产品名称**（必填）：输入产品的中文名称
     - 示例：`火影忍者 - 鸣人手办 限定版`
   - **URL 标识**（自动生成）：系统会根据名称自动生成，也可手动修改
     - 自动生成示例：`huoying-renzhe-mingren-shouban-xianding-ban`
     - 建议简化为：`naruto-figure-limited`

   **产品详情**
   - **简短描述**：一句话介绍，用于列表页显示
     - 示例：`高度还原的鸣人战斗姿态，限量发售1000个`
   - **详细描述**：使用富文本编辑器，可以：
     - 添加段落文字
     - 插入项目列表（产品特点）
     - 加粗重要信息
     - 添加链接

   **分类与标签**
   - **产品分类**：从下拉菜单选择（手办模型/服装配饰/文具周边等）
   - **IP 系列**：选择所属动漫（火影忍者/海贼王/龙珠等）
   - **标签**：可多选（新品/热销/限定/预售等）

   **价格与状态**
   - **展示价格**：输入数字，如 `299`
   - **货币单位**：选择 CNY/USD 等
   - **库存状态**：选择（现货/预售/售罄）
   - **上架状态**：开关控制是否在网站显示

3. **保存草稿**
   - 填写过程中随时点击"保存"按钮保存进度
   - 保存后内容仅在后台可见，网站上还看不到

### 编辑已有产品

1. **找到产品**
   - 在产品列表中搜索或浏览
   - 点击产品名称进入编辑

2. **修改内容**
   - 直接在表单中修改需要更新的字段
   - 修改后记得保存

3. **查看修改历史**
   - 点击右上角"历史记录"图标
   - 可以查看所有修改记录
   - 必要时可以恢复到之前的版本

### 删除产品

1. **软删除（推荐）**
   - 将"上架状态"关闭
   - 产品在网站上不显示，但数据保留

2. **永久删除**
   - 在编辑界面点击"删除"按钮
   - 确认删除（此操作不可恢复）

## 多语言内容管理

### 切换语言版本

1. 在编辑界面上方，看到语言切换标签：`中文 | English | العربية`
2. 点击对应语言标签切换到该语言版本
3. 每个语言版本的内容独立维护

### 多语言内容填写技巧

1. **先完成中文版本**
   - 中文作为默认语言，优先完整填写
   - 其他语言可以后续补充

2. **保持内容结构一致**
   - 各语言版本的产品信息结构保持相同
   - 图片等媒体资源可以共用

3. **翻译要点**
   - 产品名称：可保留部分英文/日文原名
   - URL 标识：建议使用英文，全语言通用
   - 描述内容：需要准确翻译，保持信息完整

## 图片和媒体管理

### 上传产品图片

1. **主图上传**
   - 点击"产品主图"区域的上传按钮
   - 选择本地图片文件（支持 JPG/PNG/WebP）
   - 建议尺寸：1200x1200px 或以上
   - 文件大小：建议不超过 2MB

2. **多图上传**
   - 在"产品图册"区域可上传多张图片
   - 拖拽图片可调整顺序
   - 第一张默认作为列表页展示图

3. **图片优化建议**
   - 使用清晰的产品照片，白底最佳
   - 多角度展示（正面、侧面、背面、细节）
   - 避免加水印，系统会自动处理

### 媒体库管理

1. **访问媒体库**
   - 点击左侧菜单的"媒体库"
   - 可以看到所有已上传的图片

2. **整理图片**
   - 可以创建文件夹分类管理
   - 支持批量上传和删除
   - 可以添加图片标签便于搜索

3. **复用图片**
   - 在产品编辑时，可以从媒体库选择已有图片
   - 避免重复上传相同图片

## 发布与预览

### 预览功能

1. **开启预览**
   - 在编辑界面点击"预览"按钮
   - 会在新标签页打开网站预览

2. **预览检查要点**
   - 图片显示是否正常
   - 文字排版是否合理
   - 多语言切换是否正确
   - 移动端显示效果

### 发布内容

1. **发布单个产品**
   - 确认内容无误后，点击"发布"按钮
   - 系统提示"发布成功"
   - 约 10-30 秒后网站自动更新

2. **批量发布**
   - 在产品列表勾选多个产品
   - 点击"批量操作" → "发布"
   - 适用于统一上新多个产品

3. **发布状态说明**
   - 🟢 已发布：内容已同步到网站
   - 🟡 有更新：已发布但有新的修改未同步
   - ⚪ 草稿：从未发布过

### 更新验证

1. **立即检查**
   - 发布后打开网站对应页面
   - 强制刷新（Ctrl+F5 或 Cmd+Shift+R）
   - 检查内容是否已更新

2. **更新延迟说明**
   - 正常情况：10-30 秒生效
   - 高峰期：可能延迟 1-2 分钟
   - 如超过 5 分钟未更新，请联系技术支持

## 常见问题处理

### 1. 发布后网站没有更新

**可能原因与解决方法：**

- **浏览器缓存**
  - 强制刷新页面
  - 尝试无痕模式访问
  
- **CDN 缓存**
  - 等待 2-3 分钟
  - 清除浏览器缓存后重试

- **发布失败**
  - 检查 Sanity Studio 是否显示"发布成功"
  - 查看是否有红色错误提示
  - 尝试重新发布

### 2. 图片上传失败

**解决步骤：**

1. 检查图片格式（仅支持 JPG/PNG/WebP）
2. 确认文件大小不超过 10MB
3. 检查网络连接
4. 尝试刷新页面后重新上传

### 3. 内容保存失败

**处理方法：**

1. 检查必填字段是否都已填写
2. 查看是否有红色错误提示
3. 复制重要内容到记事本备份
4. 刷新页面重新编辑

### 4. 多语言内容显示错误

**检查事项：**

1. 确认在正确的语言标签下编辑
2. 检查是否已发布对应语言版本
3. 网站语言切换是否正常工作

### 5. 批量操作注意事项

- 批量发布前先预览几个产品
- 避免同时发布超过 50 个产品
- 大量更新建议在访问低峰期进行

## 最佳实践建议

### 内容编辑

1. **统一命名规范**
   - 产品名称格式：`[IP名称] - [产品类型] [特殊标识]`
   - 示例：`海贼王 - 路飞手办 四档形态`

2. **图片处理**
   - 上传前适当压缩图片
   - 保持图片比例一致
   - 重要产品准备多角度图片

3. **描述撰写**
   - 开头写最吸引人的卖点
   - 使用项目列表展示产品特点
   - 包含必要的规格信息

### 工作流程

1. **每日更新流程**
   ```
   早上 9:00 → 检查待上新产品
   9:00-10:00 → 编辑产品信息
   10:00-10:30 → 统一预览检查
   10:30 → 批量发布
   11:00 → 验证网站更新
   ```

2. **内容备份**
   - 重要促销文案先在文档中编辑
   - 定期导出产品数据备份
   - 保留原始图片文件

3. **团队协作**
   - 使用草稿功能进行内容准备
   - 发布前相互检查
   - 建立内容更新日志

## 联系技术支持

遇到无法解决的问题时：

1. **记录问题信息**
   - 操作步骤
   - 错误提示截图
   - 发生时间

2. **联系方式**
   - 技术支持邮箱：[预留]
   - 紧急联系电话：[预留]
   - 内部沟通群：[预留]

---

*本指南会根据系统更新定期维护，请关注最新版本。*