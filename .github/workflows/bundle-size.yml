name: Bundle Size Check

on:
  pull_request:
    branches: [main, development]
  push:
    branches: [main]

jobs:
  bundle-size:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Build and check bundle size
        run: npm run build:check
        env:
          NEXT_PUBLIC_SANITY_PROJECT_ID: ${{ secrets.NEXT_PUBLIC_SANITY_PROJECT_ID }}
          NEXT_PUBLIC_SANITY_DATASET: ${{ secrets.NEXT_PUBLIC_SANITY_DATASET }}
      
      - name: Upload bundle analysis
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: bundle-analysis
          path: .next/analyze/*.html
          retention-days: 7
      
      - name: Comment PR with bundle size
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const exec = require('child_process').execSync;
            
            try {
              const output = exec('npm run bundle-size', { encoding: 'utf-8' });
              const comment = `## 📦 Bundle Size Report\n\n\`\`\`\n${output}\n\`\`\``;
              
              github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: comment
              });
            } catch (error) {
              console.error('Bundle size check failed:', error.message);
            }