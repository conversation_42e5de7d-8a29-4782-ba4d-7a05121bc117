# Production Environment Configuration Template
# Copy this file to .env.production for production deployments

# Sanity CMS Configuration
NEXT_PUBLIC_SANITY_PROJECT_ID=4za4x22i
NEXT_PUBLIC_SANITY_DATASET=production
SANITY_API_TOKEN=your-production-api-token-here

# Webhook Security
SANITY_WEBHOOK_SECRET=your-production-webhook-secret

# Service Worker Configuration
# In production, Service Worker is enabled by default
# Set to false only if you want to disable it explicitly
NEXT_PUBLIC_ENABLE_SW=true

# Production Base URL
NEXT_PUBLIC_BASE_URL=https://your-domain.com

# Platform-specific configurations
# For Vercel deployment - these are automatically set
# VERCEL=1
# VERCEL_URL=your-app.vercel.app

# For Netlify deployment - these are automatically set  
# NETLIFY=true
# URL=https://your-app.netlify.app

# ⚠️ DO NOT SET PROXY VARIABLES IN PRODUCTION
# Proxy settings should only be used in development
# Production deployments (Vercel, Netlify, etc.) handle routing automatically

# Security Headers (if needed for custom server deployment)
# NEXT_PUBLIC_CSP_NONCE=your-csp-nonce
# NEXT_PUBLIC_SECURITY_HEADERS=true