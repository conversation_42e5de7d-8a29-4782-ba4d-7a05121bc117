# Playwright E2E 测试指南

本项目使用 Playwright 进行端到端测试，模拟真实用户操作来验证系统功能。

## 测试架构

### 目录结构
```
tests/
├── e2e/
│   ├── fixtures/
│   │   ├── test-data.ts       # 测试数据
│   │   └── page-objects/      # 页面对象模型
│   │       ├── base.page.ts   # 基础页面类
│   │       ├── home.page.ts   # 主页
│   │       ├── products.page.ts # 产品页
│   │       └── sanity-studio.page.ts # Sanity Studio
│   ├── sanity/
│   │   ├── studio.spec.ts     # Sanity Studio 测试
│   │   └── schema.spec.ts     # Schema 验证测试
│   ├── main-site/
│   │   ├── homepage.spec.ts   # 主页测试
│   │   └── products.spec.ts   # 产品页测试
│   └── api/
│       └── endpoints.spec.ts  # API 端点测试
└── utils/
    ├── error-detector.ts      # 错误检测工具
    └── sanity-validator.ts    # Sanity 数据验证器
```

## 运行测试

### 安装依赖
```bash
npm install
```

### 运行所有测试
```bash
npm run test:e2e
```

### 运行特定测试
```bash
# 只运行 Sanity Studio 测试
npm run test:sanity

# 只运行主站点测试
npm run test:main

# 只运行 API 测试
npm run test:api

# 使用 UI 模式（交互式）
npm run test:e2e:ui

# 调试模式
npm run test:e2e:debug
```

### 运行单个测试文件
```bash
npx playwright test tests/e2e/main-site/homepage.spec.ts
```

## 测试覆盖范围

### 1. Sanity Studio 测试
- ✅ Studio 加载和响应性
- ✅ 数据集切换（production/staging）
- ✅ 所有内容管理部分的可访问性
- ✅ 文档编辑器功能（只读）
- ✅ 多语言字段验证
- ✅ Schema 字段完整性检查

### 2. 主站点测试
- ✅ 多语言路由（zh/en/ar）
- ✅ 页面加载和响应性
- ✅ 核心组件存在性验证
- ✅ 产品列表和详情页
- ✅ 搜索功能
- ✅ 图片加载状态
- ✅ 响应式设计（桌面/平板/手机）

### 3. API 测试
- ✅ 搜索 API 功能和性能
- ✅ 产品筛选 API
- ✅ 分页功能
- ✅ 错误处理
- ✅ 响应时间验证

### 4. 错误检测
- ✅ 控制台错误监控
- ✅ 网络请求失败检测
- ✅ 页面崩溃检测
- ✅ Schema 不匹配检测
- ✅ 废弃字段使用警告

## 错误检测机制

### ErrorDetector 类
自动捕获以下类型的错误：
- **控制台错误**: JavaScript 错误、警告
- **网络错误**: 失败的 HTTP 请求
- **页面错误**: 页面崩溃或无响应
- **Schema 错误**: 数据结构不匹配

### 使用示例
```typescript
const errorDetector = new ErrorDetector(page);
await errorDetector.startMonitoring();
// ... 执行测试操作 ...
await errorDetector.stopMonitoring();

if (errorDetector.hasErrors()) {
  console.error(errorDetector.getErrorReport());
}
```

## 页面对象模型

采用页面对象模式提高测试的可维护性：

```typescript
// 所有页面继承 BasePage
export class HomePage extends BasePage {
  async goto(locale: 'zh' | 'en' | 'ar' = 'zh') {
    await this.page.goto(`/${locale}`);
    await this.waitForPageLoad();
  }
  
  async getHeroTitle(): Promise<string | null> {
    const heroTitle = this.page.locator('h1').first();
    return await heroTitle.textContent();
  }
}
```

## 配置说明

### playwright.config.ts
- **并行执行**: 默认启用
- **重试机制**: CI 环境下失败重试 2 次
- **截图/视频**: 失败时自动保存
- **超时设置**: 
  - 操作超时: 10 秒
  - 导航超时: 30 秒
- **测试服务器**: 自动启动开发服务器

### 环境配置
测试会自动启动以下服务：
1. Next.js 开发服务器 (3000 端口)
2. Sanity Studio (3333 端口)

## 最佳实践

### 1. 使用数据属性
在组件中添加 `data-testid` 属性：
```tsx
<div data-testid="product-card">
  <h3 data-testid="product-name">{product.name}</h3>
  <span data-testid="product-price">{product.price}</span>
</div>
```

### 2. 等待策略
```typescript
// 等待网络空闲
await page.waitForLoadState('networkidle');

// 等待特定元素
await page.waitForSelector('[data-testid="product-list"]');

// 使用合理的超时
await page.waitForTimeout(1000); // 仅在必要时使用
```

### 3. 断言最佳实践
```typescript
// 使用具体的断言
expect(products.length).toBeGreaterThan(0);

// 而不是模糊的断言
expect(products).toBeTruthy();
```

### 4. 错误处理
```typescript
try {
  await page.click('button');
} catch (error) {
  // 记录具体错误信息
  console.error(`点击按钮失败: ${error.message}`);
  // 可以选择跳过测试或标记为已知问题
  test.skip();
}
```

## CI/CD 集成

### GitHub Actions 示例
```yaml
name: E2E Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm ci
      - run: npx playwright install --with-deps
      - run: npm run test:e2e
      - uses: actions/upload-artifact@v3
        if: failure()
        with:
          name: test-results
          path: test-results/
```

## 故障排除

### 常见问题

1. **测试超时**
   - 增加超时时间: `test.setTimeout(60000)`
   - 检查网络连接
   - 确保服务器正常启动

2. **元素未找到**
   - 使用正确的选择器
   - 添加适当的等待
   - 检查页面是否正确加载

3. **Sanity 连接失败**
   - 检查环境变量配置
   - 确保 Sanity Studio 运行中
   - 验证项目 ID 和数据集

4. **并发测试失败**
   - 使用 `test.describe.serial()` 强制串行执行
   - 检查测试间的数据依赖

## 测试报告

测试完成后会生成以下报告：
- HTML 报告: `playwright-report/index.html`
- JSON 报告: `test-results/results.json`
- 失败截图: `test-results/screenshots/`
- 失败视频: `test-results/videos/`

查看 HTML 报告：
```bash
npx playwright show-report
```

## 扩展测试

### 添加新的页面测试
1. 在 `page-objects/` 创建新的页面类
2. 继承 `BasePage` 基类
3. 实现页面特定的方法
4. 在对应目录创建测试规范文件

### 添加新的错误类型
1. 在 `error-detector.ts` 中定义新的错误接口
2. 实现错误捕获逻辑
3. 更新错误报告生成方法

### 集成其他测试工具
- 性能测试: 使用 Lighthouse
- 可访问性测试: 使用 axe-core
- 视觉回归测试: 使用 Percy 或 Chromatic