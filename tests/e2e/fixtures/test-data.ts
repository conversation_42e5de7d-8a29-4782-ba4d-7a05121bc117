/**
 * 测试数据
 * 用于 E2E 测试的固定数据
 */

export const TEST_LOCALES = ['zh', 'en', 'ar'] as const;

export const TEST_PRODUCTS = {
  validProduct: {
    name: {
      zh: '测试产品',
      en: 'Test Product',
      ar: 'منتج الاختبار'
    },
    slug: 'test-product',
    price: 199.99,
    currency: 'CNY'
  },
  invalidProduct: {
    name: '缺少多语言字段的产品',
    // 缺少 slug
    price: -100 // 无效价格
  }
};

export const TEST_CATEGORIES = {
  figures: {
    name: {
      zh: '手办',
      en: 'Figures',
      ar: 'تماثيل'
    },
    slug: { current: 'figures' }
  },
  accessories: {
    name: {
      zh: '配饰',
      en: 'Accessories',
      ar: 'إكسسوارات'
    },
    slug: { current: 'accessories' }
  }
};

export const TEST_SEARCH_QUERIES = {
  valid: ['测试', 'test', 'اختبار', '手办', 'figure'],
  special: ['@#$%', '&test', '"quoted"', '中文 English'],
  empty: ['', '   ', '\n\t']
};

export const TEST_URLS = {
  homepage: {
    zh: '/zh',
    en: '/en',
    ar: '/ar'
  },
  products: {
    zh: '/zh/products',
    en: '/en/products',
    ar: '/ar/products'
  },
  filter: {
    zh: '/zh/products/filter',
    en: '/en/products/filter',
    ar: '/ar/products/filter'
  },
  about: {
    zh: '/zh/about',
    en: '/en/about',
    ar: '/ar/about'
  },
  contact: {
    zh: '/zh/contact',
    en: '/en/contact',
    ar: '/ar/contact'
  }
};

export const EXPECTED_RESPONSE_TIMES = {
  page: 5000, // 页面加载最大时间 (ms)
  api: 2000,  // API 响应最大时间 (ms)
  search: 1000, // 搜索响应最大时间 (ms)
  image: 3000 // 图片加载最大时间 (ms)
};

export const VIEWPORT_SIZES = {
  desktop: { width: 1920, height: 1080 },
  laptop: { width: 1366, height: 768 },
  tablet: { width: 768, height: 1024 },
  mobile: { width: 375, height: 667 }
};

export const ERROR_MESSAGES = {
  zh: {
    notFound: '页面未找到',
    serverError: '服务器错误',
    networkError: '网络连接失败'
  },
  en: {
    notFound: 'Page not found',
    serverError: 'Server error',
    networkError: 'Network connection failed'
  },
  ar: {
    notFound: 'الصفحة غير موجودة',
    serverError: 'خطأ في الخادم',
    networkError: 'فشل الاتصال بالشبكة'
  }
};