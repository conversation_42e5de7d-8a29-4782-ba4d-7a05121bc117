import { Page } from '@playwright/test';
import { BasePage } from './base.page';

/**
 * 产品列表页面对象
 */
export class ProductsPage extends BasePage {
  constructor(page: Page) {
    super(page);
  }

  /**
   * 导航到产品列表页
   */
  async goto(locale: 'zh' | 'en' | 'ar' = 'zh') {
    await this.page.goto(`/${locale}/products`);
    await this.waitForPageLoad();
  }

  /**
   * 获取产品列表
   */
  async getProductCards() {
    return this.page.locator('[data-testid="product-card"]').all();
  }

  /**
   * 获取产品数量
   */
  async getProductCount(): Promise<number> {
    const products = await this.getProductCards();
    return products.length;
  }

  /**
   * 点击第一个产品
   */
  async clickFirstProduct() {
    const firstProduct = this.page.locator('[data-testid="product-card"]').first();
    
    // Wait for the product to be fully visible
    await firstProduct.waitFor({ state: 'visible' });
    
    // Scroll the product into view and wait a bit
    await firstProduct.scrollIntoViewIfNeeded();
    await this.page.waitForTimeout(500);
    
    // Click on the link inside the product card
    const link = firstProduct.locator('a').first();
    await link.click({ force: true });
    
    // Wait for navigation to product detail page
    await this.page.waitForURL(/\/products\/[^\/]+$/, { timeout: 10000 });
    await this.waitForPageLoad();
  }

  /**
   * 按索引点击产品
   */
  async clickProductByIndex(index: number) {
    const product = this.page.locator('[data-testid="product-card"]').nth(index);
    await product.click();
    await this.waitForPageLoad();
  }

  /**
   * 检查筛选按钮是否存在
   */
  async hasFilterButton(): Promise<boolean> {
    return await this.isElementVisible('[data-testid="filter-button"]');
  }

  /**
   * 点击筛选按钮
   */
  async clickFilterButton() {
    const filterButton = this.page.locator('[data-testid="filter-button"]');
    await filterButton.click();
    await this.waitForPageLoad();
  }

  /**
   * 检查搜索框是否存在
   */
  async hasSearchBox(): Promise<boolean> {
    return await this.isElementVisible('[data-testid="search-box"]');
  }

  /**
   * 在搜索框中输入文本
   */
  async searchProducts(query: string) {
    const searchBox = this.page.locator('[data-testid="search-box"] input');
    await searchBox.fill(query);
    await searchBox.press('Enter');
    await this.page.waitForTimeout(1000); // 等待搜索结果
  }

  /**
   * 获取产品价格列表
   */
  async getProductPrices(): Promise<string[]> {
    const prices = await this.page.locator('[data-testid="product-price"]').allTextContents();
    return prices;
  }

  /**
   * 获取产品名称列表
   */
  async getProductNames(): Promise<string[]> {
    const names = await this.page.locator('[data-testid="product-name"]').allTextContents();
    return names;
  }

  /**
   * 检查是否有加载更多按钮
   */
  async hasLoadMoreButton(): Promise<boolean> {
    return await this.isElementVisible('[data-testid="load-more"]');
  }

  /**
   * 点击加载更多
   */
  async clickLoadMore() {
    const loadMoreButton = this.page.locator('[data-testid="load-more"]');
    await loadMoreButton.click();
    await this.page.waitForTimeout(1000); // 等待新产品加载
  }

  /**
   * 检查是否显示无产品信息
   */
  async hasNoProductsMessage(): Promise<boolean> {
    return await this.isElementVisible('[data-testid="no-products"]');
  }

  /**
   * 检查产品卡片的完整性
   */
  async checkProductCardIntegrity(index: number = 0): Promise<{
    hasImage: boolean;
    hasName: boolean;
    hasPrice: boolean;
    hasLink: boolean;
  }> {
    const card = this.page.locator('[data-testid="product-card"]').nth(index);
    
    return {
      hasImage: await card.locator('img').isVisible(),
      hasName: await card.locator('[data-testid="product-name"]').isVisible(),
      hasPrice: await card.locator('[data-testid="product-price"]').isVisible(),
      hasLink: await card.locator('a').isVisible(),
    };
  }
}