import { Page } from '@playwright/test';
import { BasePage } from './base.page';

/**
 * Sanity Studio 页面对象
 */
export class SanityStudioPage extends BasePage {
  constructor(page: Page) {
    super(page);
  }

  /**
   * 导航到 Sanity Studio
   */
  async goto(dataset: 'production' | 'staging' = 'production') {
    // 设置请求拦截以避免代理问题
    await this.page.route('**/*', route => {
      const request = route.request();
      const url = request.url();
      
      // 对localhost请求绕过代理
      if (url.includes('localhost:3333')) {
        route.continue();
      } else {
        route.continue();
      }
    });
    
    await this.page.goto(`http://localhost:3333?dataset=${dataset}`, {
      waitUntil: 'domcontentloaded',
      timeout: 60000
    });
    await this.waitForStudioLoad();
  }

  /**
   * 等待 Studio 加载完成
   */
  async waitForStudioLoad() {
    try {
      // 首先等待页面基本加载
      await this.page.waitForLoadState('domcontentloaded', { timeout: 30000 });
      
      // 检查是否有错误页面
      const pageContent = await this.page.content();
      if (pageContent.includes('Unable to connect')) {
        throw new Error('Sanity Studio 无法连接');
      }
      
      // Sanity Studio 使用 React，等待 React 根元素
      // 尝试多个可能的选择器
      const selectors = [
        '#sanity',
        '[data-sanity]',
        '[data-ui="RootBox"]',
        'div[data-testid="studio-root"]',
        // Sanity v3 可能的选择器
        'div[data-ui="Box"]:has(button)',
        'main',
        '[role="main"]'
      ];
      
      let loaded = false;
      for (const selector of selectors) {
        try {
          const element = await this.page.waitForSelector(selector, { timeout: 5000 });
          if (element) {
            loaded = true;
            break;
          }
        } catch {
          // 继续尝试下一个选择器
        }
      }
      
      if (!loaded) {
        // 如果所有选择器都失败，检查页面是否至少有内容
        const hasContent = await this.page.evaluate(() => {
          const textContent = document.body.textContent;
          return textContent ? textContent.trim().length > 0 : false;
        });
        if (!hasContent) {
          throw new Error('Sanity Studio 页面无内容');
        }
        // 等待网络空闲
        await this.page.waitForLoadState('networkidle', { timeout: 10000 });
      }
      
      // 额外等待以确保 Studio 完全加载
      await this.page.waitForTimeout(2000);
    } catch (error) {
      console.error('Studio 加载失败:', error);
      // 获取页面内容用于调试
      const content = await this.page.content();
      console.error('页面内容预览:', content.substring(0, 200));
      // 即使加载失败也继续，让测试能够报告具体问题
    }
  }

  /**
   * 检查是否显示数据集指示器
   */
  async hasDatasetIndicator(): Promise<boolean> {
    return await this.isElementVisible('button:has-text("当前数据集")');
  }

  /**
   * 获取当前数据集名称
   */
  async getCurrentDataset(): Promise<string | null> {
    const indicator = this.page.locator('button:has-text("当前数据集")');
    if (await indicator.isVisible()) {
      const text = await indicator.textContent();
      return text?.match(/当前数据集: (\w+)/)?.[1] || null;
    }
    return null;
  }

  /**
   * 点击首页内容
   */
  async clickHomepageContent() {
    await this.page.click('button:has-text("首页内容")');
    await this.page.waitForTimeout(1000);
  }

  /**
   * 点击产品管理
   */
  async clickProductManagement() {
    await this.page.click('button:has-text("产品管理")');
    await this.page.waitForTimeout(1000);
  }

  /**
   * 点击页面与配置
   */
  async clickPagesAndConfig() {
    await this.page.click('button:has-text("页面与配置")');
    await this.page.waitForTimeout(1000);
  }

  /**
   * 检查产品列表是否加载
   */
  async hasProductList(): Promise<boolean> {
    return await this.isElementVisible('[data-testid="document-list"]');
  }

  /**
   * 获取文档列表项数量
   */
  async getDocumentCount(): Promise<number> {
    const documents = this.page.locator('[data-testid="document-list-item"]');
    return await documents.count();
  }

  /**
   * 点击第一个文档
   */
  async clickFirstDocument() {
    const firstDoc = this.page.locator('[data-testid="document-list-item"]').first();
    await firstDoc.click();
    await this.waitForDocumentLoad();
  }

  /**
   * 等待文档编辑器加载
   */
  async waitForDocumentLoad() {
    await this.page.waitForSelector('[data-testid="document-panel"]', { timeout: 30000 });
    await this.page.waitForTimeout(1000);
  }

  /**
   * 检查文档编辑器是否打开
   */
  async hasDocumentEditor(): Promise<boolean> {
    return await this.isElementVisible('[data-testid="document-panel"]');
  }

  /**
   * 获取文档字段列表
   */
  async getDocumentFields(): Promise<string[]> {
    const fields = await this.page.locator('[data-testid="field-wrapper"] [data-testid="field-title"]').allTextContents();
    return fields;
  }

  /**
   * 检查是否有保存按钮
   */
  async hasSaveButton(): Promise<boolean> {
    return await this.isElementVisible('button:has-text("Publish"), button:has-text("发布")');
  }

  /**
   * 检查是否有多语言字段
   */
  async hasLocalizedFields(): Promise<boolean> {
    const zhTab = await this.isElementVisible('button:has-text("中文")');
    const enTab = await this.isElementVisible('button:has-text("English")');
    const arTab = await this.isElementVisible('button:has-text("العربية")');
    
    return zhTab || enTab || arTab;
  }

  /**
   * 切换到分类管理
   */
  async navigateToCategoryManagement() {
    await this.clickProductManagement();
    await this.page.click('button:has-text("分类管理")');
    await this.page.waitForTimeout(1000);
  }

  /**
   * 切换到 IP 系列管理
   */
  async navigateToIPSeriesManagement() {
    await this.clickProductManagement();
    await this.page.click('button:has-text("IP系列管理")');
    await this.page.waitForTimeout(1000);
  }

  /**
   * 检查文档列表是否存在
   */
  async hasDocumentList(): Promise<boolean> {
    return await this.isElementVisible('[data-testid="document-list"]');
  }

  /**
   * 检查所有主要部分是否可访问
   */
  async checkAllSectionsAccessible(): Promise<{
    homepage: boolean;
    products: boolean;
    categories: boolean;
    ipSeries: boolean;
    pages: boolean;
  }> {
    const results = {
      homepage: false,
      products: false,
      categories: false,
      ipSeries: false,
      pages: false,
    };

    // 检查首页
    try {
      await this.clickHomepageContent();
      results.homepage = await this.hasDocumentEditor();
      await this.page.click('[aria-label="Close"]').catch(() => {});
    } catch {}

    // 检查产品
    try {
      await this.clickProductManagement();
      results.products = true;
    } catch {}

    // 检查分类
    try {
      await this.navigateToCategoryManagement();
      results.categories = await this.hasDocumentList();
    } catch {}

    // 检查 IP 系列
    try {
      await this.navigateToIPSeriesManagement();
      results.ipSeries = await this.hasDocumentList();
    } catch {}

    // 检查页面配置
    try {
      await this.clickPagesAndConfig();
      results.pages = true;
    } catch {}

    return results;
  }
}