import { Page } from '@playwright/test';
import { BasePage } from './base.page';

/**
 * 主页页面对象
 */
export class HomePage extends BasePage {
  constructor(page: Page) {
    super(page);
  }

  /**
   * 导航到主页
   */
  async goto(locale: 'zh' | 'en' | 'ar' = 'zh') {
    await this.page.goto(`/${locale}`);
    await this.waitForPageLoad();
  }

  /**
   * 获取英雄区标题
   */
  async getHeroTitle(): Promise<string | null> {
    // First try to find h1
    const h1Count = await this.page.locator('h1').count();
    if (h1Count > 0) {
      const heroTitle = this.page.locator('h1').first();
      return await heroTitle.textContent();
    }
    
    // If no h1, check for logo image alt text or other hero content
    const heroSection = this.page.locator('section').first();
    const spanText = await heroSection.locator('span').first().textContent();
    
    // Return the welcome text if found
    if (spanText) {
      return spanText;
    }
    
    return 'MyNgaPop'; // Default fallback
  }

  /**
   * 获取英雄区描述
   */
  async getHeroDescription(): Promise<string | null> {
    const heroDesc = this.page.locator('main p').first();
    return await heroDesc.textContent();
  }

  /**
   * 检查精选产品部分是否存在
   */
  async hasFeaturedProducts(): Promise<boolean> {
    return await this.isElementVisible('[data-testid="featured-products"]');
  }

  /**
   * 获取精选产品数量
   */
  async getFeaturedProductsCount(): Promise<number> {
    const products = this.page.locator('[data-testid="product-card"]');
    return await products.count();
  }

  /**
   * 检查导航栏是否存在
   */
  async hasNavigation(): Promise<boolean> {
    return await this.isElementVisible('nav');
  }

  /**
   * 检查页脚是否存在
   */
  async hasFooter(): Promise<boolean> {
    return await this.isElementVisible('footer');
  }

  /**
   * 点击查看所有产品按钮
   */
  async clickViewAllProducts() {
    const viewAllButton = this.page.locator('a:has-text("查看所有产品"), a:has-text("View All Products"), a:has-text("عرض جميع المنتجات")');
    await viewAllButton.click();
    await this.waitForPageLoad();
  }

  /**
   * 检查品牌故事部分
   */
  async hasBrandStory(): Promise<boolean> {
    return await this.isElementVisible('[data-testid="brand-story"]');
  }

  /**
   * 检查统计数据部分
   */
  async hasStats(): Promise<boolean> {
    return await this.isElementVisible('[data-testid="stats"]');
  }

  /**
   * 获取语言切换器
   */
  async getLanguageSwitcher() {
    return this.page.locator('[data-testid="language-switcher"]');
  }

  /**
   * 检查所有核心部分是否存在
   */
  async checkAllSectionsPresent(): Promise<{
    hero: boolean;
    featuredProducts: boolean;
    brandStory: boolean;
    stats: boolean;
    navigation: boolean;
    footer: boolean;
  }> {
    return {
      hero: await this.isElementVisible('section[aria-label="Hero section"]') || await this.isElementVisible('main section:first-child'),
      featuredProducts: await this.hasFeaturedProducts(),
      brandStory: await this.hasBrandStory(),
      stats: await this.hasStats(),
      navigation: await this.hasNavigation(),
      footer: await this.hasFooter(),
    };
  }
}