import { Page, Locator } from '@playwright/test';
import { ErrorDetector } from '../../../utils/error-detector';

/**
 * 基础页面对象类
 * 所有页面对象都应该继承此类
 */
export abstract class BasePage {
  protected page: Page;
  protected errorDetector: ErrorDetector;

  constructor(page: Page) {
    this.page = page;
    this.errorDetector = new ErrorDetector(page);
  }

  /**
   * 获取页面标题
   */
  async getTitle(): Promise<string> {
    return await this.page.title();
  }

  /**
   * 获取当前 URL
   */
  getUrl(): string {
    return this.page.url();
  }

  /**
   * 等待页面加载完成
   */
  async waitForPageLoad() {
    await this.page.waitForLoadState('networkidle');
    await this.page.waitForLoadState('domcontentloaded');
  }

  /**
   * 开始错误监控
   */
  async startErrorMonitoring() {
    await this.errorDetector.startMonitoring();
  }

  /**
   * 停止错误监控
   */
  async stopErrorMonitoring() {
    await this.errorDetector.stopMonitoring();
  }

  /**
   * 获取错误检测器
   */
  getErrorDetector(): ErrorDetector {
    return this.errorDetector;
  }

  /**
   * 检查页面是否有错误
   */
  hasErrors(): boolean {
    return this.errorDetector.hasErrors();
  }

  /**
   * 获取错误报告
   */
  getErrorReport(): string {
    return this.errorDetector.generateDetailedReport();
  }

  /**
   * 截图
   */
  async screenshot(fileName: string) {
    await this.page.screenshot({ 
      path: `test-results/screenshots/${fileName}`,
      fullPage: true 
    });
  }

  /**
   * 检查元素是否可见
   */
  async isElementVisible(selector: string): Promise<boolean> {
    try {
      const element = this.page.locator(selector);
      return await element.isVisible();
    } catch {
      return false;
    }
  }

  /**
   * 等待元素出现
   */
  async waitForElement(selector: string, timeout: number = 30000) {
    await this.page.waitForSelector(selector, { timeout });
  }

  /**
   * 切换语言
   */
  async switchLanguage(locale: 'zh' | 'en' | 'ar') {
    const currentUrl = this.page.url();
    const url = new URL(currentUrl);
    const pathParts = url.pathname.split('/');
    
    // 替换语言部分
    if (['zh', 'en', 'ar'].includes(pathParts[1])) {
      pathParts[1] = locale;
    } else {
      pathParts.splice(1, 0, locale);
    }
    
    url.pathname = pathParts.join('/');
    await this.page.goto(url.toString());
    await this.waitForPageLoad();
  }

  /**
   * 检查页面是否响应
   */
  async isPageResponsive(): Promise<boolean> {
    return !(await this.errorDetector.checkPageCrash());
  }

  /**
   * 获取页面加载时间
   */
  async getPageLoadTime(): Promise<number> {
    const performanceTiming = await this.page.evaluate(() => {
      const timing = performance.timing;
      return timing.loadEventEnd - timing.navigationStart;
    });
    return performanceTiming;
  }

  /**
   * 检查所有图片是否加载成功
   */
  async checkAllImagesLoaded(): Promise<{ total: number; loaded: number; failed: string[] }> {
    return await this.page.evaluate(() => {
      const images = Array.from(document.querySelectorAll('img'));
      const failed: string[] = [];
      let loaded = 0;

      images.forEach(img => {
        if (img.complete && img.naturalHeight !== 0) {
          loaded++;
        } else {
          failed.push(img.src || img.getAttribute('data-src') || 'unknown');
        }
      });

      return {
        total: images.length,
        loaded,
        failed
      };
    });
  }

  /**
   * 检查所有链接是否有效
   */
  async getAllLinks(): Promise<string[]> {
    return await this.page.evaluate(() => {
      return Array.from(document.querySelectorAll('a[href]'))
        .map(a => (a as HTMLAnchorElement).href)
        .filter(href => href && !href.startsWith('javascript:'));
    });
  }
}