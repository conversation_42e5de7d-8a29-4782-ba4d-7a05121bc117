import { test, expect } from '@playwright/test';
import { SanityValidator } from '../../utils/sanity-validator';

// 模拟 Sanity 客户端进行数据获取
async function fetchSanityDocuments(type?: string): Promise<any[]> {
  try {
    const query = type ? `*[_type == "${type}"]` : '*[_type in ["product", "category", "ipSeries", "homepage", "aboutPage", "contactPage"]]';
    
    // 这里应该使用实际的 Sanity 客户端
    // 暂时返回模拟数据用于测试框架演示
    return [];
  } catch (error) {
    console.error('获取 Sanity 文档失败:', error);
    return [];
  }
}

test.describe('Sanity Schema 验证测试', () => {
  let validator: SanityValidator;

  test.beforeEach(() => {
    validator = new SanityValidator();
  });

  test('产品文档应该符合 Schema 定义', async () => {
    const products = await fetchSanityDocuments('product');
    
    if (products.length === 0) {
      test.skip();
      return;
    }

    const errors = validator.validateDocuments(products);
    
    // 生成验证报告
    if (errors.length > 0) {
      console.log(validator.getErrorSummary());
      console.log(validator.generateDetailedReport());
    }
    
    // 期望没有 Schema 错误
    expect(errors.length).toBe(0);
  });

  test('分类文档应该符合 Schema 定义', async () => {
    const categories = await fetchSanityDocuments('category');
    
    if (categories.length === 0) {
      test.skip();
      return;
    }

    const errors = validator.validateDocuments(categories);
    
    // 验证每个分类都有必填字段
    categories.forEach(category => {
      expect(category).toHaveProperty('name');
      expect(category).toHaveProperty('slug');
      
      // 验证多语言字段
      if (category.name) {
        expect(category.name).toHaveProperty('zh');
        expect(category.name).toHaveProperty('en');
        expect(category.name).toHaveProperty('ar');
      }
    });
    
    expect(errors.length).toBe(0);
  });

  test('IP系列文档应该符合 Schema 定义', async () => {
    const ipSeries = await fetchSanityDocuments('ipSeries');
    
    if (ipSeries.length === 0) {
      test.skip();
      return;
    }

    const errors = validator.validateDocuments(ipSeries);
    
    // 验证每个IP系列都有必填字段
    ipSeries.forEach(ip => {
      expect(ip).toHaveProperty('name');
      expect(ip).toHaveProperty('slug');
      
      // 验证多语言字段
      if (ip.name) {
        expect(ip.name).toHaveProperty('zh');
        expect(ip.name).toHaveProperty('en');
        expect(ip.name).toHaveProperty('ar');
      }
    });
    
    expect(errors.length).toBe(0);
  });

  test('首页文档应该符合 Schema 定义', async () => {
    const homepage = await fetchSanityDocuments('homepage');
    
    if (homepage.length === 0) {
      test.skip();
      return;
    }

    const errors = validator.validateDocuments(homepage);
    
    // 首页应该只有一个文档
    expect(homepage.length).toBe(1);
    
    // 验证首页结构
    const homepageDoc = homepage[0];
    expect(homepageDoc).toHaveProperty('hero');
    expect(homepageDoc).toHaveProperty('features');
    
    expect(errors.length).toBe(0);
  });

  test('检查废弃字段使用情况', async () => {
    const allDocuments = await fetchSanityDocuments();
    
    if (allDocuments.length === 0) {
      test.skip();
      return;
    }

    // 检查是否有文档仍在使用 mainImage 字段
    const documentsWithMainImage = allDocuments.filter(doc => 'mainImage' in doc);
    
    if (documentsWithMainImage.length > 0) {
      console.warn(`发现 ${documentsWithMainImage.length} 个文档仍在使用废弃的 mainImage 字段`);
      documentsWithMainImage.forEach(doc => {
        console.warn(`- ${doc._type}: ${doc._id}`);
      });
    }
    
    // 期望没有文档使用废弃字段
    expect(documentsWithMainImage.length).toBe(0);
  });

  test('验证所有文档的多语言字段完整性', async () => {
    const allDocuments = await fetchSanityDocuments();
    
    if (allDocuments.length === 0) {
      test.skip();
      return;
    }

    const requiredLocales = ['zh', 'en', 'ar'];
    const incompleteFields: any[] = [];
    
    allDocuments.forEach(doc => {
      // 检查所有对象类型的字段
      Object.entries(doc).forEach(([key, value]) => {
        if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
          // 检查是否是多语言字段
          const hasAnyLocale = requiredLocales.some(locale => locale in value);
          if (hasAnyLocale) {
            const missingLocales = requiredLocales.filter(locale => !(locale in value));
            if (missingLocales.length > 0) {
              incompleteFields.push({
                documentId: doc._id,
                documentType: doc._type,
                field: key,
                missingLocales
              });
            }
          }
        }
      });
    });
    
    if (incompleteFields.length > 0) {
      console.log('发现不完整的多语言字段:');
      incompleteFields.forEach(item => {
        console.log(`- ${item.documentType} (${item.documentId}) - 字段 ${item.field} 缺少: ${item.missingLocales.join(', ')}`);
      });
    }
    
    expect(incompleteFields.length).toBe(0);
  });

  test('生成 Schema 验证总结报告', async () => {
    const allDocuments = await fetchSanityDocuments();
    
    if (allDocuments.length === 0) {
      console.log('没有找到任何文档进行验证');
      test.skip();
      return;
    }

    const errors = validator.validateDocuments(allDocuments);
    
    // 统计信息
    const stats = {
      totalDocuments: allDocuments.length,
      documentTypes: new Set(allDocuments.map(d => d._type)).size,
      totalErrors: errors.length,
      errorsByType: new Map<string, number>()
    };
    
    // 按文档类型分组
    const docsByType = new Map<string, any[]>();
    allDocuments.forEach(doc => {
      const list = docsByType.get(doc._type) || [];
      list.push(doc);
      docsByType.set(doc._type, list);
    });
    
    // 生成报告
    console.log('\n=== Schema 验证报告 ===');
    console.log(`总文档数: ${stats.totalDocuments}`);
    console.log(`文档类型数: ${stats.documentTypes}`);
    console.log(`总错误数: ${stats.totalErrors}`);
    
    console.log('\n按类型分布:');
    docsByType.forEach((docs, type) => {
      console.log(`- ${type}: ${docs.length} 个文档`);
    });
    
    if (errors.length > 0) {
      console.log('\n错误详情:');
      console.log(validator.generateDetailedReport());
    } else {
      console.log('\n✅ 所有文档都通过 Schema 验证');
    }
  });
});