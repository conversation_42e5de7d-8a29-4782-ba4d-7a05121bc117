import { test, expect } from '@playwright/test';
import { SanityStudioPage } from '../fixtures/page-objects/sanity-studio.page';

test.describe('Sanity Studio 测试', () => {
  let studioPage: SanityStudioPage;

  test.beforeEach(async ({ page }) => {
    studioPage = new SanityStudioPage(page);
    await studioPage.startErrorMonitoring();
  });

  test.afterEach(async () => {
    await studioPage.stopErrorMonitoring();
    
    // 检查并报告错误
    if (studioPage.hasErrors()) {
      console.error('检测到错误:');
      console.error(studioPage.getErrorReport());
    }
  });

  test('Sanity Studio 应该能够正常加载', async () => {
    await studioPage.goto();
    
    // 获取页面内容来调试
    const pageContent = await studioPage.page.content();
    const title = await studioPage.getTitle();
    
    // 检查页面是否响应
    const isResponsive = await studioPage.isPageResponsive();
    expect(isResponsive).toBe(true);
    
    // 检查是否显示登录页面或Studio
    const hasLoginPage = pageContent.includes('Choose login provider') || pageContent.includes('登录');
    const hasStudioContent = pageContent.includes('动漫周边品牌 CMS') || pageContent.includes('Sanity Studio');
    
    // 页面应该显示登录页面或Studio内容
    expect(hasLoginPage || hasStudioContent).toBe(true);
    
    // 不应该有连接错误
    expect(pageContent).not.toContain('Unable to connect');
    expect(pageContent).not.toContain('404');
    
    // 检查是否有错误
    expect(studioPage.hasErrors()).toBe(false);
  });

  test('应该显示正确的数据集指示器', async () => {
    // 测试生产环境
    await studioPage.goto('production');
    
    // 检查是否需要登录
    const pageContent = await studioPage.page.content();
    const needsLogin = pageContent.includes('Choose login provider') || 
                      pageContent.includes('登录') ||
                      pageContent.includes('Google') ||
                      pageContent.includes('GitHub') ||
                      pageContent.includes('E-mail / password');
    
    if (!needsLogin) {
      let dataset = await studioPage.getCurrentDataset();
      expect(['production', null]).toContain(dataset);
      
      // 测试预发布环境
      await studioPage.goto('staging');
      const hasIndicator = await studioPage.hasDatasetIndicator();
      if (hasIndicator) {
        dataset = await studioPage.getCurrentDataset();
        expect(dataset).toBe('staging');
      }
    } else {
      console.log('Sanity Studio 需要登录，跳过数据集指示器测试');
    }
  });

  test('所有主要内容部分应该可以访问（需要登录）', async () => {
    await studioPage.goto();
    
    // 等待页面加载
    await studioPage.page.waitForTimeout(2000);
    
    // 检查是否需要登录 - 更全面的检测
    const pageContent = await studioPage.page.content();
    const isLoginPage = pageContent.includes('Choose login provider') || 
                       pageContent.includes('登录') ||
                       pageContent.includes('Google') ||
                       pageContent.includes('GitHub') ||
                       pageContent.includes('E-mail / password');
    
    if (isLoginPage) {
      console.log('Sanity Studio 显示登录页面，这是预期的行为（只读测试）');
      expect(isLoginPage).toBe(true); // 确认显示登录页面
    } else {
      // 如果不是登录页面，才测试内容访问
      const sections = await studioPage.checkAllSectionsAccessible();
      
      // 验证所有部分都可以访问
      expect(sections.homepage).toBe(true);
      expect(sections.products).toBe(true);
      expect(sections.categories).toBe(true);
      expect(sections.ipSeries).toBe(true);
      expect(sections.pages).toBe(true);
    }
    
    // 确保没有控制台错误（除了网络错误）
    const consoleErrors = studioPage.getErrorDetector().getConsoleErrors();
    const nonNetworkErrors = consoleErrors.filter(err => !err.message.includes('net::'));
    expect(nonNetworkErrors.length).toBe(0);
  });

  test('首页内容编辑器应该正常工作（需要登录）', async () => {
    await studioPage.goto();
    
    // 检查是否需要登录
    const pageContent = await studioPage.page.content();
    const needsLogin = pageContent.includes('Choose login provider') || 
                      pageContent.includes('登录') ||
                      pageContent.includes('Google') ||
                      pageContent.includes('GitHub') ||
                      pageContent.includes('E-mail / password');
    
    if (needsLogin) {
      console.log('Sanity Studio 需要登录，无法测试编辑器功能');
      expect(needsLogin).toBe(true);
    } else {
      await studioPage.clickHomepageContent();
      
      // 检查编辑器是否打开
      const hasEditor = await studioPage.hasDocumentEditor();
      expect(hasEditor).toBe(true);
      
      // 检查是否有字段
      const fields = await studioPage.getDocumentFields();
      expect(fields.length).toBeGreaterThan(0);
      
      // 检查是否有保存按钮（但不点击）
      const hasSaveButton = await studioPage.hasSaveButton();
      expect(hasSaveButton).toBe(true);
    }
  });

  test('产品管理应该显示产品列表（需要登录）', async () => {
    await studioPage.goto();
    
    // 检查是否需要登录
    const pageContent = await studioPage.page.content();
    const needsLogin = pageContent.includes('Choose login provider') || 
                      pageContent.includes('登录') ||
                      pageContent.includes('Google') ||
                      pageContent.includes('GitHub') ||
                      pageContent.includes('E-mail / password');
    
    if (needsLogin) {
      console.log('Sanity Studio 需要登录，无法测试产品管理');
      expect(needsLogin).toBe(true);
    } else {
      await studioPage.clickProductManagement();
      
      // 等待一下让子菜单展开
      await studioPage.page.waitForTimeout(1000);
      
      // 检查是否有产品相关的选项
      const hasProductOptions = await studioPage.page.locator('button:has-text("分类管理")').isVisible();
      expect(hasProductOptions).toBe(true);
    }
  });

  test('应该能够访问产品编辑页面（需要登录）', async ({ page }) => {
    await studioPage.goto();
    
    // 检查是否需要登录
    const pageContent = await studioPage.page.content();
    const needsLogin = pageContent.includes('Choose login provider') || 
                      pageContent.includes('登录') ||
                      pageContent.includes('Google') ||
                      pageContent.includes('GitHub') ||
                      pageContent.includes('E-mail / password');
    
    if (needsLogin) {
      console.log('Sanity Studio 需要登录，无法测试产品编辑页面');
      expect(needsLogin).toBe(true);
    } else {
      await studioPage.clickProductManagement();
      
      // 点击 "全部产品" 或类似的选项来查看产品列表
      try {
        await page.click('button:has-text("全部产品")');
      } catch {
        // 如果没有 "全部产品"，尝试其他方式
        await page.click('[data-testid="document-list-item"]').catch(() => {});
      }
      
      await page.waitForTimeout(2000);
      
      // 检查是否有文档列表
      const hasDocuments = await studioPage.hasProductList();
      if (hasDocuments) {
        const docCount = await studioPage.getDocumentCount();
        expect(docCount).toBeGreaterThanOrEqual(0);
        
        // 如果有文档，尝试打开第一个
        if (docCount > 0) {
          await studioPage.clickFirstDocument();
          const hasEditor = await studioPage.hasDocumentEditor();
          expect(hasEditor).toBe(true);
          
          // 检查是否有多语言字段
          const hasLocalized = await studioPage.hasLocalizedFields();
          expect(hasLocalized).toBe(true);
        }
      }
    }
  });

  test('分类管理应该可以访问（需要登录）', async () => {
    await studioPage.goto();
    
    // 检查是否需要登录
    const pageContent = await studioPage.page.content();
    const needsLogin = pageContent.includes('Choose login provider') || 
                      pageContent.includes('登录') ||
                      pageContent.includes('Google') ||
                      pageContent.includes('GitHub') ||
                      pageContent.includes('E-mail / password');
    
    if (needsLogin) {
      console.log('Sanity Studio 需要登录，无法测试分类管理');
      expect(needsLogin).toBe(true);
    } else {
      await studioPage.navigateToCategoryManagement();
      
      // 检查是否显示了分类列表或相关内容
      const hasContent = await studioPage.hasProductList();
      expect(hasContent).toBe(true);
      
      // 检查页面是否正常响应
      const isResponsive = await studioPage.isPageResponsive();
      expect(isResponsive).toBe(true);
    }
  });

  test('IP系列管理应该可以访问（需要登录）', async () => {
    await studioPage.goto();
    
    // 检查是否需要登录
    const pageContent = await studioPage.page.content();
    const needsLogin = pageContent.includes('Choose login provider') || 
                      pageContent.includes('登录') ||
                      pageContent.includes('Google') ||
                      pageContent.includes('GitHub') ||
                      pageContent.includes('E-mail / password');
    
    if (needsLogin) {
      console.log('Sanity Studio 需要登录，无法测试IP系列管理');
      expect(needsLogin).toBe(true);
    } else {
      await studioPage.navigateToIPSeriesManagement();
      
      // 检查是否显示了IP系列列表或相关内容
      const hasContent = await studioPage.hasProductList();
      expect(hasContent).toBe(true);
      
      // 检查页面是否正常响应
      const isResponsive = await studioPage.isPageResponsive();
      expect(isResponsive).toBe(true);
    }
  });

  test('页面加载性能应该在合理范围内', async () => {
    const startTime = Date.now();
    await studioPage.goto();
    const loadTime = Date.now() - startTime;
    
    // Studio 加载时间应该在 30 秒内
    expect(loadTime).toBeLessThan(30000);
    
    // 获取更详细的性能指标
    const pageLoadTime = await studioPage.getPageLoadTime();
    console.log(`页面加载时间: ${pageLoadTime}ms`);
    
    // 检查是否有性能相关的控制台警告
    const consoleErrors = studioPage.getErrorDetector().getConsoleErrors();
    const performanceWarnings = consoleErrors.filter(err => 
      err.message.toLowerCase().includes('performance') ||
      err.message.toLowerCase().includes('slow')
    );
    expect(performanceWarnings.length).toBe(0);
  });
});