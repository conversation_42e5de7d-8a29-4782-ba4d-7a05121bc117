import { test, expect } from '@playwright/test';
import { ProductsPage } from '../fixtures/page-objects/products.page';

test.describe('产品页面测试', () => {
  let productsPage: ProductsPage;

  test.beforeEach(async ({ page }) => {
    productsPage = new ProductsPage(page);
    await productsPage.startErrorMonitoring();
  });

  test.afterEach(async () => {
    await productsPage.stopErrorMonitoring();
    
    if (productsPage.hasErrors()) {
      console.error('页面错误:');
      console.error(productsPage.getErrorReport());
    }
  });

  test('产品列表页应该正常加载', async () => {
    await productsPage.goto();
    
    // 检查页面标题
    const title = await productsPage.getTitle();
    expect(title).toBeTruthy();
    
    // 检查页面是否响应
    const isResponsive = await productsPage.isPageResponsive();
    expect(isResponsive).toBe(true);
    
    // 检查是否有产品显示
    const productCount = await productsPage.getProductCount();
    console.log(`找到 ${productCount} 个产品`);
    
    if (productCount === 0) {
      // 如果没有产品，应该显示空状态消息
      const hasNoProductsMsg = await productsPage.hasNoProductsMessage();
      expect(hasNoProductsMsg).toBe(true);
    } else {
      expect(productCount).toBeGreaterThan(0);
    }
  });

  test('产品卡片应该包含所有必要信息', async () => {
    await productsPage.goto();
    
    const productCount = await productsPage.getProductCount();
    
    if (productCount > 0) {
      // 检查第一个产品卡片
      const cardIntegrity = await productsPage.checkProductCardIntegrity(0);
      
      expect(cardIntegrity.hasImage).toBe(true);
      expect(cardIntegrity.hasName).toBe(true);
      expect(cardIntegrity.hasPrice).toBe(true);
      expect(cardIntegrity.hasLink).toBe(true);
    } else {
      test.skip();
    }
  });

  test('点击产品应该导航到产品详情页', async () => {
    await productsPage.goto();
    
    const productCount = await productsPage.getProductCount();
    
    if (productCount > 0) {
      // 获取第一个产品的名称
      const productNames = await productsPage.getProductNames();
      const firstProductName = productNames[0];
      
      // 点击第一个产品
      await productsPage.clickFirstProduct();
      
      // 检查是否导航到了产品详情页
      const url = productsPage.getUrl();
      expect(url).toContain('/products/');
      
      // 页面应该正常加载
      const isResponsive = await productsPage.isPageResponsive();
      expect(isResponsive).toBe(true);
      
      // 检查是否有控制台错误
      const consoleErrors = productsPage.getErrorDetector().getConsoleErrors();
      expect(consoleErrors.length).toBe(0);
    } else {
      test.skip();
    }
  });

  test('筛选功能应该可用', async () => {
    await productsPage.goto();
    
    const hasFilter = await productsPage.hasFilterButton();
    
    if (hasFilter) {
      await productsPage.clickFilterButton();
      
      // 等待导航完成
      await productsPage.page.waitForLoadState('networkidle');
      
      // 检查是否导航到筛选页面
      const url = productsPage.getUrl();
      expect(url).toContain('/filter');
      
      // 页面应该正常加载
      const isResponsive = await productsPage.isPageResponsive();
      expect(isResponsive).toBe(true);
    } else {
      console.log('产品页面没有筛选按钮');
    }
  });

  test('搜索功能测试', async () => {
    await productsPage.goto();
    
    const hasSearch = await productsPage.hasSearchBox();
    
    if (hasSearch) {
      // 执行搜索
      await productsPage.searchProducts('test');
      
      // 等待搜索结果
      await productsPage.page.waitForTimeout(2000);
      
      // 检查页面是否仍然正常
      const isResponsive = await productsPage.isPageResponsive();
      expect(isResponsive).toBe(true);
      
      // 检查是否有网络错误
      const networkErrors = productsPage.getErrorDetector().getNetworkErrors();
      const searchErrors = networkErrors.filter(err => err.url.includes('/api/search'));
      expect(searchErrors.length).toBe(0);
    } else {
      console.log('产品页面没有搜索框');
    }
  });

  test('多语言产品页面测试', async () => {
    const locales = ['zh', 'en', 'ar'] as const;
    
    for (const locale of locales) {
      await productsPage.goto(locale);
      
      // 检查页面是否正常加载
      const isResponsive = await productsPage.isPageResponsive();
      expect(isResponsive).toBe(true);
      
      // 检查是否有产品显示
      const productCount = await productsPage.getProductCount();
      console.log(`${locale} 语言版本有 ${productCount} 个产品`);
      
      // 检查没有控制台错误
      const consoleErrors = productsPage.getErrorDetector().getConsoleErrors();
      expect(consoleErrors.length).toBe(0);
    }
  });

  test('产品图片加载测试', async () => {
    await productsPage.goto();
    
    // 等待图片加载
    await productsPage.page.waitForTimeout(3000);
    
    const imageStatus = await productsPage.checkAllImagesLoaded();
    
    console.log(`产品图片加载状态: ${imageStatus.loaded}/${imageStatus.total}`);
    
    if (imageStatus.failed.length > 0) {
      console.log('加载失败的图片:', imageStatus.failed.slice(0, 5)); // 只显示前5个
    }
    
    // 至少 80% 的图片应该成功加载
    const successRate = imageStatus.total > 0 ? imageStatus.loaded / imageStatus.total : 1;
    expect(successRate).toBeGreaterThan(0.8);
  });

  test('加载更多功能测试', async () => {
    await productsPage.goto();
    
    const hasLoadMore = await productsPage.hasLoadMoreButton();
    
    if (hasLoadMore) {
      const initialCount = await productsPage.getProductCount();
      
      // 点击加载更多
      await productsPage.clickLoadMore();
      
      // 检查产品数量是否增加
      const newCount = await productsPage.getProductCount();
      expect(newCount).toBeGreaterThan(initialCount);
      
      console.log(`加载更多: ${initialCount} -> ${newCount} 个产品`);
    } else {
      console.log('没有加载更多按钮，可能所有产品都已显示');
    }
  });

  test('产品价格显示测试', async () => {
    await productsPage.goto();
    
    const prices = await productsPage.getProductPrices();
    
    if (prices.length > 0) {
      // 检查价格格式
      prices.forEach(price => {
        // 价格应该包含货币符号
        expect(price).toMatch(/[¥$€￥]/);
        
        // 价格应该包含数字
        expect(price).toMatch(/\d+/);
      });
      
      console.log(`检查了 ${prices.length} 个产品价格`);
    } else {
      console.log('没有找到产品价格信息');
    }
  });

  test('页面性能测试', async () => {
    const startTime = Date.now();
    await productsPage.goto();
    const loadTime = Date.now() - startTime;
    
    console.log(`产品页面加载时间: ${loadTime}ms`);
    
    // 页面应该在 10 秒内加载完成（开发环境可能较慢）
    expect(loadTime).toBeLessThan(10000);
    
    // 检查是否有性能相关的警告
    const consoleErrors = productsPage.getErrorDetector().getConsoleErrors();
    const performanceWarnings = consoleErrors.filter(err => 
      err.message.toLowerCase().includes('performance') ||
      err.message.toLowerCase().includes('slow')
    );
    
    if (performanceWarnings.length > 0) {
      console.log('性能警告:', performanceWarnings);
    }
    
    expect(performanceWarnings.length).toBe(0);
  });
});