import { test, expect } from '@playwright/test';
import { HomePage } from '../fixtures/page-objects/home.page';

test.describe('主页测试', () => {
  let homePage: HomePage;

  test.beforeEach(async ({ page }) => {
    homePage = new HomePage(page);
    await homePage.startErrorMonitoring();
  });

  test.afterEach(async () => {
    await homePage.stopErrorMonitoring();
    
    if (homePage.hasErrors()) {
      console.error('页面错误:');
      console.error(homePage.getErrorReport());
    }
  });

  test.describe('多语言测试', () => {
    const locales = ['zh', 'en', 'ar'] as const;

    locales.forEach(locale => {
      test(`${locale} 语言版本应该正常加载`, async () => {
        await homePage.goto(locale);
        
        // 检查页面是否响应
        const isResponsive = await homePage.isPageResponsive();
        expect(isResponsive).toBe(true);
        
        // 检查标题
        const title = await homePage.getTitle();
        expect(title).toBeTruthy();
        
        // 检查是否有主要标题
        const heroTitle = await homePage.getHeroTitle();
        expect(heroTitle).toBeTruthy();
        
        // 检查没有控制台错误
        const consoleErrors = homePage.getErrorDetector().getConsoleErrors();
        expect(consoleErrors.length).toBe(0);
      });
    });
  });

  test('所有核心部分应该存在', async () => {
    await homePage.goto();
    
    const sections = await homePage.checkAllSectionsPresent();
    
    // 验证所有部分
    expect(sections.hero).toBe(true);
    expect(sections.navigation).toBe(true);
    expect(sections.footer).toBe(true);
    
    // 其他部分可能是可选的，但如果存在应该正常显示
    console.log('页面部分检查结果:', sections);
  });

  test('精选产品部分应该正常显示', async () => {
    await homePage.goto();
    
    const hasFeatured = await homePage.hasFeaturedProducts();
    
    if (hasFeatured) {
      const productCount = await homePage.getFeaturedProductsCount();
      console.log(`找到 ${productCount} 个精选产品`);
      
      // 如果有精选产品部分，至少应该有一个产品
      expect(productCount).toBeGreaterThan(0);
    } else {
      console.log('主页没有精选产品部分');
    }
  });

  test('导航到产品页面应该正常工作', async () => {
    await homePage.goto();
    
    // 查找并点击查看所有产品按钮
    try {
      await homePage.clickViewAllProducts();
      
      // 检查是否导航到了产品页面
      const url = homePage.getUrl();
      expect(url).toContain('/products');
      
      // 检查页面是否正常加载
      const isResponsive = await homePage.isPageResponsive();
      expect(isResponsive).toBe(true);
    } catch (error) {
      console.log('没有找到查看所有产品按钮，可能主页布局不同');
    }
  });

  test('页面加载性能测试', async () => {
    const startTime = Date.now();
    await homePage.goto();
    const endTime = Date.now();
    
    const loadTime = endTime - startTime;
    console.log(`页面加载时间: ${loadTime}ms`);
    
    // 页面应该在 5 秒内加载完成
    expect(loadTime).toBeLessThan(5000);
    
    // 获取更详细的性能指标
    const detailedLoadTime = await homePage.getPageLoadTime();
    console.log(`浏览器报告的加载时间: ${detailedLoadTime}ms`);
  });

  test('所有图片应该正常加载', async () => {
    await homePage.goto();
    
    // 等待图片加载
    await homePage.page.waitForTimeout(2000);
    
    const imageStatus = await homePage.checkAllImagesLoaded();
    
    console.log(`图片加载状态: ${imageStatus.loaded}/${imageStatus.total} 成功加载`);
    
    if (imageStatus.failed.length > 0) {
      console.log('加载失败的图片:', imageStatus.failed);
    }
    
    // 至少 90% 的图片应该成功加载
    const successRate = imageStatus.total > 0 ? imageStatus.loaded / imageStatus.total : 1;
    expect(successRate).toBeGreaterThan(0.9);
  });

  test('网络请求错误检查', async () => {
    await homePage.goto();
    
    // 等待页面完全加载
    await homePage.page.waitForTimeout(3000);
    
    const networkErrors = homePage.getErrorDetector().getNetworkErrors();
    
    if (networkErrors.length > 0) {
      console.log('发现网络错误:');
      networkErrors.forEach(error => {
        console.log(`- ${error.method} ${error.url}: ${error.failure}`);
      });
    }
    
    // 不应该有关键资源加载失败
    const criticalErrors = networkErrors.filter(error => 
      error.url.includes('.js') || 
      error.url.includes('.css') ||
      error.url.includes('/api/')
    );
    
    expect(criticalErrors.length).toBe(0);
  });

  test('语言切换功能测试', async () => {
    await homePage.goto('zh');
    
    // 获取中文版本的标题
    const zhTitle = await homePage.getHeroTitle();
    
    // 切换到英文
    await homePage.switchLanguage('en');
    const enTitle = await homePage.getHeroTitle();
    
    // 切换到阿拉伯语
    await homePage.switchLanguage('ar');
    const arTitle = await homePage.getHeroTitle();
    
    // 标题应该不同（假设已翻译）
    expect(zhTitle).toBeTruthy();
    expect(enTitle).toBeTruthy();
    expect(arTitle).toBeTruthy();
    
    // 检查 URL 是否正确更改
    const currentUrl = homePage.getUrl();
    expect(currentUrl).toContain('/ar');
  });

  test('响应式设计测试', async ({ page }) => {
    // 测试不同的视口大小
    const viewports = [
      { width: 1920, height: 1080, name: '桌面' },
      { width: 768, height: 1024, name: '平板' },
      { width: 375, height: 667, name: '手机' }
    ];
    
    for (const viewport of viewports) {
      await page.setViewportSize({ width: viewport.width, height: viewport.height });
      await homePage.goto();
      
      console.log(`测试 ${viewport.name} 视图 (${viewport.width}x${viewport.height})`);
      
      // 检查导航栏在不同尺寸下的表现
      // 在手机视图下，导航应该是隐藏的，而是显示菜单按钮
      if (viewport.name === '手机') {
        const hasMobileMenuButton = await homePage.page.locator('button[aria-label*="menu" i]').isVisible();
        expect(hasMobileMenuButton).toBe(true);
      } else {
        const hasNav = await homePage.hasNavigation();
        expect(hasNav).toBe(true);
      }
      
      // 检查页面是否正常响应
      const isResponsive = await homePage.isPageResponsive();
      expect(isResponsive).toBe(true);
    }
  });
});