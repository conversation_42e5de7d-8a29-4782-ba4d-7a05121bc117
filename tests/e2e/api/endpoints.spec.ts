import { test, expect } from '@playwright/test';

test.describe('API 端点测试', () => {
  const baseURL = 'http://localhost:3000';

  test('搜索 API 应该正常工作', async ({ request }) => {
    const locales = ['zh', 'en', 'ar'];
    
    for (const locale of locales) {
      const response = await request.get(`${baseURL}/api/search`, {
        params: {
          q: 'test',
          locale: locale
        }
      });
      
      expect(response.ok()).toBe(true);
      expect(response.status()).toBe(200);
      
      const data = await response.json();
      
      // 验证响应结构
      expect(data).toHaveProperty('query');
      expect(data).toHaveProperty('results');
      expect(data).toHaveProperty('total');
      expect(data).toHaveProperty('locale');
      
      expect(data.query).toBe('test');
      expect(data.locale).toBe(locale);
      expect(Array.isArray(data.results)).toBe(true);
      expect(typeof data.total).toBe('number');
      
      console.log(`搜索 API (${locale}): 找到 ${data.total} 个结果`);
    }
  });

  test('搜索 API 应该处理空查询', async ({ request }) => {
    const response = await request.get(`${baseURL}/api/search`, {
      params: {
        q: '',
        locale: 'zh'
      }
    });
    
    // 空查询应该返回 400 错误
    expect(response.status()).toBe(400);
    
    const data = await response.json();
    expect(data.error).toBeTruthy();
  });

  test('搜索 API 应该处理特殊字符', async ({ request }) => {
    const specialQueries = [
      '测试@#$%',
      'test&query',
      '"quoted"',
      '中文 English عربي'
    ];
    
    for (const query of specialQueries) {
      const response = await request.get(`${baseURL}/api/search`, {
        params: {
          q: query,
          locale: 'zh'
        }
      });
      
      expect(response.ok()).toBe(true);
      
      const data = await response.json();
      expect(data.query).toBe(query);
    }
  });

  test('产品筛选 API 应该正常工作', async ({ request }) => {
    const response = await request.get(`${baseURL}/api/products/filter`, {
      params: {
        page: '1',
        limit: '24',
        sortBy: 'newest'
      }
    });
    
    expect(response.ok()).toBe(true);
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    
    // 验证响应结构
    expect(data).toHaveProperty('products');
    expect(data).toHaveProperty('totalCount');
    expect(data).toHaveProperty('currentPage');
    expect(data).toHaveProperty('totalPages');
    expect(data).toHaveProperty('filters');
    
    expect(Array.isArray(data.products)).toBe(true);
    expect(data.currentPage).toBe(1);
    
    console.log(`筛选 API: 找到 ${data.totalCount} 个产品`);
  });

  test('产品筛选 API 应该支持分页', async ({ request }) => {
    // 第一页
    const page1Response = await request.get(`${baseURL}/api/products/filter`, {
      params: {
        page: '1',
        limit: '10'
      }
    });
    
    expect(page1Response.ok()).toBe(true);
    const page1Data = await page1Response.json();
    
    if (page1Data.totalPages > 1) {
      // 第二页
      const page2Response = await request.get(`${baseURL}/api/products/filter`, {
        params: {
          page: '2',
          limit: '10'
        }
      });
      
      expect(page2Response.ok()).toBe(true);
      const page2Data = await page2Response.json();
      
      // 验证分页逻辑
      expect(page2Data.currentPage).toBe(2);
      expect(page2Data.products.length).toBeLessThanOrEqual(10);
      
      // 确保两页的产品不同
      const page1Ids = page1Data.products.map((p: any) => p._id);
      const page2Ids = page2Data.products.map((p: any) => p._id);
      const intersection = page1Ids.filter((id: string) => page2Ids.includes(id));
      expect(intersection.length).toBe(0);
    }
  });

  test('产品筛选 API 应该支持价格范围筛选', async ({ request }) => {
    const response = await request.get(`${baseURL}/api/products/filter`, {
      params: {
        minPrice: '100',
        maxPrice: '500'
      }
    });
    
    expect(response.ok()).toBe(true);
    const data = await response.json();
    
    // 验证返回的产品价格都在指定范围内
    data.products.forEach((product: any) => {
      if (product.price) {
        expect(product.price).toBeGreaterThanOrEqual(100);
        expect(product.price).toBeLessThanOrEqual(500);
      }
    });
  });

  test('重新验证 API 应该需要授权', async ({ request }) => {
    // 没有 token 的请求应该失败
    const response = await request.post(`${baseURL}/api/revalidate`, {
      data: {
        type: 'product',
        slug: 'test-product'
      }
    });
    
    // 应该返回 401 或 403
    expect([401, 403]).toContain(response.status());
  });

  test('API 响应时间测试', async ({ request }) => {
    const endpoints = [
      { url: '/api/search?q=test&locale=zh', name: '搜索 API' },
      { url: '/api/products/filter?page=1&limit=24', name: '筛选 API' }
    ];
    
    for (const endpoint of endpoints) {
      const startTime = Date.now();
      
      const response = await request.get(`${baseURL}${endpoint.url}`);
      
      const responseTime = Date.now() - startTime;
      
      expect(response.ok()).toBe(true);
      console.log(`${endpoint.name} 响应时间: ${responseTime}ms`);
      
      // API 响应时间应该在 3 秒内
      expect(responseTime).toBeLessThan(3000);
    }
  });

  test('API 错误处理测试', async ({ request }) => {
    // 测试无效的 locale
    const invalidLocaleResponse = await request.get(`${baseURL}/api/search`, {
      params: {
        q: 'test',
        locale: 'invalid'
      }
    });
    
    // 应该返回默认语言的结果或错误
    expect([200, 400]).toContain(invalidLocaleResponse.status());
    
    // 测试无效的筛选参数
    const invalidFilterResponse = await request.get(`${baseURL}/api/products/filter`, {
      params: {
        invalidField: 'invalid',
        page: '1'
      }
    });
    
    // 应该忽略无效字段但仍然返回结果
    expect(invalidFilterResponse.ok()).toBe(true);
  });
});