import { Page, ConsoleMessage, Request } from '@playwright/test';

export interface ConsoleError {
  type: 'console';
  message: string;
  url: string;
  timestamp: Date;
}

export interface NetworkError {
  type: 'network';
  url: string;
  method: string;
  failure: string | null;
  timestamp: Date;
}

export interface PageError {
  type: 'page';
  message: string;
  url: string;
  timestamp: Date;
}

export interface SchemaError {
  type: 'schema';
  field: string;
  issue: string;
  documentType: string;
  timestamp: Date;
}

export type ErrorType = ConsoleError | NetworkError | PageError | SchemaError;

/**
 * 错误检测工具类
 * 用于捕获和分析测试过程中的各种错误
 */
export class ErrorDetector {
  private errors: ErrorType[] = [];
  private page: Page;
  private consoleHandler?: (msg: ConsoleMessage) => void;
  private requestFailedHandler?: (request: Request) => void;
  private pageErrorHandler?: (error: Error) => void;

  constructor(page: Page) {
    this.page = page;
  }

  /**
   * 开始监听错误
   */
  async startMonitoring() {
    // 监听控制台错误
    this.consoleHandler = (msg: ConsoleMessage) => {
      if (msg.type() === 'error') {
        this.errors.push({
          type: 'console',
          message: msg.text(),
          url: this.page.url(),
          timestamp: new Date(),
        });
      }
    };

    // 监听网络请求失败
    this.requestFailedHandler = (request: Request) => {
      this.errors.push({
        type: 'network',
        url: request.url(),
        method: request.method(),
        failure: request.failure()?.errorText || null,
        timestamp: new Date(),
      });
    };

    // 监听页面崩溃
    this.pageErrorHandler = (error: Error) => {
      this.errors.push({
        type: 'page',
        message: error.message,
        url: this.page.url(),
        timestamp: new Date(),
      });
    };

    this.page.on('console', this.consoleHandler);
    this.page.on('requestfailed', this.requestFailedHandler);
    this.page.on('pageerror', this.pageErrorHandler);
  }

  /**
   * 停止监听错误
   */
  async stopMonitoring() {
    if (this.consoleHandler) {
      this.page.off('console', this.consoleHandler);
    }
    if (this.requestFailedHandler) {
      this.page.off('requestfailed', this.requestFailedHandler);
    }
    if (this.pageErrorHandler) {
      this.page.off('pageerror', this.pageErrorHandler);
    }
  }

  /**
   * 检查页面是否崩溃
   */
  async checkPageCrash(): Promise<boolean> {
    try {
      // 尝试执行一个简单的 JavaScript 来检查页面是否响应
      await this.page.evaluate(() => document.title);
      return false;
    } catch (error) {
      return true;
    }
  }

  /**
   * 获取所有控制台错误
   */
  getConsoleErrors(): ConsoleError[] {
    return this.errors.filter((e): e is ConsoleError => e.type === 'console');
  }

  /**
   * 获取所有网络错误
   */
  getNetworkErrors(): NetworkError[] {
    return this.errors.filter((e): e is NetworkError => e.type === 'network');
  }

  /**
   * 获取所有页面错误
   */
  getPageErrors(): PageError[] {
    return this.errors.filter((e): e is PageError => e.type === 'page');
  }

  /**
   * 获取所有错误
   */
  getAllErrors(): ErrorType[] {
    return [...this.errors];
  }

  /**
   * 清空错误列表
   */
  clearErrors() {
    this.errors = [];
  }

  /**
   * 检查是否有错误
   */
  hasErrors(): boolean {
    return this.errors.length > 0;
  }

  /**
   * 获取错误摘要
   */
  getErrorSummary(): string {
    if (!this.hasErrors()) {
      return '没有检测到错误';
    }

    const summary = [
      `总共检测到 ${this.errors.length} 个错误:`,
      `- 控制台错误: ${this.getConsoleErrors().length}`,
      `- 网络错误: ${this.getNetworkErrors().length}`,
      `- 页面错误: ${this.getPageErrors().length}`,
    ];

    return summary.join('\n');
  }

  /**
   * 生成详细的错误报告
   */
  generateDetailedReport(): string {
    if (!this.hasErrors()) {
      return '没有检测到错误';
    }

    let report = `错误报告 (生成时间: ${new Date().toISOString()})\n`;
    report += '=' .repeat(50) + '\n\n';

    // 控制台错误
    const consoleErrors = this.getConsoleErrors();
    if (consoleErrors.length > 0) {
      report += '控制台错误:\n';
      consoleErrors.forEach((err, index) => {
        report += `  ${index + 1}. [${err.timestamp.toISOString()}] ${err.url}\n`;
        report += `     消息: ${err.message}\n\n`;
      });
    }

    // 网络错误
    const networkErrors = this.getNetworkErrors();
    if (networkErrors.length > 0) {
      report += '网络错误:\n';
      networkErrors.forEach((err, index) => {
        report += `  ${index + 1}. [${err.timestamp.toISOString()}] ${err.method} ${err.url}\n`;
        report += `     失败原因: ${err.failure || '未知'}\n\n`;
      });
    }

    // 页面错误
    const pageErrors = this.getPageErrors();
    if (pageErrors.length > 0) {
      report += '页面错误:\n';
      pageErrors.forEach((err, index) => {
        report += `  ${index + 1}. [${err.timestamp.toISOString()}] ${err.url}\n`;
        report += `     错误: ${err.message}\n\n`;
      });
    }

    return report;
  }
}