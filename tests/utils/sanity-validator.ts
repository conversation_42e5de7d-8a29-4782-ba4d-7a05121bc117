import { SchemaError } from './error-detector';

interface SanityDocument {
  _id: string;
  _type: string;
  _createdAt: string;
  _updatedAt: string;
  [key: string]: any;
}

interface SchemaField {
  name: string;
  type: string;
  required?: boolean;
  localized?: boolean;
}

interface DocumentSchema {
  name: string;
  fields: SchemaField[];
}

/**
 * Sanity Schema 验证器
 * 用于验证 Sanity 文档是否符合预期的 Schema 结构
 */
export class SanityValidator {
  private schemas: Map<string, DocumentSchema> = new Map();
  private errors: SchemaError[] = [];

  constructor() {
    this.initializeSchemas();
  }

  /**
   * 初始化已知的 Schema 定义
   */
  private initializeSchemas() {
    // 产品 Schema
    this.schemas.set('product', {
      name: 'product',
      fields: [
        { name: 'name', type: 'localized', required: true, localized: true },
        { name: 'slug', type: 'slug', required: true },
        { name: 'description', type: 'localized', localized: true },
        { name: 'gallery', type: 'array', required: true },
        { name: 'price', type: 'number', required: true },
        { name: 'currency', type: 'string' },
        { name: 'category', type: 'reference' },
        { name: 'ipSeries', type: 'reference' },
        { name: 'tags', type: 'array' },
        { name: 'inStock', type: 'boolean' },
        { name: 'featured', type: 'boolean' },
        { name: 'releaseDate', type: 'date' },
      ],
    });

    // 分类 Schema
    this.schemas.set('category', {
      name: 'category',
      fields: [
        { name: 'name', type: 'localized', required: true, localized: true },
        { name: 'slug', type: 'slug', required: true },
        { name: 'description', type: 'localized', localized: true },
        { name: 'icon', type: 'string' },
      ],
    });

    // IP系列 Schema
    this.schemas.set('ipSeries', {
      name: 'ipSeries',
      fields: [
        { name: 'name', type: 'localized', required: true, localized: true },
        { name: 'slug', type: 'slug', required: true },
        { name: 'description', type: 'localized', localized: true },
        { name: 'image', type: 'flexibleImage' },
      ],
    });

    // 首页 Schema
    this.schemas.set('homepage', {
      name: 'homepage',
      fields: [
        { name: 'hero', type: 'object' },
        { name: 'features', type: 'array' },
        { name: 'brandStory', type: 'object' },
        { name: 'stats', type: 'array' },
      ],
    });

    // 关于页面 Schema
    this.schemas.set('aboutPage', {
      name: 'aboutPage',
      fields: [
        { name: 'hero', type: 'object' },
        { name: 'content', type: 'blockContent' },
        { name: 'team', type: 'array' },
        { name: 'timeline', type: 'array' },
      ],
    });

    // 联系页面 Schema
    this.schemas.set('contactPage', {
      name: 'contactPage',
      fields: [
        { name: 'title', type: 'localized', localized: true },
        { name: 'description', type: 'localized', localized: true },
        { name: 'contactInfo', type: 'object' },
        { name: 'form', type: 'object' },
      ],
    });
  }

  /**
   * 验证单个文档
   */
  validateDocument(document: SanityDocument): SchemaError[] {
    const errors: SchemaError[] = [];
    const schema = this.schemas.get(document._type);

    if (!schema) {
      errors.push({
        type: 'schema',
        field: '_type',
        issue: `未知的文档类型: ${document._type}`,
        documentType: document._type,
        timestamp: new Date(),
      });
      return errors;
    }

    // 验证必填字段
    schema.fields.forEach(field => {
      if (field.required && !(field.name in document)) {
        errors.push({
          type: 'schema',
          field: field.name,
          issue: '必填字段缺失',
          documentType: document._type,
          timestamp: new Date(),
        });
      }

      // 验证本地化字段
      if (field.localized && field.name in document) {
        const value = document[field.name];
        if (typeof value === 'object' && value !== null) {
          // 检查是否包含所有语言版本
          const requiredLocales = ['zh', 'en', 'ar'];
          const missingLocales = requiredLocales.filter(locale => !(locale in value));
          
          if (missingLocales.length > 0) {
            errors.push({
              type: 'schema',
              field: field.name,
              issue: `缺少语言版本: ${missingLocales.join(', ')}`,
              documentType: document._type,
              timestamp: new Date(),
            });
          }
        }
      }
    });

    // 检查废弃字段
    const deprecatedFields = this.checkDeprecatedFields(document);
    errors.push(...deprecatedFields);

    return errors;
  }

  /**
   * 检查废弃字段
   */
  private checkDeprecatedFields(document: SanityDocument): SchemaError[] {
    const errors: SchemaError[] = [];
    const deprecatedFields = ['mainImage']; // 已迁移到 gallery

    Object.keys(document).forEach(key => {
      if (deprecatedFields.includes(key)) {
        errors.push({
          type: 'schema',
          field: key,
          issue: `使用了废弃字段，该字段已被移除或重命名`,
          documentType: document._type,
          timestamp: new Date(),
        });
      }
    });

    return errors;
  }

  /**
   * 验证多个文档
   */
  validateDocuments(documents: SanityDocument[]): SchemaError[] {
    const allErrors: SchemaError[] = [];
    
    documents.forEach(doc => {
      const errors = this.validateDocument(doc);
      allErrors.push(...errors);
    });

    this.errors = allErrors;
    return allErrors;
  }

  /**
   * 验证字段类型
   */
  validateFieldType(value: any, expectedType: string): boolean {
    switch (expectedType) {
      case 'string':
        return typeof value === 'string';
      case 'number':
        return typeof value === 'number';
      case 'boolean':
        return typeof value === 'boolean';
      case 'array':
        return Array.isArray(value);
      case 'object':
        return typeof value === 'object' && value !== null && !Array.isArray(value);
      case 'localized':
        return typeof value === 'object' && value !== null && 
               ('zh' in value || 'en' in value || 'ar' in value);
      case 'slug':
        return typeof value === 'object' && value !== null && 'current' in value;
      case 'reference':
        return typeof value === 'object' && value !== null && '_ref' in value;
      case 'flexibleImage':
        return typeof value === 'object' && value !== null && 
               ('asset' in value || 'url' in value);
      case 'blockContent':
        return Array.isArray(value);
      default:
        return true;
    }
  }

  /**
   * 获取验证错误摘要
   */
  getErrorSummary(): string {
    if (this.errors.length === 0) {
      return '所有文档都符合 Schema 定义';
    }

    const errorsByType = new Map<string, number>();
    const errorsByField = new Map<string, number>();

    this.errors.forEach(error => {
      errorsByType.set(error.documentType, (errorsByType.get(error.documentType) || 0) + 1);
      errorsByField.set(error.field, (errorsByField.get(error.field) || 0) + 1);
    });

    let summary = `Schema 验证发现 ${this.errors.length} 个问题:\n`;
    summary += '\n按文档类型:\n';
    errorsByType.forEach((count, type) => {
      summary += `  - ${type}: ${count} 个错误\n`;
    });

    summary += '\n按字段:\n';
    errorsByField.forEach((count, field) => {
      summary += `  - ${field}: ${count} 个错误\n`;
    });

    return summary;
  }

  /**
   * 生成详细报告
   */
  generateDetailedReport(): string {
    if (this.errors.length === 0) {
      return '所有文档都符合 Schema 定义';
    }

    let report = `Schema 验证报告 (生成时间: ${new Date().toISOString()})\n`;
    report += '=' .repeat(50) + '\n\n';

    // 按文档类型分组错误
    const errorsByDocType = new Map<string, SchemaError[]>();
    this.errors.forEach(error => {
      const list = errorsByDocType.get(error.documentType) || [];
      list.push(error);
      errorsByDocType.set(error.documentType, list);
    });

    errorsByDocType.forEach((errors, docType) => {
      report += `文档类型: ${docType}\n`;
      errors.forEach((err, index) => {
        report += `  ${index + 1}. 字段: ${err.field}\n`;
        report += `     问题: ${err.issue}\n`;
      });
      report += '\n';
    });

    return report;
  }

  /**
   * 清空错误
   */
  clearErrors() {
    this.errors = [];
  }
}