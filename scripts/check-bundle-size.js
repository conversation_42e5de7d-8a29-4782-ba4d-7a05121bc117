#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { gzipSync } = require('zlib');

// Bundle size budgets (in KB)
const BUDGETS = {
  'app-pages-browser': {
    maxSize: 250, // 250KB max for main app bundle
    warnSize: 200, // Warning at 200KB
  },
  'chunks': {
    maxSize: 100, // 100KB max for any individual chunk
    warnSize: 75,  // Warning at 75KB
  },
  'total': {
    maxSize: 500, // 500KB total JS
    warnSize: 400, // Warning at 400KB
  }
};

// ANSI color codes
const colors = {
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  green: '\x1b[32m',
  reset: '\x1b[0m',
};

function formatBytes(bytes) {
  return (bytes / 1024).toFixed(2) + ' KB';
}

function getGzipSize(filePath) {
  const content = fs.readFileSync(filePath);
  return gzipSync(content).length;
}

function checkBundleSizes() {
  const buildDir = path.join(__dirname, '..', '.next');
  const statsPath = path.join(buildDir, 'build-manifest.json');
  
  if (!fs.existsSync(statsPath)) {
    console.error('Build manifest not found. Please run "npm run build" first.');
    process.exit(1);
  }

  const manifest = JSON.parse(fs.readFileSync(statsPath, 'utf-8'));
  const results = [];
  let totalSize = 0;
  let hasErrors = false;
  let hasWarnings = false;

  // Check main app bundle
  const appFiles = manifest.pages['/_app'] || [];
  let appBundleSize = 0;
  
  appFiles.forEach(file => {
    if (file.endsWith('.js')) {
      const filePath = path.join(buildDir, file);
      if (fs.existsSync(filePath)) {
        const size = getGzipSize(filePath);
        appBundleSize += size;
        totalSize += size;
      }
    }
  });

  if (appBundleSize > 0) {
    const budget = BUDGETS['app-pages-browser'];
    const sizeKB = appBundleSize / 1024;
    const status = sizeKB > budget.maxSize ? 'error' : 
                   sizeKB > budget.warnSize ? 'warning' : 'ok';
    
    if (status === 'error') hasErrors = true;
    if (status === 'warning') hasWarnings = true;

    results.push({
      name: 'Main App Bundle',
      size: appBundleSize,
      formatted: formatBytes(appBundleSize),
      budget: formatBytes(budget.maxSize * 1024),
      status,
    });
  }

  // Check individual chunks
  const staticDir = path.join(buildDir, 'static', 'chunks');
  if (fs.existsSync(staticDir)) {
    const chunks = fs.readdirSync(staticDir).filter(f => f.endsWith('.js'));
    
    chunks.forEach(chunk => {
      const filePath = path.join(staticDir, chunk);
      const size = getGzipSize(filePath);
      totalSize += size;
      
      const budget = BUDGETS.chunks;
      const sizeKB = size / 1024;
      const status = sizeKB > budget.maxSize ? 'error' : 
                     sizeKB > budget.warnSize ? 'warning' : 'ok';
      
      if (status === 'error') hasErrors = true;
      if (status === 'warning') hasWarnings = true;

      if (status !== 'ok') {
        results.push({
          name: `Chunk: ${chunk}`,
          size: size,
          formatted: formatBytes(size),
          budget: formatBytes(budget.maxSize * 1024),
          status,
        });
      }
    });
  }

  // Check total size
  const totalBudget = BUDGETS.total;
  const totalSizeKB = totalSize / 1024;
  const totalStatus = totalSizeKB > totalBudget.maxSize ? 'error' : 
                      totalSizeKB > totalBudget.warnSize ? 'warning' : 'ok';
  
  if (totalStatus === 'error') hasErrors = true;
  if (totalStatus === 'warning') hasWarnings = true;

  // Print results
  console.log('\n📦 Bundle Size Report\n');
  console.log('═'.repeat(60));
  
  results.forEach(result => {
    const color = result.status === 'error' ? colors.red : 
                  result.status === 'warning' ? colors.yellow : colors.green;
    const icon = result.status === 'error' ? '❌' : 
                 result.status === 'warning' ? '⚠️ ' : '✅';
    
    console.log(`${icon} ${result.name}`);
    console.log(`   Size: ${color}${result.formatted}${colors.reset} (Budget: ${result.budget})`);
  });

  console.log('─'.repeat(60));
  const totalColor = totalStatus === 'error' ? colors.red : 
                     totalStatus === 'warning' ? colors.yellow : colors.green;
  const totalIcon = totalStatus === 'error' ? '❌' : 
                    totalStatus === 'warning' ? '⚠️ ' : '✅';
  
  console.log(`${totalIcon} Total JS Size: ${totalColor}${formatBytes(totalSize)}${colors.reset} (Budget: ${formatBytes(totalBudget.maxSize * 1024)})`);
  console.log('═'.repeat(60));

  // Exit with appropriate code
  if (hasErrors) {
    console.log(`\n${colors.red}❌ Bundle size check failed!${colors.reset}`);
    console.log('Please optimize your bundles to meet the size budgets.\n');
    process.exit(1);
  } else if (hasWarnings) {
    console.log(`\n${colors.yellow}⚠️  Bundle size check passed with warnings.${colors.reset}`);
    console.log('Consider optimizing to avoid future budget violations.\n');
  } else {
    console.log(`\n${colors.green}✅ Bundle size check passed!${colors.reset}\n`);
  }
}

// Run the check
checkBundleSizes();