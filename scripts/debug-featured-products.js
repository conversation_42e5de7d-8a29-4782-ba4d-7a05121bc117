#!/usr/bin/env node

import 'dotenv/config'
import sanityClient from '@sanity/client'

// Configuration
const projectId = process.env.NEXT_PUBLIC_SANITY_PROJECT_ID
const dataset = process.env.NEXT_PUBLIC_SANITY_DATASET || 'production'
const apiVersion = '2024-01-11'

if (!projectId) {
  console.error('❌ Missing NEXT_PUBLIC_SANITY_PROJECT_ID environment variable')
  process.exit(1)
}

// Initialize Sanity client
const client = sanityClient({
  projectId,
  dataset,
  apiVersion,
  useCdn: false
})

console.log(`🔍 Debugging Featured Products Settings...`)
console.log(`📦 Dataset: ${dataset}`)
console.log('')

async function debugFeaturedProducts() {
  try {
    // 1. Check featured products configuration
    console.log('📋 Fetching Featured Products Configuration...')
    const featuredProducts = await client.fetch(`
      *[_type == "featuredProducts" && isActive == true][0] {
        _id,
        title,
        subtitle,
        displaySettings,
        isActive,
        "productsCount": count(products),
        products[0..2]->{
          _id,
          name,
          tags,
          price,
          category->{
            name
          }
        }
      }
    `)

    if (!featuredProducts) {
      console.error('❌ No active featured products configuration found!')
      console.log('Please create a featured products configuration in Sanity Studio and set isActive to true.')
      return
    }

    console.log('✅ Found Featured Products Configuration:')
    console.log(`   ID: ${featuredProducts._id}`)
    console.log(`   Title: ${featuredProducts.title?.zh || featuredProducts.title?.en || 'No title'}`)
    console.log(`   Active: ${featuredProducts.isActive}`)
    console.log(`   Products Count: ${featuredProducts.productsCount}`)
    console.log('')

    // 2. Check display settings
    console.log('⚙️  Display Settings:')
    const displaySettings = featuredProducts.displaySettings || {}
    console.log(`   Show Prices: ${displaySettings.showPrices ?? 'undefined (will default to true)'}`)
    console.log(`   Show Ratings: ${displaySettings.showRatings ?? 'undefined (will default to true)'}`)
    console.log(`   Show Categories: ${displaySettings.showCategories ?? 'undefined (will default to true)'}`)
    console.log(`   Show Badges: ${displaySettings.showBadges ?? 'undefined (will default to true)'}`)
    console.log('')

    // 3. Check products data
    console.log('📦 Sample Products:')
    if (featuredProducts.products && featuredProducts.products.length > 0) {
      featuredProducts.products.forEach((product, index) => {
        console.log(`\n   Product ${index + 1}:`)
        console.log(`   - Name: ${product.name?.zh || product.name?.en || 'No name'}`)
        console.log(`   - Price: ${product.price || 'No price set'}`)
        console.log(`   - Category: ${product.category?.name?.zh || product.category?.name?.en || 'No category'}`)
        console.log(`   - Tags: ${product.tags && product.tags.length > 0 ? product.tags.join(', ') : 'No tags'}`)
      })
    } else {
      console.log('   ❌ No products found in featured products list!')
    }

    // 4. Run the actual query used by the frontend
    console.log('\n\n🔍 Running Frontend Query...')
    const frontendData = await client.fetch(`
      {
        "featuredProducts": *[_type == "featuredProducts" && isActive == true][0] {
          _id,
          title,
          subtitle,
          "products": products[]->{
            _id,
            name,
            slug,
            shortDescription,
            gallery[] {
              imageType,
              imageType == "upload" => {
                "uploadedImage": uploadedImage {
                  asset,
                  alt,
                  caption
                }
              },
              imageType == "external" => {
                externalUrl,
                alt,
                caption,
                fallbackImage
              }
            },
            price,
            currency,
            stockStatus,
            tags,
            category->{
              name,
              slug
            },
            publishedAt,
            tags,
            "rating": 4.5
          },
          displaySettings,
          layout,
          _updatedAt
        }
      }
    `)

    if (frontendData.featuredProducts) {
      console.log('✅ Frontend query successful!')
      console.log(`   Display Settings in frontend query: ${JSON.stringify(frontendData.featuredProducts.displaySettings, null, 2)}`)
    } else {
      console.log('❌ Frontend query returned no data!')
    }

  } catch (error) {
    console.error('❌ Error:', error)
  }
}

// Run debug
debugFeaturedProducts()