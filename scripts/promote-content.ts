#!/usr/bin/env npx tsx

/**
 * Content Promotion Script (TypeScript version)
 * Promotes content from staging dataset to production dataset
 * 
 * Usage: npx tsx scripts/promote-content.ts [options]
 * Options:
 *   --dry-run     Preview changes without applying them
 *   --force       Skip confirmation prompts
 *   --types       Comma-separated list of document types to sync
 *   --exclude     Comma-separated list of document types to exclude
 */

import { createClient } from '@sanity/client'
import * as readline from 'readline'
import { exec } from 'child_process'
import { promisify } from 'util'

const execAsync = promisify(exec)

// Types
interface PromotionConfig {
  projectId: string
  token: string
  apiVersion: string
  useCdn: boolean
  stagingDataset: string
  productionDataset: string
}

interface DocumentChange {
  type: string
  id: string
  doc?: any
}

interface Changes {
  new: DocumentChange[]
  updated: DocumentChange[]
  unchanged: DocumentChange[]
}

// Configuration
const config: PromotionConfig = {
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || '4za4x22i',
  token: process.env.SANITY_API_TOKEN || '',
  apiVersion: '2024-01-01',
  useCdn: false,
  stagingDataset: process.env.NEXT_PUBLIC_SANITY_DATASET_STAGING || 'staging',
  productionDataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production'
}

// Validate environment
if (!config.token) {
  console.error('❌ Error: SANITY_API_TOKEN is required for content promotion')
  console.error('Please set it in your .env file')
  process.exit(1)
}

// Parse command line arguments
const args = process.argv.slice(2)
const isDryRun = args.includes('--dry-run')
const isForce = args.includes('--force')

// Parse document types from command line
const typesIndex = args.indexOf('--types')
const excludeIndex = args.indexOf('--exclude')

let documentTypesToSync = [
  'homepage',
  'aboutPage',
  'contactPage',
  'siteSettings',
  'navigation',
  'product',
  'category',
  'ipSeries'
]

if (typesIndex !== -1 && args[typesIndex + 1]) {
  documentTypesToSync = args[typesIndex + 1].split(',')
}

if (excludeIndex !== -1 && args[excludeIndex + 1]) {
  const excludeTypes = args[excludeIndex + 1].split(',')
  documentTypesToSync = documentTypesToSync.filter(type => !excludeTypes.includes(type))
}

// Create clients
const stagingClient = createClient({
  projectId: config.projectId,
  dataset: config.stagingDataset,
  apiVersion: config.apiVersion,
  useCdn: config.useCdn,
  token: config.token
})

const productionClient = createClient({
  projectId: config.projectId,
  dataset: config.productionDataset,
  apiVersion: config.apiVersion,
  useCdn: config.useCdn,
  token: config.token
})

// ANSI color codes
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
}

function log(message: string, color: keyof typeof colors = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

// Helper to ask for user confirmation
async function askConfirmation(question: string): Promise<boolean> {
  if (isForce) return true
  
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  })
  
  return new Promise((resolve) => {
    rl.question(`${question} (y/N): `, (answer) => {
      rl.close()
      resolve(answer.toLowerCase() === 'y')
    })
  })
}

// Compare documents (ignoring system fields)
function areDocumentsEqual(doc1: any, doc2: any): boolean {
  const systemFields = ['_rev', '_updatedAt', '_createdAt']
  
  const clean = (doc: any) => {
    const cleaned = { ...doc }
    systemFields.forEach(field => delete cleaned[field])
    return cleaned
  }
  
  return JSON.stringify(clean(doc1)) === JSON.stringify(clean(doc2))
}

// Main promotion function
async function promoteContent() {
  try {
    log('\n🚀 Sanity Content Promotion Tool', 'cyan')
    log('==================================\n')
    
    log(`📍 Source Dataset: ${config.stagingDataset}`, 'yellow')
    log(`📍 Target Dataset: ${config.productionDataset}`, 'yellow')
    log(`📋 Document Types: ${documentTypesToSync.join(', ')}`, 'blue')
    
    if (isDryRun) {
      log('\n⚠️  DRY RUN MODE - No changes will be made', 'yellow')
    }
    
    // Step 1: Create backup of production
    if (!isDryRun) {
      const shouldBackup = await askConfirmation('\n💾 Create a backup of production dataset before proceeding?')
      
      if (shouldBackup) {
        log('\n📦 Creating production backup...', 'blue')
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5)
        const backupName = `production-backup-${timestamp}`
        
        try {
          const exportCommand = `sanity dataset export ${config.productionDataset} ${backupName}.tar.gz --project ${config.projectId}`
          await execAsync(exportCommand)
          log(`✅ Backup created: ${backupName}.tar.gz`, 'green')
        } catch (error) {
          log('❌ Failed to create backup', 'red')
          console.error(error)
          
          const proceed = await askConfirmation('Continue without backup?')
          if (!proceed) {
            log('❌ Promotion cancelled', 'red')
            return
          }
        }
      }
    }
    
    // Step 2: Fetch and analyze documents
    log('\n🔍 Analyzing content differences...', 'blue')
    
    const changes: Changes = {
      new: [],
      updated: [],
      unchanged: []
    }
    
    let totalDocuments = 0
    
    for (const docType of documentTypesToSync) {
      const query = `*[_type == "${docType}"]`
      
      try {
        const [stagingDocs, productionDocs] = await Promise.all([
          stagingClient.fetch(query),
          productionClient.fetch(query)
        ])
        
        totalDocuments += stagingDocs.length
        
        // Create a map of production documents by ID
        const prodDocsMap = new Map(
          productionDocs.map((doc: any) => [doc._id, doc])
        )
        
        // Compare documents
        for (const stagingDoc of stagingDocs) {
          const prodDoc = prodDocsMap.get(stagingDoc._id)
          
          if (!prodDoc) {
            changes.new.push({
              type: docType,
              id: stagingDoc._id,
              doc: stagingDoc
            })
          } else if (!areDocumentsEqual(stagingDoc, prodDoc)) {
            changes.updated.push({
              type: docType,
              id: stagingDoc._id,
              doc: stagingDoc
            })
          } else {
            changes.unchanged.push({
              type: docType,
              id: stagingDoc._id
            })
          }
        }
        
        log(`  ✓ Analyzed ${stagingDocs.length} ${docType} documents`)
        
      } catch (error) {
        log(`  ✗ Failed to analyze ${docType} documents`, 'red')
        console.error(error)
      }
    }
    
    // Display changes summary
    log('\n📊 Changes Summary:', 'cyan')
    log('─────────────────────')
    log(`  📄 Total documents analyzed: ${totalDocuments}`)
    log(`  🆕 New documents: ${changes.new.length}`, changes.new.length > 0 ? 'green' : 'reset')
    log(`  📝 Updated documents: ${changes.updated.length}`, changes.updated.length > 0 ? 'yellow' : 'reset')
    log(`  ✓  Unchanged documents: ${changes.unchanged.length}`)
    
    if (changes.new.length === 0 && changes.updated.length === 0) {
      log('\n✅ No changes to promote! Both datasets are in sync.', 'green')
      return
    }
    
    // Show detailed changes
    if (changes.new.length > 0) {
      log('\n🆕 New Documents:', 'green')
      const newByType = changes.new.reduce((acc, item) => {
        acc[item.type] = (acc[item.type] || 0) + 1
        return acc
      }, {} as Record<string, number>)
      
      Object.entries(newByType).forEach(([type, count]) => {
        log(`  • ${type}: ${count} document${count > 1 ? 's' : ''}`)
      })
    }
    
    if (changes.updated.length > 0) {
      log('\n📝 Updated Documents:', 'yellow')
      const updatedByType = changes.updated.reduce((acc, item) => {
        acc[item.type] = (acc[item.type] || 0) + 1
        return acc
      }, {} as Record<string, number>)
      
      Object.entries(updatedByType).forEach(([type, count]) => {
        log(`  • ${type}: ${count} document${count > 1 ? 's' : ''}`)
      })
    }
    
    // Ask for confirmation
    if (!isDryRun) {
      log('\n⚠️  WARNING: This will modify the production dataset!', 'yellow')
      const confirmed = await askConfirmation(
        `Promote ${changes.new.length + changes.updated.length} changes to production?`
      )
      
      if (!confirmed) {
        log('\n❌ Promotion cancelled by user', 'red')
        return
      }
      
      // Step 3: Apply changes
      log('\n🔄 Applying changes to production...', 'blue')
      
      let successCount = 0
      let errorCount = 0
      const errors: string[] = []
      
      // Process in batches to avoid overwhelming the API
      const BATCH_SIZE = 10
      
      // Process new documents
      if (changes.new.length > 0) {
        log('\n📥 Creating new documents...')
        
        for (let i = 0; i < changes.new.length; i += BATCH_SIZE) {
          const batch = changes.new.slice(i, i + BATCH_SIZE)
          
          await Promise.all(
            batch.map(async (item) => {
              try {
                await productionClient.create(item.doc!)
                successCount++
                log(`  ✅ Created: ${item.type} - ${item.id}`, 'green')
              } catch (error: any) {
                errorCount++
                const errorMsg = `Failed to create ${item.type} - ${item.id}: ${error.message}`
                errors.push(errorMsg)
                log(`  ❌ ${errorMsg}`, 'red')
              }
            })
          )
        }
      }
      
      // Process updated documents
      if (changes.updated.length > 0) {
        log('\n📝 Updating existing documents...')
        
        for (let i = 0; i < changes.updated.length; i += BATCH_SIZE) {
          const batch = changes.updated.slice(i, i + BATCH_SIZE)
          
          await Promise.all(
            batch.map(async (item) => {
              try {
                // Remove system fields to avoid conflicts
                const { _id, _rev, _createdAt, _updatedAt, ...updateData } = item.doc!
                
                await productionClient
                  .patch(_id)
                  .set(updateData)
                  .commit()
                  
                successCount++
                log(`  ✅ Updated: ${item.type} - ${item.id}`, 'green')
              } catch (error: any) {
                errorCount++
                const errorMsg = `Failed to update ${item.type} - ${item.id}: ${error.message}`
                errors.push(errorMsg)
                log(`  ❌ ${errorMsg}`, 'red')
              }
            })
          )
        }
      }
      
      // Final summary
      log('\n📊 Promotion Results:', 'cyan')
      log('─────────────────────')
      log(`  ✅ Success: ${successCount} documents`, 'green')
      log(`  ❌ Errors: ${errorCount} documents`, errorCount > 0 ? 'red' : 'reset')
      
      if (errorCount > 0) {
        log('\n⚠️  Some documents failed to promote:', 'yellow')
        errors.forEach(error => log(`  • ${error}`, 'red'))
        log('\nPlease review the errors and try again if needed.', 'yellow')
      } else {
        log('\n🎉 All changes successfully promoted to production!', 'green')
        log('🔄 The production dataset is now in sync with staging.', 'cyan')
      }
    } else {
      // Dry run summary
      log('\n✅ Dry run complete. Use without --dry-run to apply changes.', 'green')
    }
    
  } catch (error) {
    log('\n❌ Promotion failed with unexpected error:', 'red')
    console.error(error)
    process.exit(1)
  }
}

// Show help if requested
if (args.includes('--help') || args.includes('-h')) {
  log('Sanity Content Promotion Tool', 'cyan')
  log('\nUsage: npx tsx scripts/promote-content.ts [options]')
  log('\nOptions:')
  log('  --dry-run     Preview changes without applying them')
  log('  --force       Skip confirmation prompts')
  log('  --types       Comma-separated list of document types to sync')
  log('  --exclude     Comma-separated list of document types to exclude')
  log('  --help        Show this help message')
  log('\nExamples:')
  log('  npx tsx scripts/promote-content.ts --dry-run')
  log('  npx tsx scripts/promote-content.ts --types product,category')
  log('  npx tsx scripts/promote-content.ts --exclude siteSettings --force')
  process.exit(0)
}

// Run the promotion
promoteContent()