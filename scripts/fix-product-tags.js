#!/usr/bin/env node

import 'dotenv/config'
import sanityClient from '@sanity/client'

// Configuration
const projectId = process.env.NEXT_PUBLIC_SANITY_PROJECT_ID
const dataset = process.env.NEXT_PUBLIC_SANITY_DATASET || 'production'
const apiVersion = '2024-01-11'
const token = process.env.SANITY_API_TOKEN

if (!projectId) {
  console.error('❌ Missing NEXT_PUBLIC_SANITY_PROJECT_ID environment variable')
  process.exit(1)
}

if (!token) {
  console.error('❌ Missing SANITY_API_TOKEN environment variable')
  console.error('Please add a token with write permissions to your .env file')
  process.exit(1)
}

// Initialize Sanity client with write permissions
const client = sanityClient({
  projectId,
  dataset,
  apiVersion,
  token,
  useCdn: false
})

console.log(`🔧 Fixing product tags...`)
console.log(`📦 Dataset: ${dataset}`)
console.log('')

async function fixProductTags() {
  try {
    // Get the specific product with SS tag
    const product = await client.fetch(`
      *[_type == "product" && _id == "02be94ee-e490-4a48-93c3-bf9d253762bd"][0] {
        _id,
        name,
        tags,
        stockStatus,
        publishedAt
      }
    `)

    if (!product) {
      console.error('❌ Product not found!')
      return
    }

    console.log(`📦 Found product: ${product.name?.zh || product.name?.en}`)
    console.log(`   Current tags: ${product.tags?.join(', ') || '(none)'}`)

    // Determine appropriate tags
    const newTags = []
    
    // Add "popular" tag (as it's a Mario product)
    newTags.push('popular')
    
    // If it's a pre-order product, add those tags
    if (product.stockStatus === 'pre-order') {
      newTags.push('preorder')
      newTags.push('limited')
    }
    
    // Check if it's a new product (within 30 days)
    if (product.publishedAt) {
      const publishDate = new Date(product.publishedAt)
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
      
      if (publishDate > thirtyDaysAgo) {
        newTags.push('new')
      }
    }

    console.log(`   New tags: ${newTags.join(', ')}`)
    console.log('')

    // Update the product
    console.log('🔄 Updating product...')
    
    await client
      .patch(product._id)
      .set({ tags: newTags })
      .commit()

    console.log('✅ Product tags updated successfully!')
    
    // Also run the general migration for other products
    console.log('\n🔄 Running general migration for other products...\n')
    
    const allProducts = await client.fetch(`
      *[_type == "product" && isPublished == true && (count(tags) == 0 || "SS" in tags)] {
        _id,
        name,
        tags,
        publishedAt,
        stockStatus,
        category->{
          _id,
          name,
          "productCount": count(*[_type == "product" && category._ref == ^._id && isPublished == true])
        }
      }
    `)

    console.log(`Found ${allProducts.length} products that need tag updates`)

    for (const prod of allProducts) {
      const productName = prod.name?.zh || prod.name?.en || 'Unnamed Product'
      const currentTags = prod.tags || []
      const updatedTags = []

      // Skip if already has valid tags
      if (currentTags.some(tag => ['new', 'popular', 'limited', 'preorder', 'exclusive', 'sale'].includes(tag))) {
        continue
      }

      // Add appropriate tags based on criteria
      if (prod.publishedAt) {
        const publishDate = new Date(prod.publishedAt)
        const thirtyDaysAgo = new Date()
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
        
        if (publishDate > thirtyDaysAgo) {
          updatedTags.push('new')
        }
      }

      if (prod.stockStatus === 'pre-order') {
        updatedTags.push('preorder')
        updatedTags.push('limited')
      }

      if (prod.category?.productCount >= 5) {
        updatedTags.push('popular')
      }

      if (updatedTags.length > 0) {
        console.log(`\n📦 Updating: ${productName}`)
        console.log(`   Adding tags: ${updatedTags.join(', ')}`)
        
        await client
          .patch(prod._id)
          .set({ tags: updatedTags })
          .commit()
      }
    }

    console.log('\n✨ All products updated successfully!')

  } catch (error) {
    console.error('❌ Error:', error)
  }
}

// Run the fix
fixProductTags()