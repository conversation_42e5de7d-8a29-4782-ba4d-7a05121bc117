#!/usr/bin/env node

/**
 * Content Promotion Script
 * Promotes content from staging dataset to production dataset
 * 
 * Usage: node scripts/promote-to-production.js [options]
 * Options:
 *   --dry-run    Preview changes without applying them
 *   --force      Skip confirmation prompts
 */

const { createClient } = require('@sanity/client')
const readline = require('readline')
const { exec } = require('child_process')
const { promisify } = require('util')
const execAsync = promisify(exec)

// Configuration
const config = {
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || '4za4x22i',
  token: process.env.SANITY_API_TOKEN,
  apiVersion: '2024-01-01',
  useCdn: false
}

// Validate environment
if (!config.token) {
  console.error('❌ Error: SANITY_API_TOKEN is required for content promotion')
  console.error('Please set it in your .env file')
  process.exit(1)
}

// Parse command line arguments
const args = process.argv.slice(2)
const isDryRun = args.includes('--dry-run')
const isForce = args.includes('--force')

// Create clients for both datasets
const stagingClient = createClient({
  ...config,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET_STAGING || 'staging'
})

const productionClient = createClient({
  ...config,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production'
})

// Console styling
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

// Helper to ask for user confirmation
async function askConfirmation(question) {
  if (isForce) return true
  
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  })
  
  return new Promise((resolve) => {
    rl.question(`${question} (y/N): `, (answer) => {
      rl.close()
      resolve(answer.toLowerCase() === 'y')
    })
  })
}

// Get document types to sync
const documentTypesToSync = [
  'homepage',
  'aboutPage',
  'contactPage',
  'siteSettings',
  'navigation',
  'product',
  'category',
  'ipSeries'
]

// Main promotion function
async function promoteContent() {
  try {
    log('\n🚀 Content Promotion Tool', 'blue')
    log('=========================\n')
    
    log(`Source Dataset: ${stagingClient.config().dataset}`, 'yellow')
    log(`Target Dataset: ${productionClient.config().dataset}`, 'yellow')
    
    if (isDryRun) {
      log('\n📋 DRY RUN MODE - No changes will be made', 'yellow')
    }
    
    // Step 1: Create backup of production
    if (!isDryRun) {
      log('\n📦 Creating production backup...', 'blue')
      const backupName = `production-backup-${new Date().toISOString().split('T')[0]}`
      
      try {
        const exportCommand = `sanity dataset export production ${backupName}.tar.gz --project ${config.projectId}`
        await execAsync(exportCommand)
        log(`✅ Backup created: ${backupName}.tar.gz`, 'green')
      } catch (error) {
        log('❌ Failed to create backup', 'red')
        console.error(error)
        return
      }
    }
    
    // Step 2: Fetch documents from staging
    log('\n📊 Analyzing content differences...', 'blue')
    
    const changes = {
      new: [],
      updated: [],
      unchanged: []
    }
    
    for (const docType of documentTypesToSync) {
      const query = `*[_type == "${docType}"]`
      
      const stagingDocs = await stagingClient.fetch(query)
      const productionDocs = await productionClient.fetch(query)
      
      // Create a map of production documents by ID
      const prodDocsMap = new Map(
        productionDocs.map(doc => [doc._id, doc])
      )
      
      // Compare documents
      for (const stagingDoc of stagingDocs) {
        const prodDoc = prodDocsMap.get(stagingDoc._id)
        
        if (!prodDoc) {
          changes.new.push({
            type: docType,
            id: stagingDoc._id,
            doc: stagingDoc
          })
        } else if (JSON.stringify(stagingDoc) !== JSON.stringify(prodDoc)) {
          changes.updated.push({
            type: docType,
            id: stagingDoc._id,
            doc: stagingDoc
          })
        } else {
          changes.unchanged.push({
            type: docType,
            id: stagingDoc._id
          })
        }
      }
    }
    
    // Display changes summary
    log('\n📋 Changes Summary:', 'blue')
    log(`  New documents: ${changes.new.length}`, changes.new.length > 0 ? 'green' : 'reset')
    log(`  Updated documents: ${changes.updated.length}`, changes.updated.length > 0 ? 'yellow' : 'reset')
    log(`  Unchanged documents: ${changes.unchanged.length}`)
    
    if (changes.new.length === 0 && changes.updated.length === 0) {
      log('\n✅ No changes to promote!', 'green')
      return
    }
    
    // Show detailed changes
    if (changes.new.length > 0) {
      log('\n🆕 New Documents:', 'green')
      changes.new.forEach(item => {
        log(`  - ${item.type}: ${item.id}`)
      })
    }
    
    if (changes.updated.length > 0) {
      log('\n📝 Updated Documents:', 'yellow')
      changes.updated.forEach(item => {
        log(`  - ${item.type}: ${item.id}`)
      })
    }
    
    // Ask for confirmation
    if (!isDryRun) {
      const confirmed = await askConfirmation(
        '\n⚠️  Are you sure you want to promote these changes to production?'
      )
      
      if (!confirmed) {
        log('\n❌ Promotion cancelled', 'red')
        return
      }
      
      // Step 3: Apply changes
      log('\n🔄 Applying changes to production...', 'blue')
      
      let successCount = 0
      let errorCount = 0
      
      // Process new documents
      for (const item of changes.new) {
        try {
          await productionClient.create(item.doc)
          successCount++
          log(`  ✅ Created: ${item.type} - ${item.id}`, 'green')
        } catch (error) {
          errorCount++
          log(`  ❌ Failed to create: ${item.type} - ${item.id}`, 'red')
          console.error(error.message)
        }
      }
      
      // Process updated documents
      for (const item of changes.updated) {
        try {
          // Remove revision to avoid conflicts
          const { _rev, ...docWithoutRev } = item.doc
          await productionClient
            .patch(item.id)
            .set(docWithoutRev)
            .commit()
          successCount++
          log(`  ✅ Updated: ${item.type} - ${item.id}`, 'green')
        } catch (error) {
          errorCount++
          log(`  ❌ Failed to update: ${item.type} - ${item.id}`, 'red')
          console.error(error.message)
        }
      }
      
      // Final summary
      log('\n📊 Promotion Complete!', 'blue')
      log(`  Success: ${successCount}`, 'green')
      log(`  Errors: ${errorCount}`, errorCount > 0 ? 'red' : 'reset')
      
      if (errorCount > 0) {
        log('\n⚠️  Some documents failed to promote. Please check the errors above.', 'yellow')
      } else {
        log('\n✅ All changes successfully promoted to production!', 'green')
      }
    }
    
  } catch (error) {
    log('\n❌ Promotion failed with error:', 'red')
    console.error(error)
    process.exit(1)
  }
}

// Run the promotion
promoteContent()