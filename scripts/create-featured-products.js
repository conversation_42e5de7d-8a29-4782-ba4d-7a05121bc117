require('dotenv').config({ path: '../.env' });
const { createClient } = require('@sanity/client');

// Create client
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || '4za4x22i',
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
  apiVersion: '2023-05-03',
  token: process.env.SANITY_API_TOKEN,
  useCdn: false,
});

async function createFeaturedProducts() {
  try {
    // Check if featured products already exists
    const existing = await client.fetch(`*[_type == "featuredProducts"][0]`);
    
    if (existing) {
      console.log('✅ Featured products configuration already exists:', existing._id);
      console.log('Title:', existing.title);
      console.log('Products count:', existing.products?.length || 0);
      console.log('Is active:', existing.isActive);
      return;
    }

    // Get some products to feature
    const products = await client.fetch(`
      *[_type == "product" && isPublished == true] | order(_createdAt desc)[0...3] {
        _id,
        name
      }
    `);
    
    if (products.length < 3) {
      console.error('❌ Not enough published products. Found:', products.length);
      console.log('Please create at least 3 published products first.');
      return;
    }

    console.log('Found products to feature:');
    products.forEach((p, i) => {
      console.log(`${i + 1}. ${p.name.zh || p.name.en} (${p._id})`);
    });

    // Create featured products document
    const featuredProductsDoc = {
      _type: 'featuredProducts',
      title: {
        _type: 'localeString',
        zh: '精选产品',
        en: 'Featured Products',
        ar: 'المنتجات المميزة'
      },
      subtitle: {
        _type: 'localeText',
        zh: '探索我们精心挑选的热门动漫周边产品，每一件都承载着无数动漫爱好者的梦想',
        en: 'Explore our carefully selected popular anime merchandise, each carrying the dreams of countless anime fans',
        ar: 'استكشف منتجاتنا المختارة بعناية من البضائع الشعبية للأنمي، كل منها يحمل أحلام عشاق الأنمي'
      },
      products: products.map(p => ({
        _type: 'reference',
        _ref: p._id,
        _key: p._id
      })),
      displaySettings: {
        _type: 'object',
        showPrices: true,
        showRatings: true,
        showCategories: true,
        showBadges: true
      },
      layout: {
        _type: 'object',
        columnsDesktop: 3,
        columnsTablet: 2,
        columnsMobile: 1
      },
      isActive: true
    };

    console.log('\nCreating featured products configuration...');
    const result = await client.create(featuredProductsDoc);
    
    console.log('✅ Featured products configuration created successfully!');
    console.log('Document ID:', result._id);
    console.log('\nYou can now manage it at: http://localhost:3333/desk/featured-products');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    if (error.message.includes('token')) {
      console.log('\n💡 Tip: Make sure you have set SANITY_API_TOKEN in your .env file');
      console.log('You can create a token at: https://www.sanity.io/manage/project/4za4x22i/settings/api');
    }
  }
}

createFeaturedProducts();