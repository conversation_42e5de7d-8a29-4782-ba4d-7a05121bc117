#!/usr/bin/env node

/**
 * Sanity 数据集验证脚本
 * 用于验证 production 和 staging 数据集的差异
 */

const { createClient } = require('@sanity/client')
require('dotenv').config({ path: '.env.local' })

const projectId = process.env.NEXT_PUBLIC_SANITY_PROJECT_ID
const token = process.env.SANITY_API_TOKEN

if (!projectId || !token) {
  console.error('❌ 缺少必要的环境变量：')
  console.error('  - NEXT_PUBLIC_SANITY_PROJECT_ID')
  console.error('  - SANITY_API_TOKEN')
  process.exit(1)
}

// 创建客户端
const productionClient = createClient({
  projectId,
  dataset: 'production',
  token,
  useCdn: false,
  apiVersion: '2023-01-01'
})

const stagingClient = createClient({
  projectId,
  dataset: 'staging',
  token,
  useCdn: false,
  apiVersion: '2023-01-01'
})

// 文档类型列表
const documentTypes = [
  'homepage',
  'aboutPage', 
  'contactPage',
  'siteSettings',
  'navigation',
  'product',
  'category',
  'ipSeries'
]

async function getDocumentCounts(client, dataset) {
  console.log(`\n📊 正在分析 ${dataset} 数据集...`)
  
  const results = {}
  
  for (const type of documentTypes) {
    try {
      const query = `count(*[_type == "${type}"])`
      const count = await client.fetch(query)
      results[type] = count
      
      // 获取最新更新时间
      const latestQuery = `*[_type == "${type}"] | order(_updatedAt desc)[0]._updatedAt`
      const latestUpdate = await client.fetch(latestQuery)
      
      console.log(`  ${type.padEnd(15)} : ${count.toString().padStart(3)} 个文档 ${latestUpdate ? `(最新更新: ${new Date(latestUpdate).toLocaleString('zh-CN')})` : ''}`)
    } catch (error) {
      console.log(`  ${type.padEnd(15)} : ❌ 查询失败 - ${error.message}`)
      results[type] = 0
    }
  }
  
  return results
}

async function getDatasetInfo(client, dataset) {
  try {
    const query = `{
      "totalDocuments": count(*[]),
      "documentTypes": array::unique(*[]._type),
      "lastUpdated": *[_updatedAt != null] | order(_updatedAt desc)[0]._updatedAt
    }`
    
    const info = await client.fetch(query)
    return info
  } catch (error) {
    console.error(`❌ 获取 ${dataset} 数据集信息失败:`, error.message)
    return null
  }
}

async function compareSingletonDocuments(prodClient, stagingClient) {
  console.log('\n🔍 比较单例文档差异...')
  
  const singletonTypes = ['homepage', 'aboutPage', 'contactPage', 'siteSettings']
  
  for (const type of singletonTypes) {
    try {
      const prodDoc = await prodClient.fetch(`*[_type == "${type}"][0]`)
      const stagingDoc = await stagingClient.fetch(`*[_type == "${type}"][0]`)
      
      console.log(`\n📄 ${type}:`)
      
      if (!prodDoc && !stagingDoc) {
        console.log('  ⚪ 两个环境都没有此文档')
        continue
      }
      
      if (!prodDoc) {
        console.log('  🔴 生产环境缺少此文档')
        console.log(`  🟢 预发布环境最后更新: ${new Date(stagingDoc._updatedAt).toLocaleString('zh-CN')}`)
        continue
      }
      
      if (!stagingDoc) {
        console.log(`  🟢 生产环境最后更新: ${new Date(prodDoc._updatedAt).toLocaleString('zh-CN')}`)
        console.log('  🔴 预发布环境缺少此文档')
        continue
      }
      
      const prodUpdated = new Date(prodDoc._updatedAt)
      const stagingUpdated = new Date(stagingDoc._updatedAt)
      
      console.log(`  🟢 生产环境最后更新: ${prodUpdated.toLocaleString('zh-CN')}`)
      console.log(`  🟡 预发布环境最后更新: ${stagingUpdated.toLocaleString('zh-CN')}`)
      
      if (Math.abs(prodUpdated - stagingUpdated) > 60000) { // 差异超过1分钟
        const newer = prodUpdated > stagingUpdated ? '生产环境' : '预发布环境'
        console.log(`  ⚠️  ${newer} 的版本更新`)
      } else {
        console.log('  ✅ 两个环境的更新时间相近')
      }
      
    } catch (error) {
      console.log(`  ❌ 比较 ${type} 时出错: ${error.message}`)
    }
  }
}

async function main() {
  console.log('🔍 Sanity 数据集验证工具')
  console.log('=' .repeat(50))
  console.log(`📦 项目 ID: ${projectId}`)
  
  try {
    // 获取数据集基本信息
    const [prodInfo, stagingInfo] = await Promise.all([
      getDatasetInfo(productionClient, 'production'),
      getDatasetInfo(stagingClient, 'staging')
    ])
    
    // 显示数据集概览
    console.log('\n📈 数据集概览:')
    if (prodInfo) {
      console.log(`  🟢 Production - 总文档数: ${prodInfo.totalDocuments}, 文档类型: ${prodInfo.documentTypes.length}`)
      if (prodInfo.lastUpdated) {
        console.log(`      最后更新: ${new Date(prodInfo.lastUpdated).toLocaleString('zh-CN')}`)
      }
    }
    
    if (stagingInfo) {
      console.log(`  🟡 Staging - 总文档数: ${stagingInfo.totalDocuments}, 文档类型: ${stagingInfo.documentTypes.length}`)
      if (stagingInfo.lastUpdated) {
        console.log(`      最后更新: ${new Date(stagingInfo.lastUpdated).toLocaleString('zh-CN')}`)
      }
    }
    
    // 获取详细文档计数
    const [prodCounts, stagingCounts] = await Promise.all([
      getDocumentCounts(productionClient, 'production'),
      getDocumentCounts(stagingClient, 'staging')
    ])
    
    // 比较数据集
    console.log('\n📊 数据集比较:')
    console.log('-'.repeat(50))
    console.log('文档类型'.padEnd(15) + '生产环境'.padEnd(10) + '预发布环境'.padEnd(10) + '差异')
    console.log('-'.repeat(50))
    
    for (const type of documentTypes) {
      const prodCount = prodCounts[type] || 0
      const stagingCount = stagingCounts[type] || 0
      const diff = stagingCount - prodCount
      
      let diffStr = ''
      if (diff > 0) {
        diffStr = `+${diff} 🟡`
      } else if (diff < 0) {
        diffStr = `${diff} 🔴`
      } else {
        diffStr = '✅'
      }
      
      console.log(
        type.padEnd(15) + 
        prodCount.toString().padEnd(10) + 
        stagingCount.toString().padEnd(10) + 
        diffStr
      )
    }
    
    // 比较单例文档
    await compareSingletonDocuments(productionClient, stagingClient)
    
    // 验证结果总结
    console.log('\n🎯 验证结果总结:')
    console.log('=' .repeat(50))
    
    if (prodInfo && stagingInfo) {
      if (prodInfo.totalDocuments === stagingInfo.totalDocuments) {
        console.log('✅ 两个数据集的文档总数相同')
      } else {
        console.log(`⚠️  文档总数不同 (生产环境: ${prodInfo.totalDocuments}, 预发布环境: ${stagingInfo.totalDocuments})`)
      }
      
      const prodTypes = new Set(prodInfo.documentTypes)
      const stagingTypes = new Set(stagingInfo.documentTypes)
      
      const onlyInProd = [...prodTypes].filter(type => !stagingTypes.has(type))
      const onlyInStaging = [...stagingTypes].filter(type => !prodTypes.has(type))
      
      if (onlyInProd.length > 0) {
        console.log(`🟢 仅在生产环境中的文档类型: ${onlyInProd.join(', ')}`)
      }
      
      if (onlyInStaging.length > 0) {
        console.log(`🟡 仅在预发布环境中的文档类型: ${onlyInStaging.join(', ')}`)
      }
      
      if (onlyInProd.length === 0 && onlyInStaging.length === 0) {
        console.log('✅ 两个数据集包含相同的文档类型')
      }
    }
    
    console.log('\n🔗 Studio 访问链接:')
    console.log(`  🟢 生产环境: https://myngapop.sanity.studio/`)
    console.log(`  🟡 预发布环境: https://myngapop.sanity.studio/?dataset=staging`)
    
  } catch (error) {
    console.error('❌ 验证过程中发生错误:', error.message)
    process.exit(1)
  }
}

main().catch(console.error)