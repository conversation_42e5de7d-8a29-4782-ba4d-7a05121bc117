#!/usr/bin/env node

import 'dotenv/config'
import sanityClient from '@sanity/client'

// Configuration
const projectId = process.env.NEXT_PUBLIC_SANITY_PROJECT_ID
const dataset = process.env.NEXT_PUBLIC_SANITY_DATASET || 'production'
const apiVersion = '2024-01-11'
const token = process.env.SANITY_API_TOKEN

if (!projectId) {
  console.error('❌ Missing NEXT_PUBLIC_SANITY_PROJECT_ID environment variable')
  process.exit(1)
}

if (!token) {
  console.error('❌ Missing SANITY_API_TOKEN environment variable')
  console.error('Please add a token with write permissions to your .env file')
  process.exit(1)
}

// Initialize Sanity client with write permissions
const client = sanityClient({
  projectId,
  dataset,
  apiVersion,
  token,
  useCdn: false
})

// Check if dry run mode
const isDryRun = process.argv.includes('--dry-run')

console.log(`🚀 ${isDryRun ? 'DRY RUN:' : ''} Starting product tags migration...`)
console.log(`📦 Dataset: ${dataset}`)
console.log('')

async function migrateProductTags() {
  try {
    // 1. Get all published products
    const products = await client.fetch(`
      *[_type == "product" && isPublished == true] {
        _id,
        name,
        publishedAt,
        stockStatus,
        tags,
        category->{
          _id,
          name,
          "productCount": count(*[_type == "product" && category._ref == ^._id && isPublished == true])
        }
      }
    `)

    console.log(`📋 Found ${products.length} published products`)
    console.log('')

    // Track updates
    let updatedCount = 0
    const updates = []

    // Process each product
    for (const product of products) {
      const productName = product.name?.zh || product.name?.en || 'Unnamed Product'
      const currentTags = product.tags || []
      const newTags = [...currentTags]
      let hasChanges = false

      // Rule 1: Add "new" tag for products published within last 30 days
      if (product.publishedAt) {
        const publishDate = new Date(product.publishedAt)
        const thirtyDaysAgo = new Date()
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
        
        if (publishDate > thirtyDaysAgo && !currentTags.includes('new')) {
          newTags.push('new')
          hasChanges = true
        }
      }

      // Rule 2: Add "preorder" and "limited" tags for pre-order products
      if (product.stockStatus === 'pre-order') {
        if (!currentTags.includes('preorder')) {
          newTags.push('preorder')
          hasChanges = true
        }
        if (!currentTags.includes('limited')) {
          newTags.push('limited')
          hasChanges = true
        }
      }

      // Rule 3: Add "popular" tag for products in categories with 5+ products
      if (product.category?.productCount >= 5 && !currentTags.includes('popular')) {
        newTags.push('popular')
        hasChanges = true
      }

      // If there are changes, prepare update
      if (hasChanges) {
        updates.push({
          product,
          productName,
          currentTags,
          newTags,
          addedTags: newTags.filter(tag => !currentTags.includes(tag))
        })
      }
    }

    // Display what will be updated
    if (updates.length === 0) {
      console.log('✅ No products need tag updates!')
      return
    }

    console.log(`📝 ${updates.length} products will be updated:\n`)
    
    for (const update of updates) {
      console.log(`  📦 ${update.productName}`)
      console.log(`     Current tags: ${update.currentTags.length > 0 ? update.currentTags.join(', ') : '(none)'}`)
      console.log(`     Adding tags: ${update.addedTags.join(', ')}`)
      console.log(`     New tags: ${update.newTags.join(', ')}`)
      console.log('')
    }

    // If dry run, stop here
    if (isDryRun) {
      console.log('🔍 DRY RUN COMPLETE - No changes were made')
      console.log('Run without --dry-run to apply these changes')
      return
    }

    // Confirm before proceeding
    console.log('⚠️  This will update the tags for the products listed above.')
    console.log('Press Ctrl+C to cancel, or wait 5 seconds to continue...')
    
    await new Promise(resolve => setTimeout(resolve, 5000))

    // Apply updates
    console.log('\n🔄 Applying updates...\n')

    for (const update of updates) {
      try {
        await client
          .patch(update.product._id)
          .set({ tags: update.newTags })
          .commit()

        updatedCount++
        console.log(`✅ Updated: ${update.productName}`)
      } catch (error) {
        console.error(`❌ Failed to update ${update.productName}:`, error.message)
      }
    }

    console.log(`\n✨ Migration complete! Updated ${updatedCount} products.`)

  } catch (error) {
    console.error('❌ Migration failed:', error)
    process.exit(1)
  }
}

// Run migration
migrateProductTags()