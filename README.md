# MyNgaPop - 跨境动漫周边品牌官网

<div align="center">
  <img src="https://img.shields.io/badge/Next.js-15.3.5-black?style=for-the-badge&logo=next.js" alt="Next.js" />
  <img src="https://img.shields.io/badge/TypeScript-5.0+-blue?style=for-the-badge&logo=typescript" alt="TypeScript" />
  <img src="https://img.shields.io/badge/Tailwind_CSS-3.4+-38B2AC?style=for-the-badge&logo=tailwind-css" alt="Tailwind CSS" />
  <img src="https://img.shields.io/badge/Sanity-3.98+-red?style=for-the-badge&logo=sanity" alt="Sanity" />
</div>

## 项目概述

MyNgaPop 是一个专业的跨境动漫周边品牌官网，基于 Next.js 14+ 和 Sanity CMS 构建。项目支持多语言（中文、英文、阿拉伯语）、静态生成 + ISR（增量静态再生）、全球 CDN 分发等特性。

### 核心特性

- 🌍 **多语言支持**: 中文、英文、阿拉伯语三种语言
- 🚀 **高性能**: 静态生成 + ISR，CDN 分发
- 📝 **内容管理**: Sanity CMS 可视化内容管理
- 📱 **响应式设计**: 移动端优先的响应式布局
- 🎨 **动画效果**: Framer Motion 动画库
- 🔍 **SEO 优化**: 完整的 SEO 配置
- 🛡️ **类型安全**: TypeScript 全覆盖

### 技术栈

- **前端框架**: Next.js 15.3.5 (App Router)
- **样式方案**: Tailwind CSS 3.4+
- **动画库**: Framer Motion 11.11+
- **CMS**: Sanity v3.98+
- **多语言**: next-intl 3.22+
- **部署平台**: Vercel
- **包管理**: pnpm

## 快速开始

### 环境要求

- Node.js >= 18.17.0
- pnpm >= 8.0.0 (推荐)

### 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd MyNgaPop

# 安装依赖
pnpm install

# 安装 Sanity Studio 依赖
cd sanity && pnpm install && cd ..
```

### 环境变量配置

复制 `.env.example` 为 `.env.local` 并配置以下变量：

```bash
# Sanity 配置
NEXT_PUBLIC_SANITY_PROJECT_ID=your-project-id
NEXT_PUBLIC_SANITY_DATASET=production
SANITY_API_TOKEN=your-read-token
SANITY_WEBHOOK_SECRET=your-webhook-secret

# Next.js 配置
NEXT_PUBLIC_BASE_URL=http://localhost:3000
```

### 启动开发服务器

```bash
# 启动 Next.js 开发服务器
pnpm dev

# 新终端：启动 Sanity Studio
cd sanity && pnpm dev
```

### 访问应用

- **前端应用**: http://localhost:3000
- **Sanity Studio**: http://localhost:3333

## 项目结构

```
MyNgaPop/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── [locale]/          # 多语言动态路由
│   │   │   ├── layout.tsx     # 多语言布局
│   │   │   ├── page.tsx       # 首页
│   │   │   └── products/      # 产品页面
│   │   ├── api/               # API 路由
│   │   │   └── revalidate/    # ISR 重验证端点
│   │   ├── globals.css        # 全局样式
│   │   └── i18n.ts           # 国际化配置
│   ├── components/            # 组件目录
│   │   ├── ui/               # 基础 UI 组件
│   │   ├── product/          # 产品相关组件
│   │   └── layout/           # 布局组件
│   ├── lib/                  # 工具库
│   │   ├── sanity/          # Sanity 相关
│   │   └── utils/           # 工具函数
│   ├── messages/            # 多语言翻译文件
│   ├── types/               # TypeScript 类型定义
│   └── middleware.ts        # 中间件
├── sanity/                  # Sanity Studio 项目
│   ├── schemas/            # 内容模型
│   └── sanity.config.ts    # Sanity 配置
├── docs/                   # 项目文档
└── public/                 # 静态资源
```

## 开发指南

### 代码规范

```bash
# 类型检查
pnpm type-check

# 代码检查
pnpm lint

# 代码格式化
pnpm format
```

### 构建与部署

```bash
# 构建生产版本
pnpm build

# 预览生产构建
pnpm start

# 构建 Sanity Studio
cd sanity && pnpm build
```

### 多语言开发

1. 在 `src/messages/` 目录下添加翻译文件
2. 使用 `useTranslations` Hook 获取翻译
3. 服务端组件使用 `getTranslations` 函数

```typescript
// 客户端组件
import { useTranslations } from 'next-intl'

export default function Component() {
  const t = useTranslations('Navigation')
  return <h1>{t('home')}</h1>
}

// 服务端组件
import { getTranslations } from 'next-intl/server'

export default async function Page() {
  const t = await getTranslations('Navigation')
  return <h1>{t('home')}</h1>
}
```

## 内容管理

### Sanity Studio 操作

1. **访问 CMS**: http://localhost:3333
2. **产品管理**: 创建、编辑、删除产品
3. **多语言内容**: 每个字段支持多语言版本
4. **媒体管理**: 上传和管理图片资源
5. **发布流程**: 内容编辑 → 保存草稿 → 发布更新

### 内容模型

- **产品 (Product)**: 产品信息、图片、分类、价格等
- **分类 (Category)**: 产品分类管理
- **IP 系列 (IP Series)**: 动漫 IP 系列管理
- **SEO**: 搜索引擎优化配置

### ISR 自动更新

发布内容后，网站会通过 Webhook 自动触发 ISR 更新：

- 产品更新 → 自动重新生成相关页面
- 分类更新 → 自动重新生成产品列表
- 支持多语言页面同步更新

## 部署指南

### Vercel 部署

1. 连接 GitHub 仓库到 Vercel
2. 配置环境变量
3. 自动部署设置

### 环境变量配置

在 Vercel Dashboard 中配置：

```bash
NEXT_PUBLIC_SANITY_PROJECT_ID=your-project-id
NEXT_PUBLIC_SANITY_DATASET=production
SANITY_API_TOKEN=your-read-token
SANITY_WEBHOOK_SECRET=your-webhook-secret
```

### Webhook 配置

在 Sanity 管理面板配置 Webhook：

1. 进入 manage.sanity.io
2. 选择项目 → API → Webhooks
3. 创建新 Webhook:
   - URL: `https://your-domain.vercel.app/api/revalidate`
   - Trigger: Create, Update, Delete
   - Filter: `_type in ["product", "category"]`

## 故障排除

### 常见问题

#### 1. 开发服务器启动失败

**解决方案**:
```bash
# 检查 Node.js 版本
node --version  # 需要 >= 18.17.0

# 清理缓存
pnpm store prune
rm -rf node_modules .next
pnpm install
```

#### 2. Sanity Studio 无法访问

**解决方案**:
```bash
# 检查 Sanity 配置
cd sanity
pnpm install
pnpm dev
```

#### 3. 多语言路由不工作

**解决方案**:
- 检查 `middleware.ts` 配置
- 确认 `src/messages/` 目录下有对应语言文件
- 重启开发服务器

#### 4. 图片加载失败

**解决方案**:
- 检查 `next.config.mjs` 中的 `images.domains` 配置
- 确认 Sanity 项目 ID 正确

#### 5. 构建失败

**解决方案**:
```bash
# 类型检查
pnpm type-check

# 修复 ESLint 问题
pnpm lint --fix

# 清理构建缓存
rm -rf .next
pnpm build
```

### 性能优化建议

1. **图片优化**: 使用 Next.js Image 组件和 WebP 格式
2. **代码分割**: 使用动态导入减少首屏加载时间
3. **缓存策略**: 配置合适的 ISR 重验证时间
4. **CDN 配置**: 使用 Vercel 全球 CDN 分发

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送分支 (`git push origin feature/amazing-feature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证。详情请参阅 [LICENSE](LICENSE) 文件。

## 联系方式

- 项目维护者: [您的姓名]
- 邮箱: [您的邮箱]
- 项目地址: [GitHub 仓库地址]

---

如需更多帮助，请查看 [项目文档](docs/) 或提交 Issue。